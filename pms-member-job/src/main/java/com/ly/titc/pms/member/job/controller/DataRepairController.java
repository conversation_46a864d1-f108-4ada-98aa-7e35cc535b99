package com.ly.titc.pms.member.job.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.MD5Util;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.job.entity.request.BatchDeleteEsDataRequest;
import com.ly.titc.pms.member.job.entity.request.ExecuteSqlRequest;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.PageMemberParamDto;
import com.ly.titc.pms.member.mediator.handler.es.ElasticsearchHandler;
import com.ly.titc.pms.member.mediator.service.BackDoorMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @title: DataRepairController
 * @projectName pms-member
 * @description: 数据修复controller
 * @date 2023/10/30 11:16
 */
@Slf4j
@RestController
@RequestMapping("/data/repair/")
public class DataRepairController {

    @Resource
    private BackDoorMedService backDoorMedService;

    @Resource
    private ElasticsearchHandler elasticsearchHandler;

    @Resource
    private MemberMedService memberMedService;
    /**
     * 请勿使用
     *
     * @param request
     * @return
     */
    @PostMapping("/executeSql")
    public Response<String> executeSql(@RequestBody @Valid ExecuteSqlRequest request) {
        // 密码校验
        checkPwd(request.getPwd());

        // sql格式不正确
        String sql = request.getSql();
        if ("insert".equalsIgnoreCase(request.getType())) {
            backDoorMedService.doInsertSomething(sql);
        } else {
            if (!sql.contains(" where  ")) {
                throw new ServiceException("参数非法2", RespCodeEnum.CODE_400.getCode());
            }
            if ("update".equalsIgnoreCase(request.getType())) {
                backDoorMedService.doUpdateSomething(sql);
            } else if ("delete".equalsIgnoreCase(request.getType())) {
                backDoorMedService.doDeleteSomething(sql);
            } else {
                throw new ServiceException(RespCodeEnum.CODE_400);
            }

        }
        return Response.success(null);
    }

    /**
     * 删除ES数据
     *
     * @return
     */
    @PostMapping("/delEsData")
    public Response<String> delEsData(@RequestBody @Valid  BatchDeleteEsDataRequest request) {
        checkPwd(request.getPwd());

        if (CollectionUtils.isEmpty(request.getMemberNos())) {
            PageMemberParamDto paramDto = new PageMemberParamDto();
            paramDto.setMasterType(MasterTypeEnum.BLOC.getType());
            paramDto.setMasterCode(request.getBlocCode());
            paramDto.setPageIndex(1);
            paramDto.setPageSize(1000);
            Pageable<MemberDetailDto> memberDetailDtoPageable = memberMedService.pageMemberByFinalMode(paramDto);
            for (MemberDetailDto data : memberDetailDtoPageable.getDatas()) {
                elasticsearchHandler.delete(data.getMemberNo());
            }
        } else {
            List<String> memberNos = request.getMemberNos();
            for (String memberNo : memberNos) {
                elasticsearchHandler.delete(memberNo);
            }
        }
        return Response.success(null);
    }

    private static void checkPwd(String token) {
        String pwd = "";
        try {
            pwd = MD5Util.getMD5(token, Constants.UTF_8);
        } catch (Exception e) {
            throw new ServiceException(RespCodeEnum.CODE_500);
        }
        // 密码不正确
        if (!"35c9c233f4af7f10829240719fdd77b2".equals(pwd)) {
            throw new ServiceException("参数非法1", RespCodeEnum.CODE_400.getCode());
        }
    }

    public static void main(String[] args) {
        String pwd = MD5Util.getMD5("pms-member", Constants.UTF_8);
        System.out.println(pwd);
    }
}
