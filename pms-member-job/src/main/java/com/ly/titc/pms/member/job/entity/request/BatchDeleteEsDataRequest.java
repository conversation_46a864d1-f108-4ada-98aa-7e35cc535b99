package com.ly.titc.pms.member.job.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除ES数据
 *
 * <AUTHOR>
 * @date 2024/12/3 10:13
 */
@Data
public class BatchDeleteEsDataRequest {

    /**
     * 集团编码
     */
    private String blocCode;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String pwd;

    /**
     * 会员号
     */
    private List<String> memberNos;
}
