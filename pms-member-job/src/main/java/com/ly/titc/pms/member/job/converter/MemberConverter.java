package com.ly.titc.pms.member.job.converter;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.convert.BaseConverter;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberCardDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberTagDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberCardInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateMemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/13 14:11
 */
@Mapper(componentModel = "spring")
public interface MemberConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "id", source = "memberNo"),
            @Mapping(target = "registerDate", source = "gmtCreate", qualifiedByName = "localDateTimeToLong"),
    })
    MemberDocumentDto convertDtoToDto(MemberDetailDto memberInfo);

    List<MemberCardDocumentDto> convertDtoToDto(List<MemberCardInfoDto> memberCardInfos);

    @Mappings({
            @Mapping(target = "effectBeginDate", source = "effectBeginDate", qualifiedByName = "effectBeginDate"),
            @Mapping(target = "effectEndDate", source = "effectEndDate", qualifiedByName = "effectEndDateToLong"),
    })
    MemberCardDocumentDto convertDtoToDto(MemberCardInfoDto memberCardInfo);

    MemberTagDocumentDto convertPoToDto(MemberProfileTagInfoDto info);

    @Mappings({
            // 基本信息映射
            @Mapping(target = "memberInfo.realName", source = "realName"),
            @Mapping(target = "memberInfo.enName", source = "enName"),
            @Mapping(target = "memberInfo.nickName", source = "nickName"),
            @Mapping(target = "memberInfo.gender", source = "gender"),
            @Mapping(target = "memberInfo.mobile", source = "mobile"),
            @Mapping(target = "memberInfo.idType", source = "idType"),
            @Mapping(target = "memberInfo.idNo", source = "idNo"),
            @Mapping(target = "memberInfo.birthday", source = "birthday"),

            @Mapping(target = "memberExtendInfo.nationality", source = "nationality"),
            @Mapping(target = "memberExtendInfo.nativePlace", source = "nativePlace"),
            @Mapping(target = "memberExtendInfo.nation", source = "nation"),
            @Mapping(target = "memberExtendInfo.language", source = "language"),
            @Mapping(target = "memberExtendInfo.numberPlate", source = "numberPlate"),
            @Mapping(target = "memberExtendInfo.remark", source = "remark"),


            // 联系信息映射 - 从 memberContactInfo 中获取
            @Mapping(target = "memberContactInfo.email", source = "email"),
            @Mapping(target = "memberContactInfo.wechat", source = "wechat"),
            @Mapping(target = "memberContactInfo.qq", source = "qq"),
            @Mapping(target = "memberContactInfo.countryId", source = "countryId"),
            @Mapping(target = "memberContactInfo.provinceId", source = "provinceId"),
            @Mapping(target = "memberContactInfo.cityId", source = "cityId"),
            @Mapping(target = "memberContactInfo.districtId", source = "districtId"),
            @Mapping(target = "memberContactInfo.address", source = "address"),
            @Mapping(target = "memberContactInfo.postalCode", source = "postalCode")
    })
    UpdateMemberInfoDto convertUpdateMemberInfoDto(CustomerDetailInfoResp customerDetailInfo);

    @Named("localDateTimeToLong")
    default Long localDateTimeToLong(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Timestamp.valueOf(localDateTime).getTime();
    }

    @Named("effectBeginDate")
    default Long effectBeginDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        LocalDate localDate = LocalDateUtil.parseByNormalDate(date);
        return Timestamp.valueOf(localDate.atTime(0, 0)).getTime();
    }

    @Named("effectEndDateToLong")
    default Long effectEndDateToLong(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        LocalDate localDate = LocalDateUtil.parseByNormalDate(date);
        return Timestamp.valueOf(localDate.atTime(23, 59, 59)).getTime();
    }


}
