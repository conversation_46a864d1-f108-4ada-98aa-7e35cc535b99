package com.ly.titc.pms.member.job.handler.topic;

import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTopicHandler;
import com.ly.titc.springboot.mq.manager.TurboMQTagHandlerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: 客人信息变更
 * @author: luyan
 * @create: 2025-07-02 17:04
 **/
@Slf4j
@Component
public class CustomerBpsTopicHandler extends AbstractTurboMQTopicHandler {

    @Override
    public boolean execute(String tag, String messageId, String msg) {
        AbstractTurboMQTagHandler handler = TurboMQTagHandlerManager.getInstance(tag);
        if (Objects.isNull(handler)) {
            log.warn("this tag has not handler to process!!tag:{};messageId:{}", tag, messageId);
            return true;
        }
        try {
            return handler.execute(messageId, msg);
        } catch (Exception e) {
            log.error("titc_pms_member_bps_topic error, tag:{}, messageId:{}, msg:{}", tag, messageId, msg, e);
            return false;
        }
    }

    @Override
    public String getTopic() {
        return TurboMqTopic.PMS_CUSTOMER_BPS_TOPIC;
    }
}
