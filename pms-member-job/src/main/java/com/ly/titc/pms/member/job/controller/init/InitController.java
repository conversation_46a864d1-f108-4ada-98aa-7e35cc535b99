package com.ly.titc.pms.member.job.controller.init;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.job.entity.request.InitRequest;
import com.ly.titc.pms.member.mediator.service.ConfigInitMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author：rui
 * @name：InitController
 * @Date：2024-11-22 13:54
 * @Filename：InitController
 */
@Slf4j
@RestController
@RequestMapping("/member/config/init")
public class InitController {

    @Resource
    private ConfigInitMedService configInitMedService;

    /**
     * 初始化配置
     * @param request
     * @return
     */
    @PostMapping
    public Response init(@RequestBody InitRequest request) {
        configInitMedService.initCardConfig(request.getMasterType(), request.getMasterCode());
        return Response.success();
    }
}
