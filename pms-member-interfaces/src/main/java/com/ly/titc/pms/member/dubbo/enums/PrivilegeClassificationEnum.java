package com.ly.titc.pms.member.dubbo.enums;

/**
 * @Author：rui
 * @name：PrivilegeTypeEnum
 * @Date：2024-11-19 23:22
 * @Filename：PrivilegeTypeEnum
 */
public enum PrivilegeClassificationEnum {

    // 权益类型 1.仅作展示 2 价格折扣 3 预订保留 4 延迟退房

    SHOW(1, "仅作展示"),
    DISCOUNT(2, "价格折扣"),
    RESERVE(3, "预订保留"),
    DELAY(4, "延迟退房");

    private Integer type;

    private String name;

    PrivilegeClassificationEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }
    public static String getNameByType(Integer type) {
        for (PrivilegeClassificationEnum value : PrivilegeClassificationEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }
}
