package com.ly.titc.pms.member.dubbo.entity.request.profile;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 新增会员标签
 *
 * <AUTHOR>
 * @date 2024/10/31 17:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddMemberTagReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 标签分类
     */
    @NotNull(message = "标签分类不能为空")
    private Integer tagType;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    @NotNull(message = "打标分类不能为空")
    private Integer markType;

    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不能为空")
    private Long tagId;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    private String tagName;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;

}
