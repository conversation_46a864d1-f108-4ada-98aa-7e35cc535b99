package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.StateEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新储值规则状态
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class UpdateStoreUsageStateReq extends BlocBaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    /**
     * 状态 0 无效 1 有效
     */
    @NotNull(message = "状态不能为空")
    @LegalEnum(target = StateEnum.class,methodName = "getState")
    private Integer state;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
