package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageStoreUsageRuleReq extends BasePageReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 集团code
     */
    @NotBlank(message = "集团编码不能为空")
    private String blocCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 适用来源
     */
    private List<String> scopeSources;

    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

    /**
     * 适用门店编码
     */
    private List<String> scopeHotelCodes;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

}
