package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/27 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetUpgradeRuleByIdReq extends BaseMasterReq {

    /**
     * 升级规则ID
     */
    @NotNull(message = "升级规则ID不能为空")
    private Long ruleId;
}
