package com.ly.titc.pms.member.dubbo.entity.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: MemberCardInfoDto
 * @projectName pms-member
 * @description: 会员卡信息
 * @date 2023/11/16 15:46
 */
@Data
@Accessors(chain = true)
public class MemberCardInfoResp {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 等级有效期
     */
    private Integer validPeriod;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 最近一次等级变更时间
     */
    private LocalDateTime lastLevelChangeDate;

    /**
     * 发放门店类型 集团:BLOC;门店:HOTEL
     */
    private String issueHotelType;

    /**
     * 发放门店(集团编号; 酒店编号)
     */
    private String issueHotel;

    /**
     * 发放门店
     */
    private String issueHotelName;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;

}
