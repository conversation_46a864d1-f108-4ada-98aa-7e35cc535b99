package com.ly.titc.pms.member.dubbo.entity.request.privilege;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/29 14:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckPrivilegeUsedReq extends BaseMasterReq {

    /**
     * 权益ID
     */
    @NotNull(message = "权益ID")
    private Long privilegeId;
}
