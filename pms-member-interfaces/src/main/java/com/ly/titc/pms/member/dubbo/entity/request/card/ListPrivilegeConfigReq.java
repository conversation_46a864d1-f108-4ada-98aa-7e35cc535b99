package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/28 19:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListPrivilegeConfigReq extends BaseMasterReq {

    /**
     * 卡配置ID
     */
    @NotNull(message = "卡ID不能为空")
    private Long cardId;

    /**
     * 卡等级
     */
    @NotNull(message = "卡等级不能为空")
    private Integer cardLevel;
}
