package com.ly.titc.pms.member.dubbo.entity.request.record;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：PageCheckinRecordRequest
 * @Date：2024-12-11 21:32
 * @Filename：PageCheckinRecordRequest
 */
@Data
public class PageLevelChangeRecordReq extends BasePageReq {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 开始日期
     */
    private String beginTime;

    /**
     * 结束日期
     */
    private String endTime;

}
