package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询会员卡配置
 *
 * <AUTHOR>
 * @date 2025/6/27 10:38
 */
@Getter
@Setter
@Accessors(chain = true)
public class GetCardConfigReq extends BaseMasterReq {

    /**
     * 会员卡ID
     */
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

}
