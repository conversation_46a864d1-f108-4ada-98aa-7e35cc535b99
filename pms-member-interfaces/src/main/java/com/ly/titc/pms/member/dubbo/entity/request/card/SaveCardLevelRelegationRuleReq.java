package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保存会员卡降级规则入参
 *
 * @Author：rui
 * @name：SaveMemberCardLevelRelegationRuleReq
 * @Date：2024-10-29 11:35
 * @Filename：SaveMemberCardLevelRelegationRuleReq
 */
@Data
public class SaveCardLevelRelegationRuleReq extends BaseMasterReq {

    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员保级等级
     */
    private Integer sourceLevel;

    /**
     * 保级成功执行类型：ALL-全部条件;ANY-满足任一个条件
     */
    private String relegationSuccessfulPerformType;

    /**
     * 保级失败规则；1-降至上一等级、2-降至最低等级、3-降至满足等级、4-降至指定等级
     */
    private Integer relegationFailureRule;

    /**
     * 保级失败降至指定等级
     */
    private Integer targetLevel;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序值，越大越靠前
     */
    private Integer sort;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不可为空")
    private String operator;

    /**
     * 保级配置明细
     */
    private List<CardLevelRelegationRuleDetailReq> details;
}
