package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 删除卡配置
 *
 * <AUTHOR>
 * @date 2025/6/27 10:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteCardConfigReq extends BaseMasterReq {

    /**
     * 卡配置ID
     */
    @NotNull(message = "卡ID不能为空")
    private Long cardId;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
