package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.GetByIdReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.config.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRelatedConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberTagConfigDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberTagConfigResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员配置接口
 *
 * <AUTHOR>
 * @date 2025/6/27 17:53
 */
public interface MemberConfigDubboService {

    /**
     * 保存会员相关配置
     *
     * @param request
     * @return
     */
    Response<Long> saveMemberRelatedConfig(@Valid SaveMemberRelatedConfigReq request);

    /**
     * 查询会员相关配置
     *
     * @param request
     * @return
     */
    Response<MemberRelatedConfigResp> getMemberRelatedConfig(@Valid GetMemberRelatedConfigReq request);

    /**
     * 保存标签
     *
     * @return dto
     */
    Response<Long> saveTagConfig(@Valid SaveMemberTagConfigReq request);

    /**
     * 删除标签
     *
     * @param request
     * @return
     */
    Response<String> deleteTagConfig(@Valid DeleteMemberTagConfigReq request);

    /**
     * 分页查询标签
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberTagConfigDetailResp>> pageTagConfig(@Valid PageMemberTagConfigReq request);

    /**
     * 查询标签信息列表
     *
     * @param request
     * @return
     */
    Response<List<MemberTagConfigDetailResp>> listTagConfig(@Valid ListMemberTagConfigReq request);

    /**
     * 查询标签详情
     *
     * @param request
     * @return 详情
     */
    Response<MemberTagConfigDetailResp> getTagConfig(@Valid GetByIdReq request);

}
