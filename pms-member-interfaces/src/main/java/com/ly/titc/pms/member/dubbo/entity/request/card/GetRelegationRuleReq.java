package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 根据卡和等级查询降级规则入参
 *
 * @Author：rui
 * @name：GetRelegationRuleByCardTemplateAndLevelReq
 * @Date：2024-10-30 11:24
 * @Filename：GetRelegationRuleByCardTemplateAndLevelReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetRelegationRuleReq extends BaseMasterReq {

    private Integer cardLevel;

    private Long cardId;
}
