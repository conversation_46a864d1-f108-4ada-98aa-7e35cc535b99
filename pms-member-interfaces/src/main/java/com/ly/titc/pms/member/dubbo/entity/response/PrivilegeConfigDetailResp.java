package com.ly.titc.pms.member.dubbo.entity.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberPrivilegeConfigDetailResp
 * @Date：2024-11-14 18:01
 * @Filename：MemberPrivilegeConfigDetailResp
 */
@Data
@Accessors(chain = true)
public class PrivilegeConfigDetailResp {

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 权益类型 1.仅作展示 2 价格折扣 3 预订保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 适用类型 1 集团 2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 全部 1 部分
     */
    private Integer scopeType;

    /**
     * 适用范围值
     */
    private List<String> scopeValues;

    /**
     * 权益值
     */
    private String value;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;

}