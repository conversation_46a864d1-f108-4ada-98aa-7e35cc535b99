package com.ly.titc.pms.member.dubbo.entity.response;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.titc.pms.member.dubbo.entity.request.common.DiscountBenefitResp;
import com.ly.titc.pms.member.dubbo.entity.request.common.MemberOrderPayResp;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname MemberRechargeDetailResp
 * @descrition 会员充值明细信息返回体
 * @since 2023/8/16 10:18
 */
@Data
@Accessors(chain = true)
public class MemberRechargeDetailResp {

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 平台渠道描述
     */
    private String platformChannelDesc;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 归属名称
     */
    private String masterName;
    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 订单状态 1 待支付 2 支付成功业务处理成功 3 支付失败 4 交易关闭 5.支付成功业务处理失败 6 部分退款 7 全额退款
     */
    private Integer orderState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp gmtCreate;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 数量
     * （充值的时候是充值明细-数量）
     */
    private Integer num;

    /**
     * 单价
     * 充值详情时是指 面额售价
     */
    private BigDecimal price;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 储值金额相关信息
     */
    private MemberOrderRechargeResp orderRecharge;

    /**
     * 活动礼包相关信息
     */
    private List<DiscountBenefitResp> benefits;

    /**
     * 支付信息
     */
    private MemberOrderPayResp payInfo;

}
