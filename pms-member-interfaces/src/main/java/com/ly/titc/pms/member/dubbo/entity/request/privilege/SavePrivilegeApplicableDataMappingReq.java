package com.ly.titc.pms.member.dubbo.entity.request.privilege;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 保存权益适用范围
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class SavePrivilegeApplicableDataMappingReq {

    /**
     * 权益类型 1.仅作展示 2 价格折扣 3 预订保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 适用类型 1 集团 2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 1 全部 0 部分
     */
    private Integer scopeType;

    /**
     * 适用范围值
     */
    private List<String> scopeValues = new ArrayList<>();

    /**
     * 权益值
     */
    private String value;

}
