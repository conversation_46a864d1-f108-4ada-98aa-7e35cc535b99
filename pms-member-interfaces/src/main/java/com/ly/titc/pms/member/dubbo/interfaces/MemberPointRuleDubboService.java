package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SavePointConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPointConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPointUsageRuleInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.SavePointUsageRuleResultResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 积分配置
 *
 * <AUTHOR>
 * @classname
 * @descrition 会员积分资产
 * @since 2024-12-29 13:12
 */
public interface MemberPointRuleDubboService {

    /**
     * 设置积分配置
     *
     * @param request
     * @return
     */
    Response<Boolean> setConfig(@Valid SavePointConfigReq request);

    /**
     * 查询积分设置
     *
     * @param request
     * @return
     */
    Response<MemberPointConfigResp> getConfig(@Valid BaseMasterReq request);

    /**
     * 分页查询积分可用规则
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberPointUsageRuleInfoResp>> pageRule(@Valid PagePointUsageRuleReq request);

    /**
     * 保存积分可用规则
     *
     * @param request
     * @return
     */
    Response<List<SavePointUsageRuleResultResp>> saveRule(@Valid SavePointUsageConfigReq request);

    /**
     * 积分可用规则状态更新
     *
     * @param request
     * @return
     */
    Response<Boolean> updateState(@Valid UpdatePointUsageStateReq request);

    /**
     * 删除积分可用规则
     *
     * @param request
     * @return
     */
    Response<Boolean> deleteRule(@Valid DeletePointUsageReq request);

    /**
     * 设置提醒
     *
     * @param request
     * @return
     */
    Response<List<SavePointUsageRuleResultResp>> remind(@Valid UsageRuleRemindReq request);
}
