package com.ly.titc.pms.member.dubbo.entity.request.member.config;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：MemberTagMarkRuleInfoDto
 * @Date：2024-11-8 11:17
 * @Filename：MemberTagMarkRuleInfoDto
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveMemberTagMarkRuleReq {

    /**
     * 打标条件类型
     */
    private Integer conditionType;

    /**
     * 计算方式
     */
    private Integer calculateType;

    /**
     * 条件值
     */
    private String conditionValue;


}
