package com.ly.titc.pms.member.dubbo.entity.request.member.config;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：MemberRelatedConfigInfoDto
 * @Date：2024-11-25 11:08
 * @Filename：MemberRelatedConfigInfoDto
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SaveMemberRelatedConfigReq extends BaseMasterReq {

    /**
     * 配置json
     */
    private String content;

    /**
     * 0 注册 1 手机验证设置 2 列表显示 3 列表快捷操作
     */
    private Integer type;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
