package com.ly.titc.pms.member.dubbo.entity.request.card;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 保存会员卡适用范围
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveCardApplicableDataMappingReq {

    /**
     * 适用类型 1 集团 2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围值
     */
    private String scopeValue;

}
