package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据归属分类
 *
 * <AUTHOR>
 * @date 2024/4/30 10:07
 */
@Getter
@AllArgsConstructor
public enum MasterTypeEnum {

    BLOC(1, "集团"),

    HOTEL(2, "酒店"),

    CLUB(3, "酒馆组");

    private final Integer type;

    private final String desc;

    public static String getDesc(Integer type){
        for (MasterTypeEnum masterTypeEnum : MasterTypeEnum.values()) {
            if (masterTypeEnum.getType().equals(type)){
                return masterTypeEnum.getDesc();
            }
        }
        return "";
    }
}
