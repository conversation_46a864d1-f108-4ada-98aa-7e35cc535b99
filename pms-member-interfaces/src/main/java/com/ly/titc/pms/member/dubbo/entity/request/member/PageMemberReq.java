package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.*;
import com.ly.titc.pms.member.dubbo.enums.*;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @classname PageMemberReq
 * @descrition 分页查询会员信息
 * @since 2023/8/12 10:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageMemberReq extends BasePageReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 查询模式
     * <p>强一致性：走DB查询</p>
     * <p>最终一致性：走ES查询</p>
     */
    private ConsistencyEnum queryMode = ConsistencyEnum.FINAL;

    /**
     * 会员真实姓名
     */
    private String realName;

    /**
     * 会员手机号
     */
    @LegalPhoneNumber(message = "手机号不合法")
    private String mobile;

    /**
     * 证件类型
     */
    @LegalEnum(target = IdTypeEnum.class, methodName = "getType", message = "证件类型不合法")
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 会员状态
     */
    @LegalEnum(target = MemberStateEnum.class, methodName = "getState", message = "会员状态不合法")
    private Integer state;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 注册开始日期
     */
    @LegalNormalDate(message = "会员注册开始日期不合法[pattern:yyyy-MM-dd]")
    private String memberRegisterBeginDate;

    /**
     * 注册结束日期
     */
    @LegalNormalDate(message = "会员注册结束日期不合法[pattern:yyyy-MM-dd]")
    private String memberRegisterEndDate;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 标签ID
     */
    private List<Long> tagIds;

    /**
     * 黑名单标记 0 未拉黑 1 已拉黑
     */
    private Integer blackFlag;
}
