package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 删除卡等级配置
 *
 * <AUTHOR>
 * @date 2025/6/27 11:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteCardLevelConfigReq extends BaseMasterReq {

    /**
     * 卡配置ID
     */
    @NotNull(message = "卡ID不能为空")
    private Long cardId;

    /**
     * 卡等级
     */
    @NotNull(message = "卡等级不能为空")
    private Integer careLevel;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
