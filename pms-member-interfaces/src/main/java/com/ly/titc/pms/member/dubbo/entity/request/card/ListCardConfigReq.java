package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 查询会员卡配置
 *
 * <AUTHOR>
 * @date 2025/6/27 10:38
 */
@Getter
@Setter
@Accessors(chain = true)
public class ListCardConfigReq extends BaseMasterReq {

    /**
     * 会员卡ID列表
     */
    @NotEmpty(message = "会员卡ID不能为空")
    private List<Long> cardIds;

}
