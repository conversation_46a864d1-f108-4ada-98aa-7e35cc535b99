package com.ly.titc.pms.member.dubbo.entity.request.member.config;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：GetMemberRelatedConfigReq
 * @Date：2024-11-25 11:34
 * @Filename：GetMemberRelatedConfigReq
 */
@Data
@Accessors(chain = true)
public class GetMemberRelatedConfigReq extends BaseMasterReq {

    /**
     * 0 注册 1 手机验证设置 2 列表显示 3 列表快捷操作
     */
    private Integer type;

}
