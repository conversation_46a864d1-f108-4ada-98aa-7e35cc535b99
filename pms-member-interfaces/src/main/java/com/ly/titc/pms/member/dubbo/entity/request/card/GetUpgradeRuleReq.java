package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据卡和等级查询升级规则入参
 *
 * @Author：rui
 * @name：GetUpgradeRuleByCardTemplateAndLevelReq
 * @Date：2024-10-30 11:24
 * @Filename：GetUpgradeRuleByCardTemplateAndLevelReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetUpgradeRuleReq extends BaseMasterReq {

    private Integer cardLevel;

    private Long cardId;
}
