package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 10:20
 */
@Getter
@AllArgsConstructor
public enum MemberStoreUsageModeEnum {
    ALLOCATE(1, "指定可用"),
    ALONE(2, "仅充值方可用"),
    ALL(3, "全部可用"),

    ;

    private Integer value;

    private String desc;

    public static MemberStoreUsageModeEnum getByValue(Integer value) {
        for (MemberStoreUsageModeEnum memberStoreUsageModeEnum : MemberStoreUsageModeEnum.values()) {
            if (memberStoreUsageModeEnum.getValue().equals(value)) {
                return memberStoreUsageModeEnum;
            }
        }
        return null;
    }
}
