package com.ly.titc.pms.member.dubbo.entity.request.card;

import lombok.Data;

/**
 * @Author：rui
 * @name：MemberCardLevelRelegationRuleDetailReq
 * @Date：2024-10-30 17:16
 * @Filename：MemberCardLevelRelegationRuleDetailReq
 */
@Data
public class CardLevelRelegationRuleDetailReq {

    private Long id;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 升级规则id
     */
    private Long upgradeRuleId;

    /**
     * 条件类型： 0-入住次数 1-房晚 2-充值金额 3-消费金 4-成长值
     */
    private Integer conditionType;

    /**
     * 条件值
     */
    private String conditionValue;

    /**
     * 计算方式：0-大于等于 1-大于 2-小于等于 3-小于
     */
    private Integer calculateType;

    /**
     * 是否限制渠道：0-不限制，1-限制
     */
    private Integer isRestrictChannel;

    /**
     * 限制渠道；多个渠道英文分号;分隔
     */
    private String restrictChannelCodes;
}
