package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author：rui
 * @name：ListByCardIdAndLevelReq
 * @Date：2024-11-7 19:56
 * @Filename：ListByCardIdAndLevelReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListCardLevelPrivilegeReq extends BaseMasterReq {

    private Long cardId;

    private List<Long> cardIdList;

    private Integer cardLevel;

    private Integer type;

    private Integer state;

    private String name;

    private String scopeValue;
}
