package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/27 14:59
 */
@Data
public class ListCardLevelUpgradeRuleReq extends BaseMasterReq {

    /**
     * 会员卡ID
     */
    private List<Long> cardIds;

    /**
     * 状态
     */
    private Integer state;

}
