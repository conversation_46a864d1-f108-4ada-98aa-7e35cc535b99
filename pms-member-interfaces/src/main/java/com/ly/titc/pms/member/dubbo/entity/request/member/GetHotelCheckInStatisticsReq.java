package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 查询门店入住统计
 *
 * <AUTHOR>
 * @date 2025/2/11 15:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetHotelCheckInStatisticsReq extends BaseReq {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 门店编号
     */
    @NotBlank(message = "门店编号不能为空")
    private String hotelCode;

}
