package com.ly.titc.pms.member.dubbo.entity.request.member.config;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/27 18:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteMemberTagConfigReq extends BaseMasterReq {

    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不能为空")
    private Long tagId;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
