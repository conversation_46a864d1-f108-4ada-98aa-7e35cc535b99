package com.ly.titc.pms.member.dubbo.entity.request.member.config;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：ListMemberTagConfigInfoReq
 * @Date：2024-11-18 15:57
 * @Filename：ListMemberTagConfigInfoReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ListMemberTagConfigReq extends BaseMasterReq {

    private List<Long> tagIds;

    private String name;
}
