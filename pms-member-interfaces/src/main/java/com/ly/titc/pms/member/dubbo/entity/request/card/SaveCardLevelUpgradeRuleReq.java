package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保存会员卡升级规则入参
 *
 * @Author：rui
 * @name：SaveMemberCardUpgradeRuleReq
 * @Date：2024-10-29 11:34
 * @Filename：SaveMemberCardUpgradeRuleReq
 */
@Data
public class SaveCardLevelUpgradeRuleReq extends BaseMasterReq {

    /**
     * 编辑时必传
     */
    private Long id;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 升级方案名称
     */
    private String name;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 升级类型 1 手动 2 自动
     */
    private Integer upgradeType;

    /**
     * 升级成功执行类型 ALL - 全部  ANY-满足任何一个条件
     */
    private String upgradeSuccessfulPerformType;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不可为空")
    private String operator;

    /**
     * 条件
     */
    private List<CardLevelUpgradeRuleDetailReq> details;

}
