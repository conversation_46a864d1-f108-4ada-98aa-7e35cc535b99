package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.GetByIdReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.*;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.common.entity.Response;

import javax.validation.Valid;
import java.util.List;

/**
 * 卡配置Dubbo接口
 *
 * <AUTHOR>
 * @date 2025/6/27 10:27
 */
public interface CardConfigDubboService {

    /**
     * 保存会员卡配置
     *
     * @return id
     */
    Response<Long> saveCardConfig(@Valid SaveCardConfigReq request);

    /**
     * 删除会员卡
     *
     * @param request
     * @return
     */
    Response<String> deleteCardConfig(@Valid DeleteCardConfigReq request);

    /**
     * 查询默认卡
     *
     * @param request
     * @return
     */
    Response<CardConfigResp> getDefaultCard(@Valid BaseMasterReq request);

    /**
     * 查询会员卡配置
     *
     * @param request
     * @return
     */
    Response<CardConfigResp> getCardConfig(@Valid GetCardConfigReq request);

    /**
     * 查询会员卡配置
     *
     * @param request
     * @return
     */
    Response<CardFullConfigResp> getCardFullConfig(@Valid GetCardConfigReq request);

    /**
     * 查询会员卡配置（多卡）
     *
     * @param request
     * @return
     */
    Response<List<CardConfigResp>> listCardConfig(@Valid ListCardConfigReq request);

    /**
     * 查询会员卡配置
     *
     * @param request
     * @return
     */
    Response<List<CardConfigResp>> listCardConfigByMaster(@Valid BaseMasterReq request);

    /**
     * 保存会员卡等级
     *
     * @return request
     */
    Response<Long> saveCardLevelConfig(@Valid SaveCardLevelConfigReq request);

    /**
     * 删除会员卡等级
     *
     * @param request
     * @return
     */
    Response<String> deleteCardLevelConfig(@Valid DeleteCardLevelConfigReq request);

    /**
     * 查询卡等级配置
     *
     * @param request
     * @return
     */
    Response<List<CardLevelConfigResp>> listCardLevelConfig(@Valid ListCardLevelConfigReq request);

    /**
     * 分页查询会员等级
     *
     * @param request
     * @return
     */
    Response<Pageable<CardLevelConfigResp>> pageCardLevelConfig(@Valid PageCardLevelConfigReq request);

    /**
     * 查询会员卡等级详情
     *
     * @param request
     * @return
     */
    Response<CardLevelConfigResp> getCardLevelConfig(@Valid GetByIdReq request);

    /**
     * 查询卡等级权益
     *
     * @param request
     * @return
     */
    Response<List<PrivilegeConfigResp>> listPrivilegeConfig(@Valid ListPrivilegeConfigReq request);

    /**
     * 启停操作会员卡等级
     *
     * @param request
     * @return
     */
    Response<String> actionCardLevel(@Valid ActionCardLevelReq request);

    /**
     * 根据卡和等级查询升级规则
     *
     * @param request
     * @return 升级规则
     */
    Response<CardLevelUpgradeRuleResp> getUpgradeRule(@Valid GetUpgradeRuleReq request);

    /**
     * 批量查询卡升级规则
     *
     * @param request
     * @return
     */
    Response<List<CardLevelUpgradeRuleResp>> listUpgradeRule(@Valid ListCardLevelUpgradeRuleReq request);

    /**
     * 分页查询会员卡升级规则
     *
     * @param request
     * @return 分页结果
     */
    Response<Pageable<CardLevelUpgradeRuleResp>> pageUpgradeRule(@Valid PageCardLevelUpgradeRuleReq request);

    /**
     * 升级规则详情
     *
     * @param request
     * @return
     */
    Response<CardLevelUpgradeRuleResp> getUpgradeRuleById(@Valid GetUpgradeRuleByIdReq request);

    /**
     * 保存会员卡升级规则
     *
     * @param req 请求体
     * @return id
     */
    Response<Long> saveCardLevelUpgradeRule(@Valid SaveCardLevelUpgradeRuleReq req);

    /**
     * 保存会员卡降级规则
     *
     * @param req 请求体
     * @return id
     */
    Response<Long> saveCardLevelRelegationRule(@Valid SaveCardLevelRelegationRuleReq req);

    /**
     * 批量查询卡保级规则
     *
     * @param request
     * @return
     */
    Response<List<CardLevelRelegationRuleResp>> listRelegationRule(@Valid ListCardLevelRelegationRuleReq request);

    /**
     * 分页查询会员卡降级规则
     *
     * @param request
     * @return 分页结果
     */
    Response<Pageable<CardLevelRelegationRuleResp>> pageRelegationRule(@Valid PageCardLevelRelegationRuleReq request);

    /**
     * 保级规则详情
     *
     * @param request
     * @return
     */
    Response<CardLevelRelegationRuleResp> getRelegationRule(@Valid GetRelegationRuleReq request);

    /**
     * 升级规则详情
     *
     * @param request
     * @return
     */
    Response<CardLevelRelegationRuleResp> getRelegationRuleById(@Valid GetRelegationRuleByIdReq request);

    /**
     * 删除升级规则
     *
     * @param request
     * @return
     */
    Response<String> deleteUpgradeRule(@Valid DeleteUpgradeRuleReq request);

    /**
     * 启用停用升级规则
     *
     * @param request
     * @return
     */
    Response<String> actionUpgradeRule(@Valid ActionUpgradeRuleReq request);

    /**
     * 删除保级规则
     *
     * @param request
     * @return
     */
    Response<String> deleteRelegationRule(@Valid DeleteRelegationRuleReq request);

    /**
     * 启停保级规则
     *
     * @param request
     * @return
     */
    Response<String> actionRelegationRule(@Valid ActionRelegationRuleReq request);

    /**
     * 查询卡等级权益
     *
     * @param request
     * @return
     */
    Response<List<CardLevelPrivilegeConfigResp>> listCardLevelPrivilege(@Valid ListCardLevelPrivilegeReq request);


}
