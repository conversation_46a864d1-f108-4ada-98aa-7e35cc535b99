package com.ly.titc.pms.member.dubbo.entity.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCardNoRuleInfoResp
 * @Date：2024-11-8 10:43
 * @Filename：MemberCardNoRuleInfoResp
 */
@Data
public class CardNoRuleResp {

    private Long id;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 卡号前缀
     */
    private String cardPrefix;

    /**
     * 卡号长度
     */
    private Integer cardLength;

    /**
     * 排除的数字（分号分隔）
     */
    private String excludeNumber;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;
}
