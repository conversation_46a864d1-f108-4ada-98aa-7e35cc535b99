package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/27 15:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteUpgradeRuleReq extends BaseMasterReq {

    /**
     * 保级规则ID
     */
    @NotNull(message = "保级规则ID不能为空")
    private Long ruleId;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
