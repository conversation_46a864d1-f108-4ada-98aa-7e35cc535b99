package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 14:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListCardLevelConfigReq extends BaseMasterReq {

    /**
     * 卡配置ID
     */
    private Long cardId;

    /**
     * 卡配置ID列表
     */
    private List<Long> cardIds;

    /**
     * 卡等级配置
     */
    private String name;
}
