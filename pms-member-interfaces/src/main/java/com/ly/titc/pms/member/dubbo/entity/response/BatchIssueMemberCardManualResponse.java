package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

import java.util.List;

/**
 * 批量手动发放会员卡响应
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
public class BatchIssueMemberCardManualResponse {

    /**
     * 总数量
     */
    private int totalCount;

    /**
     * 成功数量
     */
    private int successCount;

    /**
     * 失败数量
     */
    private int failureCount;

    /**
     * 详细结果列表
     */
    private List<IssueMemberCardResult> results;

    /**
     * 单个会员卡发放结果
     */
    @Data
    public static class IssueMemberCardResult {

        /**
         * 客户编号（客人转会员时传入）
         */
        private String customerNo;

        /**
         * 会员号（已有会员升级时传入）
         */
        private String memberNo;

        /**
         * 是否发放成功
         */
        private boolean success;

        /**
         * 失败原因
         */
        private String failureReason;

        /**
         * 发放成功后的会员号
         */
        private String resultMemberNo;

        /**
         * 会员卡号
         */
        private String memberCardNo;

        /**
         * 发放的会员卡等级
         */
        private Integer cardLevel;

        /**
         * 发放的会员卡等级名称
         */
        private String cardLevelName;

        /**
         * 操作类型（REGISTER: 注册会员, UPGRADE: 会员升级）
         */
        private String operationType;
    }
}
