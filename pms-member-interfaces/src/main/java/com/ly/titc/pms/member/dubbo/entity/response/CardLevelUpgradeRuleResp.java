package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelUpgradeRuleResp
 * @Date：2024-10-30 11:14
 * @Filename：MemberCardLevelUpgradeRuleResp
 */
@Data
public class CardLevelUpgradeRuleResp {

    /**
     * id
     */
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 原等级名称
     */
    private String sourceLevelName;

    /**
     * 目标等级名称
     */
    private String targetLevelName;

    /**
     * 升级成功执行类型 ALL - 全部  ANY-满足任何一个条件
     */
    private String upgradeSuccessfulPerformType;

    /**
     * 描述
     */
    private String description;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 规则详情
     */
    private List<CardLevelUpgradeRuleDetailResp> details;
}
