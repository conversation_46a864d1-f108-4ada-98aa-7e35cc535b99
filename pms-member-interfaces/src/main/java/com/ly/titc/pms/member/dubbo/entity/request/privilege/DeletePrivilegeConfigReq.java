package com.ly.titc.pms.member.dubbo.entity.request.privilege;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/27 19:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeletePrivilegeConfigReq extends BaseMasterReq {

    /**
     * 权益ID
     */
    @NotNull(message = "权益ID不能为空")
    private Long privilegeId;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
