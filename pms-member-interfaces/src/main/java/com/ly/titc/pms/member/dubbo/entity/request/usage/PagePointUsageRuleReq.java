package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 分页查询积分可用规则
 *
 * <AUTHOR>
 * @date 2025/6/12 10:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PagePointUsageRuleReq extends BasePageReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 适用来源
     */
    private List<String> scopeSources;

    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

    /**
     * 适用门店编码
     */
    private List<String> scopeHotelCodes;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

}
