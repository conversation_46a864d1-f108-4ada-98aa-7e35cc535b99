package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存储值可用规则
 *
 * <AUTHOR>
 * @date 2025/6/12 19:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveStoreUsageConfigReq extends BaseReq {

    private Long id;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 酒馆组编码（冗余存储）
     */
    private String clubCode;

    /**
     * 集团编码（冗余存储）
     */
    private String blocCode;

    /**
     * 酒店编码（冗余存储）
     */
    private Integer hotelCode;

    /**
     * 规则名称
     */
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    @NotEmpty(message = "适用渠道不能为空")
    private List<String> scopePlatformChannels;

    /**
     * 配置的适用来源 CLUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    @NotNull(message = "适用来源不能为空")
    private List<String> scopeSources;

    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     */
    @NotNull(message = "选择门店范围不能为空")
    private Integer scopeHotelRange;

    /**
     * 适用酒店codes
     * 适用来源是门店时 必填
     */
    private List<String> scopeHotelCodes;

    /**
     * 是否可用 1可使用 0不可使用
     */
    @NotNull(message = "是否可用不能为空")
    private Integer isCanUse;

    /**
     * 使用模式 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    @NotNull(message = "使用模式不能为空")
    private Integer usageMode;

    /**
     * 指定可用酒店codes
     */
    private List<String> usageHotelCodes;

    /**
     * 使用是否需要密码 1：需要 0 不需要
     */
    private Integer isUsePassword;

    /**
     * 可用场景
     */
    private List<String> scenes;

    /**
     * 储值扣款模式 1 优先本金 2 优先礼金 3 比例扣减
     */
    @NotNull(message = "储值扣款模式不能为空")
    private Integer deductionType;

    /**
     * 礼金扣减比例
     */
    private String deductionRatio;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
