package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * 查询入住统计
 *
 * <AUTHOR>
 * @classname GetByMemberNoReq
 * @descrition 会员编号查询请求体
 * @since 2023/6/7 13:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GetCheckInStatisticsReq extends BaseReq {

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

}
