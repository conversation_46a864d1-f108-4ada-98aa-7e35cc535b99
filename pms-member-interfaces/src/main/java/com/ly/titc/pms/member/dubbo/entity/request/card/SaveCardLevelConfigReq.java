package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 保存会员卡等级信息
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SaveCardLevelConfigReq extends BaseMasterReq {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员等级
     */
    private Integer cardLevel;

    /**
     * 会员等级名称
     */
    private String cardLevelName;

    /**
     * 会员等级描述信息
     */
    private String cardLevelDesc;

    /**
     * 卡费
     */
    private BigDecimal cardPrice;

    /**
     * 折扣
     */
    private Integer cardDiscount;

    /**
     * 会员保级类型：0-永久保级;1-不保级;2-条件保级
     */
    private Integer relegationType;

    /**
     * 等级背景图标
     */
    private String levelImage;

    /**
     * 状态；0 无效 1 正常 
     */
    private Integer state;

    /**
     * 有效期
     */
    private Integer validPeriod;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 权益
     */
    private List<SaveCardLevelPrivilegeConfigReq> privilegeConfigs;

}
