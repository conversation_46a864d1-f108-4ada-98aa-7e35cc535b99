package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/27 14:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActionCardLevelReq extends BaseMasterReq {
    /**
     * 卡配置ID
     */
    @NotNull(message = "卡ID不能为空")
    private Long cardId;

    /**
     * 卡等级
     */
    @NotNull(message = "卡等级不能为空")
    private Integer careLevel;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private Integer state;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
