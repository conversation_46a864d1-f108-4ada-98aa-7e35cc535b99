package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.entity.request.GetByIdReq;
import com.ly.titc.pms.member.dubbo.entity.request.privilege.*;
import com.ly.titc.pms.member.dubbo.entity.response.PrivilegeConfigResp;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/27 19:03
 */
public interface PrivilegeConfigDubboService {

    /**
     * 保存权益配置
     *
     * @param request
     * @return
     */
    Response<Long> savePrivilegeConfig(@Valid SavePrivilegeConfigReq request);

    /**
     * 删除权益配置
     *
     * @param request
     */
    Response<String> deletePrivilegeConfig(@Valid DeletePrivilegeConfigReq request);

    /**
     * 启用/禁用权益配置
     *
     * @param request
     * @return
     */
    Response<Long> actionPrivilegeConfig(@Valid ActionPrivilegeConfigReq request);

    /**
     * 根据归属查询会员权益
     * @param request
     * @return 会员权益配置
     */
    Response<PrivilegeConfigResp> getPrivilegeConfig(@Valid GetByIdReq request);

    /**
     * 根据归属查询会员权益
     * @param request
     * @return 会员权益配置
     */
    Response<List<PrivilegeConfigResp>> listPrivilegeConfig(@Valid BaseMasterReq request);

    /**
     * 分页查询权益
     * @param request
     * @return 分页结果
     */
    Response<Pageable<PrivilegeConfigResp>> pagePrivilegeConfig(@Valid PagePrivilegeConfigReq request);

    /**
     * 校验权益是否被使用
     *
     * @param request
     * @return
     */
    Response<Boolean> checkPrivilegeUsed(@Valid CheckPrivilegeUsedReq request);

}
