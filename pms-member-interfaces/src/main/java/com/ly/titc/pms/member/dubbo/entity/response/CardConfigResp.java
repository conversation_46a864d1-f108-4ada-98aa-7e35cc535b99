package com.ly.titc.pms.member.dubbo.entity.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardConfigResp
 * @Date：2024-10-30 11:13
 * @Filename：MemberCardConfigResp
 */
@Data
public class CardConfigResp {

    /**
     * 卡主键ID
     */
    private Long id;

    /**
     * 集团编码（ELONG为空）
     */
    private String blocCode;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 卡代码
     */
    private String cardCode;

    /**
     * 卡类型 1: 基础卡 2 企业卡
     */
    private Integer cardType;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 适用类型 1 集团  2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 默认 1 全部
     */
    private Integer applicationScope;

    /**
     * 手机号是否必填 0 否 1 是
     */
    private Integer mobileInput;

    /**
     * 姓名是否必填 0 否 1 是
     */
    private Integer nameInput;

    /**
     * 证件是否必填 0 否 1 是
     */
    private Integer certificateInput;

    /**
     * 是否默认 0 否 1是
     */
    private Integer isDefault;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;

    /**
     * 适用范围值列表
     */
    private List<ScopeValueResp> scopeValues = new ArrayList<>();

    /**
     * 卡生成规则
     */
    private CardNoRuleResp cardNoRule;

    /**
     * 卡等级配置
     */
    private List<CardLevelConfigWithPrivilegeResp> cardLevelConfigs = new ArrayList<>();
}
