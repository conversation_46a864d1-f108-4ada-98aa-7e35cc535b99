<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>pms-member-parent</artifactId>
    <groupId>com.ly.titc</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>pms-member-mediator</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-biz</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-facade</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>mdm-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>cdm-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>unified-message-interfaces</artifactId>
      <version>1.0.7-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>ehr-api</artifactId>
    </dependency>
    <!-- oauth-->
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>oauth-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.dsf</groupId>
      <artifactId>dubbo-dsf-extensions-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>springboot-start-redisson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>springboot-start-mq</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>springboot-start-elasticsearch</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-asset-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>cashier-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-account-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-spm-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-customer-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>chm-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-com</artifactId>
      <version>1.0.0</version>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>test/*</exclude>
          <exclude>qa/*</exclude>
          <exclude>uat/*</exclude>
          <exclude>stage/*</exclude>
          <exclude>product/*</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources/${package.environment}</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <showWarnings>true</showWarnings>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <!-- 引入lombok-->
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <!-- 引入mapstruct-processor-->
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${lombok-mapstruct-binding.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>