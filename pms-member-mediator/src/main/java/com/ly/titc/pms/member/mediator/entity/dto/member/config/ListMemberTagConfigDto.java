package com.ly.titc.pms.member.mediator.entity.dto.member.config;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：ListMemberTagConfigInfoReq
 * @Date：2024-11-18 15:57
 * @Filename：ListMemberTagConfigInfoReq
 */
@Data
@Accessors(chain = true)
public class ListMemberTagConfigDto {

    private Integer masterType;

    private String masterCode;

    private List<Long> tagIdList;

    private String name;
}
