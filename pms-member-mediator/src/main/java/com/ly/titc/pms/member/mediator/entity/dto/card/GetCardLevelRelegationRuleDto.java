package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据卡和等级查询降级规则入参
 *
 * @Author：rui
 * @name：GetRelegationRuleByCardTemplateAndLevelReq
 * @Date：2024-10-30 11:24
 * @Filename：GetRelegationRuleByCardTemplateAndLevelReq
 */
@Data
public class GetCardLevelRelegationRuleDto {

    private Integer masterType;

    private String masterCode;

    private Integer cardLevel;

    private Long cardId;
}
