package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;

import java.util.List;

/**
 * 积分使用规则
 *
 * <AUTHOR>
 * @date 2025/6/25 19:46
 */
public interface PointUsageMedService {

    /**
     * 根据适用主体查询积分使用规则
     *
     * @param dto
     * @return
     */
    List<PointUsageRuleDto> listPointRule(ListPointRuleDto dto);

    /**
     * 分页查询积分使用规则
     *
     * @param dto
     * @return
     */
    Pageable<PointUsageRuleDetailDto> pagePointUsageRule(PagePointUsageDto dto);

    /**
     * 保存使用规则
     *
     * @param dto
     * @return
     */
    List<SaveUsageRuleResultDto> savePointUsageRule(SavePointUsageRuleDto dto);

    /**
     * 更新规则状态
     *
     * @param dto
     * @return
     */
    Boolean updateState(UpdatePointUsageStateDto dto);

    /**
     * 删除规则
     *
     * @param dto
     */
    void deletePointUsageRule(DeletePointUsageRuleDto dto);

    /**
     * 查询集团适用规则
     *
     * @param dto
     * @return
     */
    List<BlocScopeUsageDto> listBlocScopeUsageRule(ListBlocScopeUsageDto dto);

    /**
     * 未设置提醒
     */
    List<MemberUsageRuleSaveResultDto> remind(String blocCode, Integer masterType, String masterCode);

}
