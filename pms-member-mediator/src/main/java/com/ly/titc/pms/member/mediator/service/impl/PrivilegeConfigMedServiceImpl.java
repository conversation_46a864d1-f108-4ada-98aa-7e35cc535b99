package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.biz.CardLevelPrivilegeConfigBiz;
import com.ly.titc.pms.member.biz.PrivilegeConfigBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import com.ly.titc.pms.member.entity.bo.PagePrivilegeConfigBo;
import com.ly.titc.pms.member.mediator.converter.CardConfigMedConverter;
import com.ly.titc.pms.member.mediator.converter.PrivilegeConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.*;
import com.ly.titc.pms.member.mediator.service.PrivilegeConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权益配置服务实现类
 *
 * <AUTHOR>
 * @date 2025/6/25 18:15
 */
@Slf4j
@Service
public class PrivilegeConfigMedServiceImpl implements PrivilegeConfigMedService {

    @Resource
    private PrivilegeConfigBiz privilegeConfigBiz;

    @Resource
    private PrivilegeConfigMedConverter privilegeConfigMedConverter;

    @Resource
    private CardLevelPrivilegeConfigBiz cardLevelPrivilegeConfigBiz;

    @Resource
    private CardConfigMedConverter cardConfigMedConverter;

    @Resource
    private RedisFactory redisFactory;

    @Override
    public Long savePrivilegeConfig(SavePrivilegeConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.PRIVILEGE_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode(), dto.getName());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this privilege config save is processing...masterType:{},masterCode:{},name:{}", dto.getMasterType(), dto.getMasterCode(), dto.getName());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            // 名称重复校验
            PrivilegeConfigInfo existPrivilegeConfig = privilegeConfigBiz.getByName(dto.getMasterType(), dto.getMasterCode(), dto.getName());
            if (existPrivilegeConfig != null && !existPrivilegeConfig.getId().equals(dto.getId())) {
                throw new ServiceException(RespCodeEnum.CONFIG_20008);
            }
            // 数据组装
            PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigMedConverter.convertDtoToPo(dto);
            if (dto.getId() == null) {
                privilegeConfigInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                List<PrivilegeApplicableDataMapping> mappings = privilegeConfigMedConverter.convertDto(dto, privilegeConfigInfo.getId());
                // 重复校验
                checkRepeatData(mappings);
                privilegeConfigBiz.insert(privilegeConfigInfo, mappings);
            } else {
                List<PrivilegeApplicableDataMapping> mappings = privilegeConfigMedConverter.convertDto(dto, privilegeConfigInfo.getId());
                // 重复校验
                checkRepeatData(mappings);
                privilegeConfigBiz.update(privilegeConfigInfo, mappings);
            }
            return privilegeConfigInfo.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }

    }


    @Override
    public void deletePrivilegeConfig(DeletePrivilegeConfigDto dto) {
        if (privilegeConfigBiz.getById(dto.getMasterType(), dto.getMasterCode(), dto.getPrivilegeId()) == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20010);
        }
        if (cardLevelPrivilegeConfigBiz.checkHasPrivilege(dto.getPrivilegeId())) {
            throw new ServiceException(RespCodeEnum.CONFIG_20009);
        }
        privilegeConfigBiz.deleteByPrivilegeId(dto.getPrivilegeId(), dto.getOperator());
    }

    @Override
    public Long actionPrivilegeConfig(ActionPrivilegeConfigDto dto) {
        if (privilegeConfigBiz.getById(dto.getMasterType(), dto.getMasterCode(), dto.getPrivilegeId()) == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20010);
        }
        privilegeConfigBiz.updateState(dto.getPrivilegeId(), dto.getState(), dto.getOperator());
        return dto.getPrivilegeId();
    }

    @Override
    public List<PrivilegeConfigDto> listPrivilegeConfig(Integer masterType, String masterCode) {
        List<PrivilegeConfigInfo> privilegeConfigInfos = privilegeConfigBiz.listByMaster(masterType, masterCode);
        if (CollectionUtils.isEmpty(privilegeConfigInfos)) {
            return Lists.newArrayList();
        }
        List<Long> privilegeIds = privilegeConfigInfos.stream().map(PrivilegeConfigInfo::getId).collect(Collectors.toList());
        List<PrivilegeApplicableDataMapping> applicableDataMappings = privilegeConfigBiz.listMappingByIds(privilegeIds);
        Map<Long, List<PrivilegeApplicableDataMapping>> privilegeMappingMap = applicableDataMappings.stream().collect(Collectors.groupingBy(PrivilegeApplicableDataMapping::getPrivilegeId));

        return privilegeConfigInfos.stream().map(privilegeConfigInfo -> {
            List<PrivilegeApplicableDataMapping> mappings = privilegeMappingMap.get(privilegeConfigInfo.getId());
            return cardConfigMedConverter.convertPoToDto(privilegeConfigInfo, mappings);
        }).collect(Collectors.toList());
    }

    @Override
    public Pageable<PrivilegeConfigDto> pagePrivilegeConfig(PagePrivilegeConfigDto dto) {
        PagePrivilegeConfigBo bo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<PrivilegeConfigInfo> page = privilegeConfigBiz.pagePrivilegeConfig(bo);
        List<PrivilegeConfigInfo> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        List<Long> privilegeIds = records.stream().map(PrivilegeConfigInfo::getId).collect(Collectors.toList());
        List<PrivilegeApplicableDataMapping> applicableDataMappings = privilegeConfigBiz.listMappingByIds(privilegeIds);
        Map<Long, List<PrivilegeApplicableDataMapping>> privilegeMappingMap = applicableDataMappings.stream().collect(Collectors.groupingBy(PrivilegeApplicableDataMapping::getPrivilegeId));

        return PageableUtil.convert(page, records.stream().map(privilegeConfigInfo -> {
            List<PrivilegeApplicableDataMapping> mappings = privilegeMappingMap.get(privilegeConfigInfo.getId());
            return cardConfigMedConverter.convertPoToDto(privilegeConfigInfo, mappings);
        }).collect(Collectors.toList()));
    }

    @Override
    public PrivilegeConfigDto getPrivilegeConfig(Integer masterType, String masterCode, Long id) {
        PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigBiz.getById(masterType, masterCode, id);
        if (privilegeConfigInfo == null) {
            return null;
        }
        List<PrivilegeApplicableDataMapping> applicableDataMappings = privilegeConfigBiz.listMappingById(privilegeConfigInfo.getId());
        return cardConfigMedConverter.convertPoToDto(privilegeConfigInfo, applicableDataMappings);
    }

    @Override
    public boolean checkPrivilegeUsed(Integer masterType, String masterCode, Long privilegeId) {
        PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigBiz.getById(masterType, masterCode, privilegeId);
        if (privilegeConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20010);
        }
        return cardLevelPrivilegeConfigBiz.checkHasPrivilege(privilegeId);
    }

    /**
     * 重复校验，scopeType 不能同时存在0 和 1 的，为 1的 时候，mappingDtoList只能有1挑，为0的时候，scopeValue不能重复
     *
     * @param mappings
     */
    private static void checkRepeatData(List<PrivilegeApplicableDataMapping> mappings) {
        Map<Integer, List<PrivilegeApplicableDataMapping>> map = mappings.stream().collect(Collectors.groupingBy(PrivilegeApplicableDataMapping::getScopeType));
        if (map.keySet().size() > 1) {
            if (map.containsKey(1)) {
                throw new ServiceException(RespCodeEnum.CONFIG_20006);
            } else {
                Set<String> set = new HashSet<>();
                map.forEach((key, list) -> {
                    for (PrivilegeApplicableDataMapping memberPrivilegeApplicableDataMappingDto : list) {
                        if (set.contains(memberPrivilegeApplicableDataMappingDto.getScopeValue())) {
                            throw new ServiceException(RespCodeEnum.CONFIG_20007);
                        } else {
                            set.add(memberPrivilegeApplicableDataMappingDto.getScopeValue());
                        }
                    }
                });
            }
        } else if (map.keySet().size() == 1) {
            Integer key = map.keySet().stream().findFirst().get();
            List<PrivilegeApplicableDataMapping> dtoList = map.get(key);
            if (key == 1 && dtoList.size() > 1) {
                throw new ServiceException(RespCodeEnum.CONFIG_20006);
            }
            if (key == 0) {
                Set<String> set = new HashSet<>();
                dtoList.forEach(item -> {
                    if (set.contains(item.getScopeValue())) {
                        throw new ServiceException(RespCodeEnum.CONFIG_20007);
                    } else {
                        set.add(item.getScopeValue());
                    }
                });
            }
        }
    }
}
