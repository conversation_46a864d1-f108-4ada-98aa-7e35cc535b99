package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.bo.MemberTagStatisticsBo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileInvoiceHeaderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileOccupantsInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.mediator.entity.dto.member.BatchAddMemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.MemberTagConfigDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 会员档案MedConverter
 *
 * <AUTHOR>
 * @date 2024/11/19 18:00
 */
@Mapper(componentModel = "spring")
public interface MemberProfileMedConverter {
    MemberProfileAddressInfoDto convertPoToDto(MemberProfileAddressInfo memberProfileAddressInfo);

    MemberProfileInvoiceHeaderInfoDto convertPoToDto(MemberProfileInvoiceHeaderInfo memberProfileAddressInfo);

    MemberProfileOccupantsInfoDto convertPoToDto(MemberProfileOccupantsInfo memberProfileOccupantsInfo);

    MemberProfileTagInfoDto convertPoToDto(MemberProfileTagInfo memberProfileTagInfo);

    MemberProfileAddressInfo convertDtoToPo(SaveMemberProfileAddressDto info);

    MemberProfileInvoiceHeaderInfo convertDtoToPo(SaveMemberProfileInvoiceHeaderDto info);

    MemberProfileOccupantsInfo convertDtoToPo(MemberProfileOccupantsInfo info);

    MemberProfileOccupantsInfo convertDtoToPo(SaveMemberProfileOccupantsDto info);

    MemberProfileTagInfo convertDtoToPo(AddMemberProfileTagDto info);

    @Mappings({
            @Mapping(target = "memberNo", source = "memberNo"),
            @Mapping(target = "markType", source = "dto.markType"),
            @Mapping(target = "tagId", source = "tagConfig.id"),
            @Mapping(target = "tagName", source = "tagConfig.name"),
            @Mapping(target = "createUser", source = "dto.operator"),
            @Mapping(target = "modifyUser", source = "dto.operator"),
    })
    MemberProfileTagInfo convertPoToPo(String memberNo, MemberTagConfigDetailDto tagConfig, BatchAddMemberTagDto dto);

    MemberEventMsg convertDtoToMsg(AddMemberProfileTagDto info);

    MemberProfileTagCountDto convertBoToDto(MemberTagStatisticsBo memberTagStatisticsBo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator"),
    })
    MemberProfileTagInfo convertDtoToPo(BatchAddSingleMemberTagDto info);
}
