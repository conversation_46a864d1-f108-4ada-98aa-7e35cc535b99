package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.biz.MemberRelatedConfigBiz;
import com.ly.titc.pms.member.biz.MemberTagConfigBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberRelatedConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberTagConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberTagMarkRuleInfo;
import com.ly.titc.pms.member.entity.bo.ListMemberTagConfigBo;
import com.ly.titc.pms.member.entity.bo.PageMemberTagConfigBo;
import com.ly.titc.pms.member.mediator.converter.MemberConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagCountDto;
import com.ly.titc.pms.member.mediator.service.MemberConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员配置实现
 *
 * <AUTHOR>
 * @date 2025/6/25 17:09
 */
@Slf4j
@Service
public class MemberConfigMedServiceImpl implements MemberConfigMedService {

    @Resource
    private MemberRelatedConfigBiz memberRelatedConfigBiz;

    @Resource
    private MemberConfigMedConverter memberConfigMedConverter;

    @Resource
    private MemberTagConfigBiz memberTagConfigBiz;

    @Resource
    private MemberProfileMedService memberProfileMedService;

    @Resource
    private RedisFactory redisFactory;

    @Override
    public Long saveMemberRelatedConfig(SaveMemberRelatedConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.MEMBER_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card related config save is processing...masterType:{},masterCode:{}", dto.getMasterType(), dto.getMasterCode());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberRelatedConfigInfo configInfo = memberConfigMedConverter.convertDtoToPo(dto);
            MemberRelatedConfigInfo memberRelatedConfigInfo = memberRelatedConfigBiz.getMemberRelatedConfig(dto.getMasterType(), dto.getMasterCode(), dto.getType());
            if (memberRelatedConfigInfo == null) {
                configInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                memberRelatedConfigBiz.insert(configInfo);
            } else {
                configInfo.setId(memberRelatedConfigInfo.getId());
                memberRelatedConfigBiz.update(configInfo);
            }
            return configInfo.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public MemberRelatedConfigDto getMemberRelatedConfig(GetMemberRelatedConfigDto dto) {
        MemberRelatedConfigInfo memberRelatedConfig = memberRelatedConfigBiz.getMemberRelatedConfig(dto.getMasterType(), dto.getMasterCode(), dto.getType());
        return memberConfigMedConverter.convertPoToDto(memberRelatedConfig);
    }

    @Override
    public Long saveTagConfig(SaveMemberTagConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.MEMBER_TAG_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode(), dto.getName());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this member tag save is processing...masterType:{},masterCode:{}", dto.getMasterType(), dto.getMasterCode());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberTagConfigInfo existMemberTagConfig = memberTagConfigBiz.getByName(dto.getMasterType(), dto.getMasterCode(), dto.getName());
            if (existMemberTagConfig != null && (!existMemberTagConfig.getId().equals(dto.getId()))) {
                throw new ServiceException(RespCodeEnum.CONFIG_20004);
            }
            MemberTagConfigInfo memberRelatedConfigInfo = memberConfigMedConverter.convertDtoToPo(dto);
            if (memberRelatedConfigInfo.getId() == null) {
                memberRelatedConfigInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                List<MemberTagMarkRuleInfo> markRules = CollectionUtils.isEmpty(dto.getMarkRules()) ? Lists.newArrayList() : dto.getMarkRules().stream().map(rule -> memberConfigMedConverter.convertDtoToPo(rule, memberRelatedConfigInfo.getId(),
                        IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId())).collect(Collectors.toList());
                memberTagConfigBiz.insert(memberRelatedConfigInfo, markRules);
            } else {
                List<MemberTagMarkRuleInfo> markRules = CollectionUtils.isEmpty(dto.getMarkRules()) ? Lists.newArrayList() : dto.getMarkRules().stream().map(rule -> memberConfigMedConverter.convertDtoToPo(rule, memberRelatedConfigInfo.getId(),
                        IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId())).collect(Collectors.toList());
                memberTagConfigBiz.update(memberRelatedConfigInfo, markRules);
            }
            return memberRelatedConfigInfo.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteTagConfig(DeleteMemberTagDto dto) {
        MemberTagConfigInfo existTagInfo = memberTagConfigBiz.getById(dto.getMasterType(), dto.getMasterCode(), dto.getTagId());
        if (Objects.isNull(existTagInfo)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20014);
        }
        // 校验标签是否被使用
        List<MemberProfileTagCountDto> memberProfileTagCounts = memberProfileMedService.listMemberTagCount(Collections.singletonList(dto.getTagId()));
        if (CollectionUtils.isNotEmpty(memberProfileTagCounts)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20005);
        }
        memberTagConfigBiz.deleteByTagId(dto.getTagId(), dto.getOperator());
    }

    @Override
    public Pageable<MemberTagConfigDetailDto> pageTagConfig(PageMemberTagConfigDto dto) {
        PageMemberTagConfigBo bo = memberConfigMedConverter.convertDtoToBo(dto);
        IPage<MemberTagConfigInfo> pageable = memberTagConfigBiz.pageMemberTagConfig(bo);
        if (CollectionUtils.isEmpty(pageable.getRecords())) {
            return Pageable.empty();
        }
        List<Long> tagIds = pageable.getRecords().stream().map(MemberTagConfigInfo::getId).collect(Collectors.toList());
        List<MemberTagConfigDetailDto> list = buildMemberTagConfigDetail(tagIds, pageable.getRecords());
        return PageableUtil.convert(pageable, list);
    }


    @Override
    public List<MemberTagConfigDetailDto> listTagConfig(ListMemberTagConfigDto dto) {
        ListMemberTagConfigBo bo = memberConfigMedConverter.convertDtoToBo(dto);
        List<MemberTagConfigInfo> list = memberTagConfigBiz.listTagConfig(bo);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<Long> tagIds = list.stream().map(MemberTagConfigInfo::getId).collect(Collectors.toList());
        return buildMemberTagConfigDetail(tagIds, list);
    }

    @Override
    public MemberTagConfigDetailDto getTagConfig(Integer masterType, String masterCode, Long id) {
        MemberTagConfigInfo dto = memberTagConfigBiz.getById(masterType, masterCode, id);
        if (Objects.isNull(dto)) {
            return null;
        }
        List<MemberProfileTagCountDto> memberProfileTagCounts = memberProfileMedService.listMemberTagCount(Collections.singletonList(dto.getId()));
        List<MemberTagMarkRuleInfo> details = memberTagConfigBiz.listMarkRuleByTagId(dto.getId());
        return memberConfigMedConverter.convertTagConfigResp(dto, details, CollectionUtils.isEmpty(memberProfileTagCounts) ? 0 : memberProfileTagCounts.get(0).getCount());
    }

    private List<MemberTagConfigDetailDto> buildMemberTagConfigDetail(List<Long> tagIds, List<MemberTagConfigInfo> configs) {
        List<MemberProfileTagCountDto> memberProfileTagCounts = memberProfileMedService.listMemberTagCount(tagIds);
        Map<Long, Integer> memberTagCountMap = memberProfileTagCounts.stream().collect(Collectors.toMap(MemberProfileTagCountDto::getTagId, MemberProfileTagCountDto::getCount));
        List<MemberTagMarkRuleInfo> memberTagMarkRuleInfos = memberTagConfigBiz.listMarkRuleByTagIds(tagIds);
        Map<Long, List<MemberTagMarkRuleInfo>> markRuleMap = memberTagMarkRuleInfos.stream().collect(Collectors.groupingBy(MemberTagMarkRuleInfo::getTagId));
        return configs.stream()
                .map(tag -> memberConfigMedConverter.convertTagConfigResp(tag, markRuleMap.get(tag.getId()), memberTagCountMap.getOrDefault(tag.getId(), 0)))
                .collect(Collectors.toList());
    }
}
