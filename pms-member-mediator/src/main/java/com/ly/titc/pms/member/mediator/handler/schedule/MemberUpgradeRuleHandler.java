package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.biz.CardLevelConfigBiz;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.*;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberUpgradeRuleHandler
 * @Date：2024-11-21 14:24
 * @Filename：MemberUpgradeRuleHandler
 */
@Slf4j
@Component
public class MemberUpgradeRuleHandler extends AbstractScheduleHandler<MemberUpgradeRuleDto> {

    @Resource
    private ScheduleMedConverter scheduleMedConverter;
    @Resource
    private CardConfigMedService cardConfigMedService;
    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private MemberCardMedService memberCardMedService;
    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;


    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }
        // 查询每个卡对应的升降级规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelUpgradeRuleDto upgradeRuleDto = new ListCardLevelUpgradeRuleDto();
        upgradeRuleDto
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StatusEnum.VALID.getStatus());
        List<CardLevelUpgradeRuleDto> rules = cardConfigMedService.listCardLevelUpgradeRule(upgradeRuleDto);
        // 按照sort排序，同一个cardId-sourceLevel组合的规则按sort顺序执行
        Map<String, List<CardLevelUpgradeRuleDto>> ruleMap = rules.stream()
                .sorted(Comparator.comparing(CardLevelUpgradeRuleDto::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.groupingBy(
                        item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()),
                        Collectors.toList()
                ));
        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto();
        levelConfigDto.setMasterType(masterType).setMasterCode(masterCode).setCardIds(cardIds).setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));

        // 创建缓存对象，避免重复查询
        MemberRecordsCache cache = new MemberRecordsCache();
        LocalDateTime endDate = LocalDateTime.now();
        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            List<CardLevelUpgradeRuleDto> rulesForCard = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的升级规则，跳过这张卡
            if (CollectionUtils.isEmpty(rulesForCard)) {
                log.info("没有找到对应的升级规则，跳过这张卡, memberNo: {}", memberNo);
                continue;
            }
            // 按sort顺序执行所有规则
            for (CardLevelUpgradeRuleDto rule : rulesForCard) {
                MemberUpgradeRuleDto ruleDto = scheduleMedConverter.convertUpgradeRuleDto(rule);
                if (ruleDto == null) {
                    continue;
                }
                MemberCardLevelChangeRecord memberCardLevelChangeRecord;
                LocalDateTime startDate;
                if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER.getType())) {
                    // 获取这个会员的注册日期
                    memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                            Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
                    startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
                } else {
                    // 获取这个会员的上次升降级日期
                    List<Integer> changeTypeList = Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(),
                            ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType());
                    memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, changeTypeList);
                    startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
                }
                log.info("startDate: {}, memberNo: {}", startDate, memberNo);
                // 获取会员的各种记录数据（使用缓存优化，避免重复查询）
                Map<String, List<BaseCheckDto>> memberRecords = getAllMemberRecords(masterType, masterCode, memberNo, startDate, endDate, cache);
                // 检查升级条件
                ConditionCheckResult checkResult;
                if (ruleDto.getUpgradeSuccessfulPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                    checkResult = checkAllConditionsWithDetail(ruleDto.getDetails(), memberNo,
                            memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                            memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                            memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
                } else {
                    checkResult = checkAnyConditionWithDetail(ruleDto.getDetails(), memberNo,
                            memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                            memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                            memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
                }
                log.info("checkResult: {}, memberNo: {}", checkResult, memberNo);
                CardLevelConfigInfo cardLevelConfig = cardLevelConfigBiz.getByCardLevel(cardId, ruleDto.getTargetLevel());
                if (cardLevelConfig == null) {
                    log.info("需要升级的会员卡等级不存在，memberNo: {}, targetLevel: {}", memberNo, ruleDto.getTargetLevel());
                    continue;
                }
                if (checkResult.isPassed()) {
                    CardLevelConfigWithPrivilegeDto beforeLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
                    if (beforeLevelInfo == null) {
                        continue;
                    }
                    // 升级会员卡等级
                    UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                    updateCardLevelDto.setMemberNo(memberNo)
                            .setCardId(cardId)
                            .setMemberCardNo(memberCardInfo.getMemberCardNo())
                            .setPreLevel(cardLevel)
                            .setPreLevelName(beforeLevelInfo.getCardLevelName())
                            .setAfterLevel(ruleDto.getTargetLevel())
                            .setAfterLevelName(cardLevelConfig.getCardLevelName())
                            .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                            .setEffectEndDate(cardLevelConfig.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(cardLevelConfig.getValidPeriod())))
                            .setIsLongTerm(cardLevelConfig.getIsLongTerm())
                            .setChangeType(ChangeTypeEnum.UPGRADE_AUTO.getType())
                            .setReason("自动升级：" + checkResult.generateReason())
                            .setOperator("自动升级，规则名称：" + ruleDto.getName())
                            .setRuleId(rule.getId())
                            .setSourceType("BLOC")
                            .setSourceHotel(masterCode);
                    memberCardMedService.updateCardLevel(updateCardLevelDto);
                    // 记录日志
                    addLogRecord(ActionEnum.MEMBER_LEVEL_UPDATE, masterCode, memberNo, updateCardLevelDto.getReason() + ",规则名称：" + ruleDto.getName(), "定时任务");
                }
            }
        }
    }

    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_UPGRADE_RULE.getAction();
    }
}
