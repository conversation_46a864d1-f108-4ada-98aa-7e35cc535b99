package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.pms.member.biz.StoreConfigBiz;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.StoreConfigInfo;
import com.ly.titc.pms.member.mediator.converter.StoreConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.store.SaveStoreConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.StoreConfigDto;
import com.ly.titc.pms.member.mediator.service.StoreConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 储值设置
 *
 * <AUTHOR>
 * @date 2025/6/26 10:40
 */
@Slf4j
@Service
public class StoreConfigMedServiceImpl implements StoreConfigMedService {

    @Resource
    private StoreConfigBiz storeConfigBiz;

    @Resource
    private StoreConfigMedConverter storeConfigMedConverter;

    @Override
    public void saveStoreConfig(SaveStoreConfigDto dto) {
        StoreConfigInfo existInDB = storeConfigBiz.getByMaster(dto.getMasterType(), dto.getMasterCode());
        StoreConfigInfo config = storeConfigMedConverter.convertDtoToPo(dto);
        if (existInDB != null) {
            config.setId(existInDB.getId());
            config.setCreateUser(existInDB.getCreateUser());
            config.setGmtCreate(existInDB.getGmtCreate());
            storeConfigBiz.updateById(config);
            //如果是集团修改
            if (dto.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                //查找是否有酒店自定义设置，如果有也要刷新掉
                storeConfigBiz.updateByBloc(dto.getBlocCode(), MasterTypeEnum.HOTEL.getType(),
                        config.getIsPayVerifyRequired(), config.getIsSupportOtherCard(), dto.getOperator());
            }
        } else {
            config.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            storeConfigBiz.insert(config);
        }
    }

    @Override
    public StoreConfigDto getStoreConfig(String blocCode, Integer masterType, String masterCode) {
        StoreConfigInfo existInDB = storeConfigBiz.getByMaster(masterType, masterCode);
        //如果是酒店设置
        if (masterType.equals(MasterTypeEnum.HOTEL.getType())) {
            //查询下集团设置
            StoreConfigInfo blocConfig = storeConfigBiz.getByMaster(MasterTypeEnum.BLOC.getType(), blocCode);
            if (blocConfig == null) {
                blocConfig = initBlocConfig(blocCode);
            }
            if (existInDB == null) {
                return storeConfigMedConverter.convertPoToDto(blocConfig);

            } else {
                //是否允许酒店修改以集团设置为准
                existInDB.setAllowOtherCardModify(blocConfig.getAllowOtherCardModify());
                existInDB.setAllowPayVerifyModify(blocConfig.getAllowPayVerifyModify());
                //设置的值根据配置判断取酒店还是自己
                if (blocConfig.getAllowOtherCardModify().equals(StateEnum.NO_VALID.getState())) {
                    //如果不允许酒店自定义则取酒店设置
                    existInDB.setIsSupportOtherCard(blocConfig.getIsSupportOtherCard());
                }
                if (blocConfig.getAllowPayVerifyModify().equals(StateEnum.NO_VALID.getState())) {
                    existInDB.setIsPayVerifyRequired(blocConfig.getIsPayVerifyRequired());
                }
            }
        }
        return storeConfigMedConverter.convertPoToDto(existInDB);
    }

    private StoreConfigInfo initBlocConfig(String blocCode) {
        StoreConfigInfo config = new StoreConfigInfo();
        config.setMasterType(MasterTypeEnum.BLOC.getType());
        config.setMasterCode(blocCode);
        config.setBlocCode(blocCode);
        config.setAllowPayVerifyModify(StateEnum.VALID.getState());
        config.setIsPayVerifyRequired(StateEnum.VALID.getState());
        config.setIsSupportOtherCard(StateEnum.VALID.getState());
        config.setAllowOtherCardModify(StateEnum.VALID.getState());
        return config;
    }
}
