package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.PrivilegeClassificationEnum;
import com.ly.titc.pms.member.com.enums.PrivilegeTypeEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.bo.ListCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelRelegationRuleBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelUpgradeRuleBo;
import com.ly.titc.pms.member.mediator.converter.CardConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.service.CardConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员卡配置实现
 *
 * <AUTHOR>
 * @date 2025/6/25 14:11
 */
@Slf4j
@Service
public class CardConfigMedServiceImpl implements CardConfigMedService {

    @Resource
    private CardConfigBiz cardConfigBiz;

    @Resource
    private CardNoRuleBiz cardNoRuleBiz;

    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Resource
    private CardLevelUpgradeRuleBiz cardLevelUpgradeRuleBiz;

    @Resource
    private CardLevelRelegationRuleBiz cardLevelRelegationRuleBiz;

    @Resource
    private CardLevelPrivilegeConfigBiz cardLevelPrivilegeConfigBiz;

    @Resource
    private PrivilegeConfigBiz privilegeConfigBiz;

    @Resource
    private CardConfigMedConverter cardConfigMedConverter;

    @Resource
    private CardConfigService cardConfigService;

    @Resource
    private RedisFactory redisFactory;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private HotelDecorator hotelDecorator;

    @Override
    public Long saveCardConfig(SaveCardConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode(), dto.getCardName());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card config save is processing...masterType:{};masterCode:{},cardName:{}", dto.getMasterType(), dto.getMasterCode(), dto.getCardName());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            CardConfigInfo existCardConfig = cardConfigBiz.getByCardName(dto.getMasterType(), dto.getMasterCode(), dto.getCardName());
            if (existCardConfig != null && (dto.getId() == null || !existCardConfig.getId().equals(dto.getId()))) {
                throw new ServiceException(RespCodeEnum.CONFIG_20015);
            }
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            dto.setCardCode(StringUtils.isBlank(dto.getCardCode()) ? CommonUtil.generateUniqueNo() : dto.getCardCode());
            CardConfigInfo cardConfigInfo = cardConfigMedConverter.convertDtoToPo(dto);
            CardNoRuleInfo cardNoRuleInfo = cardConfigMedConverter.convertDtoToPo(dto.getCardNoRule(), dto.getId(), dto.getOperator());
            List<CardApplicableDataMapping> mappings = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dto.getApplicableDataMappings())) {
                mappings = dto.getApplicableDataMappings().stream().map(mapping -> cardConfigMedConverter.convertDtoToPo(mapping, dto.getId(), dto.getOperator())).collect(Collectors.toList());
            }
            cardConfigService.saveCardConfig(cardConfigInfo, mappings, cardNoRuleInfo);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public CardConfigDto getDefaultCard(Integer masterType, String masterCode) {
        CardConfigInfo defaultCard = cardConfigBiz.getDefaultCard(masterType, masterCode);
        if (defaultCard == null) {
            return null;
        }
        return getCardConfig(masterType, masterCode, defaultCard.getId());
    }

    @Override
    public List<CardConfigDto> listCardConfig(Integer masterType, String masterCode, List<Long> cardIds) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(masterType, masterCode, cardIds);
        if (CollectionUtils.isEmpty(cardConfigInfos)) {
            return Lists.newArrayList();
        }
        // 适用门店
        List<CardApplicableDataMapping> cardApplicableDataMappings = cardConfigBiz.lisApplicableDataMapping(cardIds);
        Map<Long, List<CardApplicableDataMapping>> cardApplicableDataMappingMap = cardApplicableDataMappings.stream().collect(Collectors.groupingBy(CardApplicableDataMapping::getCardId));
        // 卡生成规则
        List<CardNoRuleInfo> memberCardNoRuleInfos = cardNoRuleBiz.listByCardIds(cardIds);
        Map<Long, CardNoRuleInfo> memberCardNoRuleMap = memberCardNoRuleInfos.stream().collect(Collectors.toMap(CardNoRuleInfo::getCardId, Function.identity()));
        // 卡等级
        List<CardLevelConfigInfo> cardLevelConfigs = cardLevelConfigBiz.listByCardIds(masterType, masterCode, cardIds);
        // 酒店
        List<String> hotelCodes = cardApplicableDataMappings.stream().filter(Objects::nonNull).map(CardApplicableDataMapping::getScopeValue).distinct().collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfoList = hotelDecorator.listHotelBaseInfos(null, null, null, hotelCodes);
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, Function.identity()));
        Map<Long, List<CardLevelConfigInfo>> cardLevelMap = cardLevelConfigs.stream().collect(Collectors.groupingBy(CardLevelConfigInfo::getCardId));
        return cardConfigInfos.stream().map(cardConfigInfo -> {
            CardConfigDto cardConfigDto = cardConfigMedConverter.convertPoToDto(cardConfigInfo);
            List<CardApplicableDataMapping> applicableDataMappings = cardApplicableDataMappingMap.getOrDefault(cardConfigDto.getId(), Lists.newArrayList());
            cardConfigDto.setScopeValues(applicableDataMappings.stream().map(item -> buildScopeValue(item, hotelMap)).collect(Collectors.toList()));
            CardNoRuleInfo cardNoRuleInfo = memberCardNoRuleMap.get(cardConfigDto.getId());
            cardConfigDto.setCardNoRule(cardConfigMedConverter.convertPoToDto(cardNoRuleInfo));
            List<CardLevelConfigInfo> cardLevels = cardLevelMap.getOrDefault(cardConfigDto.getId(), Lists.newArrayList());
            cardConfigDto.setCardLevelConfigs(cardLevels.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            return cardConfigDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Long saveCardLevelUpgradeRule(SaveCardLevelUpgradeRuleDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getCardId());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card level upgrade config save is processing...cardId:{}", dto.getCardId());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            List<CardLevelUpgradeRuleInfo> cardLevelUpgradeRuleInfos = cardLevelUpgradeRuleBiz.listByCardId(dto.getCardId());
            // 当前会员卡等级升级规则，无法重复配置
            if (CollectionUtils.isNotEmpty(cardLevelUpgradeRuleInfos)) {
                boolean flag = cardLevelUpgradeRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()))
                        .anyMatch(item -> item.getTargetLevel().equals(dto.getTargetLevel()) && item.getSourceLevel().equals(dto.getSourceLevel()) && !item.getId().equals(dto.getId()));
                if (flag) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10024);
                }
            }
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo = cardConfigMedConverter.convertDtoToPo(dto);
            List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails = dto.getDetails().stream().map(detail -> cardConfigMedConverter.convertDtoToPo(detail, dto.getCardId(), dto.getId(), dto.getOperator())).collect(Collectors.toList());
            cardConfigService.saveCardLevelUpgradeRule(cardLevelUpgradeRuleInfo, upgradeRuleDetails);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public Long saveCardLevelRelegationRule(SaveCardLevelRelegationRuleDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getCardId());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card level relegation config save is processing...cardId:{}", dto.getCardId());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            List<CardLevelRelegationRuleInfo> cardLevelUpgradeRuleInfos = cardLevelRelegationRuleBiz.listByCardId(dto.getCardId());
            // 当前会员卡等级保级规则，无法重复配置
            if (CollectionUtils.isNotEmpty(cardLevelUpgradeRuleInfos)) {
                boolean flag = cardLevelUpgradeRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()))
                        .anyMatch(item -> item.getTargetLevel().equals(dto.getTargetLevel()) && item.getSourceLevel().equals(dto.getSourceLevel()) && !item.getId().equals(dto.getId()));
                if (flag) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10025);
                }
            }
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            CardLevelRelegationRuleInfo cardLevelUpgradeRuleInfo = cardConfigMedConverter.convertDtoToPo(dto);
            List<CardLevelRelegationRuleDetailInfo> upgradeRuleDetails = dto.getDetails().stream().map(detail -> cardConfigMedConverter.convertDtoToPo(detail, dto.getCardId(), dto.getId(), dto.getOperator())).collect(Collectors.toList());
            cardConfigService.saveCardLevelRelegationRule(cardLevelUpgradeRuleInfo, upgradeRuleDetails);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public Long saveCardLevelConfig(SaveCardLevelConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getCardId(), dto.getCardLevel());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card level config save is processing...cardId:{}", dto.getCardId());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            // 查询卡关联等级列表
            List<CardLevelConfigInfo> cardLevelConfigInfoRespList = cardLevelConfigBiz.listByCardIds(dto.getMasterType(), dto.getMasterCode(), Collections.singletonList(dto.getCardId()));
            if (cardLevelConfigInfoRespList.stream().filter(item -> !item.getId().equals(dto.getId()))
                    .anyMatch(item -> item.getCardLevel().equals(dto.getCardLevel()))) {
                throw new ServiceException(RespCodeEnum.MEMBER_10023);
            }
            // 权益校验
            checkPrivilege(dto);
            // 填充数据
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            List<Long> privilegeIds = dto.getPrivilegeConfigs().stream().map(SaveCardLevelPrivilegeConfigDto::getPrivilegeId).collect(Collectors.toList());
            List<PrivilegeApplicableDataMapping> mappingDtoList = privilegeConfigBiz.listMappingByIds(privilegeIds);
            PrivilegeApplicableDataMapping discountMappingDto = mappingDtoList.stream().filter(item -> item.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType())).findFirst().orElse(null);
            if (Objects.nonNull(discountMappingDto)) {
                dto.setCardDiscount(new BigDecimal(discountMappingDto.getValue()).multiply(new BigDecimal(10)).intValue());
            } else {
                throw new ServiceException(RespCodeEnum.CONFIG_20000);
            }
            // 组装数据
            CardLevelConfigInfo cardLevelConfigInfo = cardConfigMedConverter.convertDtoToPo(dto);
            List<CardLevelPrivilegeConfigInfo> mappings = dto.getPrivilegeConfigs().stream()
                    .map(config -> cardConfigMedConverter.convertDtoToPo(config, dto.getCardId(), dto.getCardLevel(), dto.getOperator())).collect(Collectors.toList());
            cardConfigService.saveCardLevelConfig(cardLevelConfigInfo, mappings);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteCardConfig(DeleteCardConfigDto dto) {
        Long cardId = dto.getCardId();
        String operator = dto.getOperator();
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(dto.getMasterType(), dto.getMasterCode(), cardId);
        if (cardConfigInfo == null) {
            return;
        }
        // 先校验有没有会员
        if (memberCardMedService.checkHasCard(cardConfigInfo.getMasterType(), cardConfigInfo.getMasterCode(), cardId, null)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10020);
        }
        cardConfigService.deleteCard(cardId, operator);
    }

    @Override
    public void deleteCardLevelConfig(DeleteCardLevelConfigDto dto) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
        if (cardConfigInfo == null) {
            return;
        }
        // 先校验有没有会员
        if (memberCardMedService.checkHasCard(cardConfigInfo.getMasterType(), cardConfigInfo.getMasterCode(), dto.getCardId(), dto.getCardLevel())) {
            throw new ServiceException(RespCodeEnum.MEMBER_10020);
        }
        cardConfigService.deleteCardLevel(dto.getCardId(), dto.getCardLevel(), dto.getOperator());
    }

    @Override
    public List<PrivilegeConfigDto> listCardLevelPrivilege(Integer masterType, String masterCode, Long cardId, Integer cardLevel) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(masterType,masterCode, cardId);
        if (cardConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20011);
        }
        CardLevelConfigInfo cardLevelConfigInfo = cardLevelConfigBiz.getByCardLevel(cardId, cardLevel);
        List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigs = cardLevelPrivilegeConfigBiz.listByCardId(cardLevelConfigInfo.getCardId(), cardLevelConfigInfo.getCardLevel());
        if (CollectionUtils.isEmpty(cardLevelPrivilegeConfigs)){
            return Lists.newArrayList();
        }
        // 查询权益
        List<Long> privilegeIds = cardLevelPrivilegeConfigs.stream().map(CardLevelPrivilegeConfigInfo::getPrivilegeId).collect(Collectors.toList());
        Map<Long, PrivilegeConfigInfo> privilegeConfigMap = mapPrivilegeMap(privilegeIds);
        Map<Long, List<PrivilegeApplicableDataMapping>> privilegeMappingMap = mapPrivilegeMapping(privilegeIds);
        // 转换
        return buildPrivilegeConfig(cardLevelPrivilegeConfigs, privilegeConfigMap, privilegeMappingMap);
    }



    @Override
    public Pageable<CardLevelConfigWithPrivilegeDto> pageCardLevelConfig(PageCardLevelConfigDto dto) {
        PageCardLevelConfigBo pageBo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<CardLevelConfigInfo> page = cardLevelConfigBiz.pageCardLevelConfig(pageBo);
        List<CardLevelConfigInfo> dtoList = page.getRecords();
        if (CollectionUtils.isEmpty(dtoList)) {
            return Pageable.empty();
        }
        List<Long> cardIds = dtoList.stream().map(CardLevelConfigInfo::getCardId).collect(Collectors.toList());
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto().setMasterType(dto.getMasterType()).setMasterCode(dto.getMasterCode()).setCardIds(cardIds);
        List<CardLevelConfigWithPrivilegeDto> privileges = listCardLevelConfig(levelConfigDto);
        return PageableUtil.convert(page, privileges);
    }

    @Override
    public CardLevelConfigWithPrivilegeDto getCardLevelConfig(Integer masterType, String masterCode, Long levelId) {
        CardLevelConfigInfo cardLevelConfigInfo = cardLevelConfigBiz.getById(masterType, masterCode, levelId);
        if (cardLevelConfigInfo == null) {
            return null;
        }
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(masterType, masterCode, cardLevelConfigInfo.getCardId());
        // 查询会员卡等级
        List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigs = cardLevelPrivilegeConfigBiz.listByCardId(cardLevelConfigInfo.getCardId(), cardLevelConfigInfo.getCardLevel());
        // 查询权益
        List<Long> privilegeIds = cardLevelPrivilegeConfigs.stream().map(CardLevelPrivilegeConfigInfo::getPrivilegeId).collect(Collectors.toList());
        Map<Long, PrivilegeConfigInfo> privilegeConfigMap = mapPrivilegeMap(privilegeIds);
        Map<Long, List<PrivilegeApplicableDataMapping>> privilegeMappingMap = mapPrivilegeMapping(privilegeIds);
        // 转换
        CardLevelConfigWithPrivilegeDto configDto = cardConfigMedConverter.convertPoToDto(cardLevelConfigInfo);
        configDto.setCardName(cardConfigInfo.getCardName());
        if (CollectionUtils.isNotEmpty(cardLevelPrivilegeConfigs)) {
            List<PrivilegeConfigDto> privilegeConfigs = buildPrivilegeConfig(cardLevelPrivilegeConfigs, privilegeConfigMap, privilegeMappingMap);
            configDto.setPrivileges(privilegeConfigs);
        }
        return configDto;
    }

    @Override
    public List<CardLevelConfigWithPrivilegeDto> listCardLevelConfig(ListCardLevelConfigDto dto) {
        ListCardLevelConfigBo listBo = cardConfigMedConverter.convertDtoToBo(dto);
        List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelConfigBiz.listCardLevelConfig(listBo);
        if (CollectionUtils.isEmpty(cardLevelConfigInfos)) {
            return Lists.newArrayList();
        }
        List<Long> cardIds = cardLevelConfigInfos.stream().map(CardLevelConfigInfo::getCardId).distinct().collect(Collectors.toList());
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(dto.getMasterType(), dto.getMasterCode(), cardIds);
        Map<Long, CardConfigInfo> cardConfigInfoDtoMap = cardConfigInfos.stream().collect(Collectors.toMap(CardConfigInfo::getId, Function.identity()));
        // 查询会员卡等级
        List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigs = cardLevelPrivilegeConfigBiz.listByCardIds(cardIds);
        Map<String, List<CardLevelPrivilegeConfigInfo>> cardLevelPrivilegeMap = cardLevelPrivilegeConfigs.stream().collect(Collectors.groupingBy(e -> String.format("%s_%s", e.getCardId(), e.getCardLevel())));
        // 查询权益
        List<Long> privilegeIds = cardLevelPrivilegeConfigs.stream().map(CardLevelPrivilegeConfigInfo::getPrivilegeId).collect(Collectors.toList());
        Map<Long, PrivilegeConfigInfo> privilegeConfigMap = mapPrivilegeMap(privilegeIds);
        Map<Long, List<PrivilegeApplicableDataMapping>> privilegeMappingMap = mapPrivilegeMapping(privilegeIds);
        return cardLevelConfigInfos.stream().map(cardLevel -> {
            CardLevelConfigWithPrivilegeDto configDto = cardConfigMedConverter.convertPoToDto(cardLevel);
            CardConfigInfo cardConfigInfoDto = cardConfigInfoDtoMap.get(cardLevel.getCardId());
            if (cardConfigInfoDto != null) {
                configDto.setCardName(cardConfigInfoDto.getCardName());
            }
            String key = String.format("%s_%s", configDto.getCardId(), configDto.getCardLevel());
            List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigInfos = cardLevelPrivilegeMap.get(key);
            if (CollectionUtils.isNotEmpty(cardLevelPrivilegeConfigs)) {
                List<PrivilegeConfigDto> privilegeConfigs = buildPrivilegeConfig(cardLevelPrivilegeConfigInfos, privilegeConfigMap, privilegeMappingMap);
                configDto.setPrivileges(privilegeConfigs);
            }
            return configDto;
        }).collect(Collectors.toList());
    }

    @Override
    public void actionCardLevel(ActionCardLevelDto dto) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
        if (cardConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20011);
        }
        CardLevelConfigInfo cardLevelConfigInfo = cardLevelConfigBiz.getByCardLevel(dto.getCardId(), dto.getCareLevel());
        if (cardLevelConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20003);
        }
        // 如果是停用，校验有没有会员
        if (dto.getState().equals(StatusEnum.INVALID.getStatus())
                && memberCardMedService.checkHasCard(dto.getMasterType(), dto.getMasterCode(), dto.getCardId(), dto.getCareLevel())) {
            throw new ServiceException(RespCodeEnum.CONFIG_20016);
        }
        cardLevelConfigInfo.setState(dto.getState()).setModifyUser(dto.getOperator());
        cardLevelConfigBiz.update(cardLevelConfigInfo);
    }

    @Override
    public List<CardConfigDto> listCardConfig(Integer masterType, String masterCode) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(masterType, masterCode, null);
        if (CollectionUtils.isEmpty(cardConfigInfos)) {
            return Lists.newArrayList();
        }
        List<Long> cardIds = cardConfigInfos.stream().map(CardConfigInfo::getId).collect(Collectors.toList());
        return listCardConfig(masterType, masterCode, cardIds);
    }

    private static ScopeValueDto buildScopeValue(CardApplicableDataMapping item, Map<String, HotelBaseInfoResp> hotelMap) {
        ScopeValueDto scopeValue = new ScopeValueDto();
        scopeValue.setValue(item.getScopeValue());
        scopeValue.setName(hotelMap.get(item.getScopeValue()).getHotelName());
        scopeValue.setBrandCode(hotelMap.get(item.getScopeValue()).getBrandCode());
        return scopeValue;
    }

    @Override
    public CardConfigDto getCardConfig(Integer masterType, String masterCode, Long cardId) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(masterType, masterCode, cardId);
        if (cardConfigInfo == null) {
            return null;
        }
        // 适用门店
        List<CardApplicableDataMapping> cardApplicableDataMappings = cardConfigBiz.lisApplicableDataMapping(cardConfigInfo.getId());
        CardConfigDto cardConfigDto = cardConfigMedConverter.convertPoToDto(cardConfigInfo);
        // 酒店信息
        List<HotelBaseInfoResp> hotelBaseInfoList = hotelDecorator.listHotelBaseInfos(cardConfigDto.getBlocCode(), null, null, null);
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, Function.identity()));
        cardConfigDto.setScopeValues(cardApplicableDataMappings.stream().map(item -> buildScopeValue(item, hotelMap)).collect(Collectors.toList()));
        // 卡生成规则
        CardNoRuleInfo cardNoRuleInfo = cardNoRuleBiz.getByCardId(cardConfigInfo.getId());
        cardConfigDto.setCardNoRule(cardConfigMedConverter.convertPoToDto(cardNoRuleInfo));
        // 卡等级
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto().setMasterType(masterType).setMasterCode(masterCode).setCardId(cardConfigDto.getId());
        List<CardLevelConfigWithPrivilegeDto> privileges = listCardLevelConfig(levelConfigDto);
        cardConfigDto.setCardLevelConfigs(privileges);
        return cardConfigDto;
    }


    @Override
    public CardFullConfigDto getCardFullConfig(Integer masterType, String masterCode, Long cardId) {
        CardConfigDto memberCardConfig = getCardConfig(masterType, masterCode, cardId);
        if (memberCardConfig == null) {
            return null;
        }
        List<Long> cardIds = Collections.singletonList(cardId);
        // 查询卡关联等级关联的权益列表
        List<CardLevelPrivilegeConfigInfo> privilegeConfigInfoList = cardLevelPrivilegeConfigBiz.listByCardIds(cardIds);
        List<PrivilegeConfigInfo> privilegeConfigInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(privilegeConfigInfoList)) {
            List<Long> privilegeIds = privilegeConfigInfoList.stream().map(CardLevelPrivilegeConfigInfo::getPrivilegeId).collect(Collectors.toList());
            privilegeConfigInfos.addAll(privilegeConfigBiz.listByIds(privilegeIds));
        }
        // 查询卡升级规则
        ListCardLevelUpgradeRuleDto upgradeRuleDto = new ListCardLevelUpgradeRuleDto();
        upgradeRuleDto.setMasterType(masterType).setMasterCode(masterCode).setCardIds(cardIds);
        List<CardLevelUpgradeRuleDto> cardLevelUpgradeRules = listCardLevelUpgradeRule(upgradeRuleDto);
        // 查询卡保级规则
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto().setMasterType(masterType)
                .setMasterCode(masterCode).setCardIds(cardIds);
        List<CardLevelRelegationRuleDto> cardLevelRelegationRules = listCardLevelRelegationRule(relegationRuleDto);
        // 酒店信息
        return cardConfigMedConverter.convertCardFullConfig(Collections.singletonList(memberCardConfig), privilegeConfigInfoList, privilegeConfigInfos,
                cardLevelUpgradeRules, cardLevelRelegationRules).get(0);
    }

    @Override
    public List<CardFullConfigDto> listCardFullConfig(Integer masterType, String masterCode, List<Long> cardIds) {
        List<CardConfigDto> memberCardConfigs = listCardConfig(masterType, masterCode, cardIds);
        if (CollectionUtils.isEmpty(memberCardConfigs)) {
            return Lists.newArrayList();
        }
        // 查询卡关联等级关联的权益列表
        List<CardLevelPrivilegeConfigInfo> privilegeConfigInfoList = cardLevelPrivilegeConfigBiz.listByCardIds(cardIds);
        List<PrivilegeConfigInfo> privilegeConfigInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(privilegeConfigInfoList)) {
            List<Long> privilegeIds = privilegeConfigInfoList.stream().map(CardLevelPrivilegeConfigInfo::getPrivilegeId).collect(Collectors.toList());
            privilegeConfigInfos.addAll(privilegeConfigBiz.listByIds(privilegeIds));
        }
        // 查询卡升级规则
        ListCardLevelUpgradeRuleDto upgradeRuleDto = new ListCardLevelUpgradeRuleDto().setMasterType(masterType).setMasterCode(masterCode).setCardIds(cardIds);
        List<CardLevelUpgradeRuleDto> cardLevelUpgradeRules = listCardLevelUpgradeRule(upgradeRuleDto);
        // 查询卡保级规则
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto().setMasterType(masterType).setMasterCode(masterCode).setCardIds(cardIds);
        List<CardLevelRelegationRuleDto> cardLevelRelegationRules = listCardLevelRelegationRule(relegationRuleDto);
        // 酒店信息
        return cardConfigMedConverter.convertCardFullConfig(memberCardConfigs, privilegeConfigInfoList, privilegeConfigInfos, cardLevelUpgradeRules, cardLevelRelegationRules);
    }

    @Override
    public CardLevelUpgradeRuleDto getCardLevelUpgradeRule(GetCardLevelUpgradeRuleDto dto) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
        if (Objects.isNull(cardConfigInfos)) {
            return null;
        }
        CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo = cardLevelUpgradeRuleBiz.getByCardLevel(dto.getCardId(), dto.getMemberCardLevel());
        if (Objects.isNull(cardLevelUpgradeRuleInfo)) {
            return null;
        }
        List<CardLevelUpgradeRuleDetailInfo> details = cardLevelUpgradeRuleBiz.listDetailByRuleId(cardLevelUpgradeRuleInfo.getId());
        CardLevelUpgradeRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(cardLevelUpgradeRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public List<CardLevelUpgradeRuleDto> listCardLevelUpgradeRule(ListCardLevelUpgradeRuleDto dto) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByCardIds(dto.getMasterType(), dto.getMasterCode(), dto.getCardIds());
        if (CollectionUtils.isEmpty(cardConfigInfos)) {
            return Lists.newArrayList();
        }
        List<CardLevelUpgradeRuleInfo> cardLevelUpgradeRuleInfos = cardLevelUpgradeRuleBiz.listByCardIds(dto.getCardIds(), dto.getState());
        if (CollectionUtils.isEmpty(cardLevelUpgradeRuleInfos)) {
            return Lists.newArrayList();
        }
        List<Long> ruleIds = cardLevelUpgradeRuleInfos.stream().map(CardLevelUpgradeRuleInfo::getId).collect(Collectors.toList());
        List<CardLevelUpgradeRuleDetailInfo> details = cardLevelUpgradeRuleBiz.listDetailByRuleIds(ruleIds);
        Map<Long, List<CardLevelUpgradeRuleDetailInfo>> cardLevelUpgradeRuleMap = details.stream().collect(Collectors.groupingBy(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId));
        return cardLevelUpgradeRuleInfos.stream().map(rule -> {
            CardLevelUpgradeRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(rule);
            List<CardLevelUpgradeRuleDetailInfo> ruleDetailInfos = cardLevelUpgradeRuleMap.getOrDefault(rule.getId(), Lists.newArrayList());
            ruleDto.setDetails(ruleDetailInfos.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            return ruleDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Pageable<CardLevelUpgradeRuleDto> pageUpgradeRule(PageCardLevelUpgradeRuleDto dto) {
        PageCardLevelUpgradeRuleBo pageBo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<CardLevelUpgradeRuleInfo> page = cardLevelUpgradeRuleBiz.pageUpgradeRule(pageBo);
        List<CardLevelUpgradeRuleInfo> ruleInfos = page.getRecords();
        if (CollectionUtils.isEmpty(ruleInfos)) {
            return Pageable.empty();
        }
        List<Long> cardIds = ruleInfos.stream().map(CardLevelUpgradeRuleInfo::getCardId).distinct().collect(Collectors.toList());
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(dto.getMasterType(), dto.getMasterCode(), cardIds);
        List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelConfigBiz.listByCardIds(dto.getMasterType(), dto.getMasterCode(), cardIds);
        Map<String, String> cardLevelMap = cardLevelConfigInfos.stream().collect(Collectors.toMap(e -> String.format("%s_%s", e.getCardId(), e.getCardLevel()), CardLevelConfigInfo::getCardLevelName));
        List<CardLevelUpgradeRuleDto> respList = cardConfigMedConverter.convertUpgradeRuleResp(ruleInfos, cardConfigInfos, cardLevelMap);
        return PageableUtil.convert(page, respList);
    }

    @Override
    public CardLevelUpgradeRuleDto getUpgradeRule(Integer masterType, String masterCode, Long id) {
        CardLevelUpgradeRuleInfo upgradeRuleInfo = cardLevelUpgradeRuleBiz.getById(masterType, masterCode, id);
        if (Objects.isNull(upgradeRuleInfo)) {
            log.error("upgrade rule is not exist , id :{}", id);
            return null;
        }
        List<CardLevelUpgradeRuleDetailInfo> details = cardLevelUpgradeRuleBiz.listDetailByRuleId(upgradeRuleInfo.getId());
        CardLevelUpgradeRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(upgradeRuleInfo);
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(masterType, masterCode, ruleDto.getCardId());
        ruleDto.setCardName(cardConfigInfo.getCardName());
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public CardLevelRelegationRuleDto getRelegationRule(GetCardLevelRelegationRuleDto dto) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
        if (Objects.isNull(cardConfigInfos)) {
            return null;
        }
        CardLevelRelegationRuleInfo cardLevelRelegationRuleInfo = cardLevelRelegationRuleBiz.getByCardLevel(dto.getCardId(), dto.getCardLevel());
        if (Objects.isNull(cardLevelRelegationRuleInfo)) {
            return null;
        }
        List<CardLevelRelegationRuleDetailInfo> details = cardLevelRelegationRuleBiz.listDetailByRuleId(cardLevelRelegationRuleInfo.getId());
        CardLevelRelegationRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(cardLevelRelegationRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public List<CardLevelRelegationRuleDto> listCardLevelRelegationRule(ListCardLevelRelegationRuleDto dto) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByCardIds(dto.getMasterType(), dto.getMasterCode(), dto.getCardIds());
        if (CollectionUtils.isEmpty(cardConfigInfos)) {
            return Lists.newArrayList();
        }
        List<CardLevelRelegationRuleInfo> cardLevelRelegationRuleInfos = cardLevelRelegationRuleBiz.listByCardIds(dto.getCardIds(), dto.getState());
        if (CollectionUtils.isEmpty(cardLevelRelegationRuleInfos)) {
            return Lists.newArrayList();
        }
        List<Long> ruleIds = cardLevelRelegationRuleInfos.stream().map(CardLevelRelegationRuleInfo::getId).collect(Collectors.toList());
        List<CardLevelRelegationRuleDetailInfo> details = cardLevelRelegationRuleBiz.listDetailByRuleIds(ruleIds);
        Map<Long, List<CardLevelRelegationRuleDetailInfo>> cardLevelRelegationRuleMap = details.stream().collect(Collectors.groupingBy(CardLevelRelegationRuleDetailInfo::getRelegationRuleId));
        return cardLevelRelegationRuleInfos.stream().map(rule -> {
            CardLevelRelegationRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(rule);
            List<CardLevelRelegationRuleDetailInfo> ruleDetailInfos = cardLevelRelegationRuleMap.getOrDefault(rule.getId(), Lists.newArrayList());
            ruleDto.setDetails(ruleDetailInfos.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            return ruleDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Pageable<CardLevelRelegationRuleDto> pageRelegationRule(PageCardLevelRelegationRuleDto dto) {
        PageCardLevelRelegationRuleBo pageBo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<CardLevelRelegationRuleInfo> page = cardLevelRelegationRuleBiz.pageRelegationRule(pageBo);
        List<CardLevelRelegationRuleInfo> ruleInfos = page.getRecords();
        if (CollectionUtils.isEmpty(ruleInfos)) {
            return Pageable.empty();
        }
        List<Long> cardIds = ruleInfos.stream().map(CardLevelRelegationRuleInfo::getCardId).distinct().collect(Collectors.toList());
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(dto.getMasterType(), dto.getMasterCode(), cardIds);
        List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelConfigBiz.listByCardIds(dto.getMasterType(), dto.getMasterCode(), cardIds);
        Map<String, String> cardLevelMap = cardLevelConfigInfos.stream().collect(Collectors.toMap(e -> String.format("%s_%s", e.getCardId(), e.getCardLevel()), CardLevelConfigInfo::getCardLevelName));
        List<CardLevelRelegationRuleDto> respList = cardConfigMedConverter.convertRelegationRuleResp(ruleInfos, cardConfigInfos, cardLevelMap);
        return PageableUtil.convert(page, respList);
    }

    @Override
    public CardLevelRelegationRuleDto getRelegationRule(Integer masterType, String masterCode, Long id) {
        CardLevelRelegationRuleInfo relegationRuleInfo = cardLevelRelegationRuleBiz.getById(masterType, masterCode, id);
        if (Objects.isNull(relegationRuleInfo)) {
            log.error("relegation rule is not exist , id :{}", id);
            return null;
        }
        List<CardLevelRelegationRuleDetailInfo> details = cardLevelRelegationRuleBiz.listDetailByRuleId(relegationRuleInfo.getId());
        CardLevelRelegationRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(relegationRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public void deleteUpgradeRule(DeleteUpgradeRuleDto dto) {
        CardLevelUpgradeRuleInfo ruleInfo = cardLevelUpgradeRuleBiz.getById(dto.getMasterType(), dto.getMasterCode(), dto.getRuleId());
        if (ruleInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20012);
        }
        cardLevelUpgradeRuleBiz.deleteByRuleId(dto.getRuleId(), dto.getOperator());
    }

    @Override
    public void actionUpgradeRule(ActionUpgradeRuleDto dto) {
        Integer state = dto.getState();
        Long ruleId = dto.getRuleId();
        String operator = dto.getOperator();
        CardLevelUpgradeRuleInfo ruleInfo = cardLevelUpgradeRuleBiz.getById(dto.getMasterType(), dto.getMasterCode(), ruleId);
        if (ruleInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20012);
        }
        if (state.equals(StatusEnum.VALID.getStatus())) {
            List<CardLevelUpgradeRuleInfo> cardLevelUpgradeRuleInfos = cardLevelUpgradeRuleBiz.listByCardId(ruleInfo.getCardId());
            if (CollectionUtils.isNotEmpty(cardLevelUpgradeRuleInfos)) {
                if (cardLevelUpgradeRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()) && !item.getId().equals(ruleId))
                        .anyMatch(item -> item.getTargetLevel().equals(ruleInfo.getTargetLevel()) && item.getSourceLevel().equals(ruleInfo.getSourceLevel()))) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10024);
                }
            }
        }
        cardLevelUpgradeRuleBiz.updateState(ruleId, state, operator);
    }

    @Override
    public void deleteRelegationRule(DeleteRelegationRuleDto dto) {
        CardLevelRelegationRuleInfo ruleInfo = cardLevelRelegationRuleBiz.getById(dto.getMasterType(), dto.getMasterCode(), dto.getRuleId());
        if (ruleInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20012);
        }
        cardLevelRelegationRuleBiz.deleteByRuleId(dto.getRuleId(), dto.getOperator());
    }

    @Override
    public void actionRelegationRule(ActionRelegationRuleDto dto) {
        Integer state = dto.getState();
        Long ruleId = dto.getRuleId();
        String operator = dto.getOperator();
        CardLevelRelegationRuleInfo ruleInfo = cardLevelRelegationRuleBiz.getById(dto.getMasterType(), dto.getMasterCode(), ruleId);
        if (ruleInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20013);
        }
        if (state.equals(StatusEnum.VALID.getStatus())) {
            List<CardLevelRelegationRuleInfo> cardLevelRelegationRuleInfos = cardLevelRelegationRuleBiz.listByCardId(ruleInfo.getCardId());
            if (CollectionUtils.isNotEmpty(cardLevelRelegationRuleInfos)) {
                if (cardLevelRelegationRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()) && !item.getId().equals(ruleId))
                        .anyMatch(item -> item.getTargetLevel().equals(ruleInfo.getTargetLevel()) && item.getSourceLevel().equals(ruleInfo.getSourceLevel()))) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10025);
                }
            }
        }
        cardLevelRelegationRuleBiz.updateState(ruleId, state, operator);
    }

    @Override
    public List<CardLevelPrivilegeConfigDto> listCardLevelPrivilege(ListCardLevelPrivilegeDto req) {
        List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getCardIdList())) {
            cardLevelPrivilegeConfigInfos = cardLevelPrivilegeConfigBiz.listByCardIds(req.getCardIdList());
        } else {
            cardLevelPrivilegeConfigInfos = cardLevelPrivilegeConfigBiz.listByCardIdAndLevel(req.getCardId(), req.getCardLevel());
        }
        if (CollectionUtils.isEmpty(cardLevelPrivilegeConfigInfos)) {
            return Lists.newArrayList();
        }
        List<Long> privilegeIds = cardLevelPrivilegeConfigInfos.stream().map(CardLevelPrivilegeConfigInfo::getPrivilegeId).collect(Collectors.toList());
        List<PrivilegeApplicableDataMapping> mappingDtoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(req.getScopeValue())) {
            mappingDtoList = privilegeConfigBiz.listApplicableDataMapping(privilegeIds, req.getScopeValue());
            if (CollectionUtils.isEmpty(mappingDtoList)) {
                return Lists.newArrayList();
            }
            privilegeIds = mappingDtoList.stream().map(PrivilegeApplicableDataMapping::getPrivilegeId).collect(Collectors.toList());
        }
        Map<Long, List<PrivilegeApplicableDataMapping>> applicableDataMappingMap = mappingDtoList.stream().collect(Collectors.groupingBy(PrivilegeApplicableDataMapping::getPrivilegeId));

        List<PrivilegeConfigInfo> privilegeConfigInfoDtoList = privilegeConfigBiz.listByIds(privilegeIds, req.getState());
        Map<Long, PrivilegeConfigInfo> privilegeMap = privilegeConfigInfoDtoList.stream().collect(Collectors.toMap(PrivilegeConfigInfo::getId, d -> d));
        return cardLevelPrivilegeConfigInfos.stream().map(info -> {
            CardLevelPrivilegeConfigDto configDto = cardConfigMedConverter.convertPoToDto(info);
            PrivilegeConfigInfo configInfo = privilegeMap.get(configDto.getPrivilegeId());
            if (configInfo != null) {
                configDto.setName(configInfo.getName());
                configDto.setType(configInfo.getType());
                configDto.setDescription(configInfo.getDescription());
                configDto.setInstruction(configInfo.getInstruction());
                configDto.setState(configInfo.getState());
            }
            List<PrivilegeApplicableDataMapping> mappings = applicableDataMappingMap.get(configDto.getPrivilegeId());
            if (CollectionUtils.isNotEmpty(mappings)) {
                PrivilegeApplicableDataMapping applicableDataMapping = mappings.get(0);
                configDto.setValue(applicableDataMapping.getValue());
                configDto.setClassification(applicableDataMapping.getClassification());
            }
            return configDto;
        }).collect(Collectors.toList());
    }

    /**
     * 权益校验
     *
     * @param dto
     */
    public void checkPrivilege(SaveCardLevelConfigDto dto) {
        List<SaveCardLevelPrivilegeConfigDto> privilegeConfigs = dto.getPrivilegeConfigs();
        if (CollectionUtils.isEmpty(privilegeConfigs)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20001);
        }
        List<Long> privilegeIds = privilegeConfigs.stream().map(SaveCardLevelPrivilegeConfigDto::getPrivilegeId).collect(Collectors.toList());
        List<PrivilegeConfigInfo> privilegeConfigInfos = privilegeConfigBiz.listByIds(privilegeIds);
        if (CollectionUtils.isEmpty(privilegeConfigInfos)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20001);
        }
        List<PrivilegeConfigInfo> priceRelated = privilegeConfigInfos.stream().filter(item ->
                item.getType().equals(PrivilegeTypeEnum.PRICE.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(priceRelated)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20002);
        }
        if (priceRelated.stream().noneMatch(item -> item.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()))) {
            throw new ServiceException(RespCodeEnum.CONFIG_20002);
        }
    }


    private List<PrivilegeConfigDto> buildPrivilegeConfig(List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigs,
                                                          Map<Long, PrivilegeConfigInfo> privilegeConfigMap,
                                                          Map<Long, List<PrivilegeApplicableDataMapping>> privilegeMappingMap) {
        return cardLevelPrivilegeConfigs.stream().map(item -> {
            PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigMap.get(item.getPrivilegeId());
            List<PrivilegeApplicableDataMapping> mappings = privilegeMappingMap.get(item.getPrivilegeId());
            return cardConfigMedConverter.convertPoToDto(privilegeConfigInfo, mappings);
        }).collect(Collectors.toList());
    }

    private Map<Long, List<PrivilegeApplicableDataMapping>> mapPrivilegeMapping(List<Long> privilegeIds) {
        List<PrivilegeApplicableDataMapping> applicableDataMappings = privilegeConfigBiz.listMappingByIds(privilegeIds);
        return applicableDataMappings.stream().collect(Collectors.groupingBy(PrivilegeApplicableDataMapping::getPrivilegeId));
    }

    private Map<Long, PrivilegeConfigInfo> mapPrivilegeMap(List<Long> privilegeIds) {
        List<PrivilegeConfigInfo> privilegeConfigInfos = privilegeConfigBiz.listByIds(privilegeIds);
        return privilegeConfigInfos.stream().collect(Collectors.toMap(PrivilegeConfigInfo::getId, Function.identity()));
    }

}
