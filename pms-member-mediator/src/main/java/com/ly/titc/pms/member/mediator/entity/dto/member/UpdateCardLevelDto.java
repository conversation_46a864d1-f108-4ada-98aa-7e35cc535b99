package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 修改等级
 *
 * <AUTHOR>
 * @title: ModifyLevelDto
 * @projectName pms-member
 * @description: 修改等级
 * @date 2023/11/20 19:44
 */
@Data
@Accessors(chain = true)
public class UpdateCardLevelDto {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 卡ID
     */
    private Long cardId;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 变更前等级
     */
    private Integer preLevel;

    /**
     * 变更前等级名称
     */
    private String preLevelName;

    /**
     * 变更后等级
     */
    private Integer afterLevel;

    /**
     * 变更后等级名称
     */
    private String afterLevelName;

    /**
     * 生效日期
     */
    private String effectBeginDate;

    /**
     * 失效日期
     */
    private String effectEndDate;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 等级变化类型，1注册; 2升级；3保级成功；4保级失败; 5手动处理; 6迁移数据
     */
    private Integer changeType;

    /**
     * 原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    private String bizType;

    private String bizNo;

    private Long ruleId;

    /**
     * “来源类型（集团、门店）
     */
    private String sourceType;

    /**
     * 门店
     */
    private String sourceHotel;

}
