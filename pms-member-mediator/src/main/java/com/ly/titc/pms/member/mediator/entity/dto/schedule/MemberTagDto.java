package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberTagDto
 * @Date：2024-11-21 14:46
 * @Filename：MemberTagDto
 */
@Data
public class MemberTagDto extends ScheduleDataDto {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签分类
     */
    private Integer type;

    /**
     * 打标分类 1 自动打标 2 手动达标
     */
    private Integer markType;


    /**
     * 自动删除 0 否 1 是
     */
    private Integer autoDelete;

    /**
     * 满足条件 ALL - 全部  ANY-满足任何一个条件
     */
    private String satisfyPerformType;

    /**
     * 标签描述
     */
    private String remark;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 状态；0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 标签规则
     */
    private List<MemberTagDetailDto> markRules;
}
