package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.region.RegionResp;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.BindMemberReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.RegisterCustomerReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.dal.entity.bo.PageMemberParamBo;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.LevelChangeTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.entity.wrapper.MemberIssueCardWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberRegisterWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberSaveWrapper;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.converter.MemberMedConverter;
import com.ly.titc.pms.member.mediator.converter.RegisterMemberConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.data.CheckInStatisticsDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.PageMemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import com.ly.titc.pms.member.mediator.handler.check.AbstractRegisterCheckHandler;
import com.ly.titc.pms.member.mediator.handler.es.ElasticsearchHandler;
import com.ly.titc.pms.member.mediator.manager.RegisterCheckHandlerManager;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.RegionDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import com.ly.titc.pms.member.mediator.service.*;
import com.ly.titc.pms.member.mediator.strategy.MemberCardIssueStrategyFactory;
import com.ly.titc.pms.member.service.MemberService;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.StatisticsCouponReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.CouponStatisticsResp;
import com.ly.titc.springboot.dcdb.dal.core.encrypt.EncryptService;
import com.ly.titc.springboot.dcdb.dal.core.service.CombinedService;
import com.ly.titc.springboot.elasticsearch.client.ElasticsearchClient;
import com.ly.titc.springboot.elasticsearch.entity.request.SearchRequest;
import com.ly.titc.springboot.elasticsearch.entity.result.DataResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.EsTemplateConstants.PAGE_MEMBER_TEMPLATE;
import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10011;
import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10033;

/**
 * 会员Med服务实现
 *
 * <AUTHOR>
 * @date 2024/10/28 19:52
 */
@Slf4j
@Service
public class MemberMedServiceImpl implements MemberMedService {

    @Resource
    private MemberInfoBiz memberInfoBiz;
    @Resource
    private MemberExtendInfoBiz memberExtendInfoBiz;
    @Resource
    private MemberContactInfoBiz memberContactInfoBiz;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private MemberMedConverter memberMedConverter;
    @Resource
    private MemberCardMedConverter memberCardMedConverter;
    @Resource
    private MemberCardMedService memberCardMedService;
    @Resource
    private MessageMedService messageMedService;
    @Resource
    private MemberService memberService;
    @Resource
    private ElasticsearchHandler elasticsearchHandler;
    @Resource
    private RedisFactory redisFactory;
    @Resource(type = RegionDecorator.class)
    private RegionDecorator regionDecorator;
    @Resource
    private MemberProfileMedService memberProfileMedService;
    @Resource
    private CustomerDecorator customerDecorator;
    @Resource(type = CombinedService.class)
    private CombinedService combinedService;
    @Resource
    private EncryptService encryptService;
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource
    private MemberDataRecordMedService memberDataRecordMedService;
    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;
    @Resource
    private CardConfigMedService cardConfigMedService;
    @Resource(type = AssetDecorator.class)
    private AssetDecorator assetDecorator;
    @Resource(type = CouponDecorator.class)
    private CouponDecorator couponDecorator;
    @Resource
    private MemberCardIssueStrategyFactory memberCardIssueStrategyFactory;
    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Override
    public RegisterMemberResultDto register(RegisterMemberDto dto) {
        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode(), traceNo = TraceNoUtil.getTraceNo(), mobile = dto.getMobile();
        String idempotentKey = CommonUtil.concat(CommonConstant.REGISTER_IDEMPOTENT_PREFIX, mobile);
        Boolean result = redisFactory.setNx(idempotentKey, 6, mobile);
        //处理中...
        if (!result) {
            log.warn("this mobile register is processing...trackingId:{};mobile:{}", traceNo, mobile);
            throw new ServiceException(RespCodeEnum.MEMBER_10000);
        }
        try {
            // 注册校验
            registerCheck(dto);
            String memberNo = memberService.getMemberNo(masterType, masterCode);
            combinedService.newTransactionRequiredNew((arg) -> {
                // 注册会员
                MemberRegisterWrapper wrapper = memberMedConverter.convertDtoToWrapper(dto, memberNo);
                fillRegionInfo(wrapper.getMemberContactInfo());
                memberService.register(wrapper);
                // 发放会员卡
                MemberIssueCardWrapper issueWrapper = assembleIssueCardWrapper(dto, memberNo);
                memberService.issueCard(issueWrapper);
                // 如果客户转会员，则调用客户服务关连会员编号及合并资产
                CustomerInfoDto customerInfo = dto.getCustomerInfo();
                if (customerInfo != null) {
                    BindMemberReq req = memberMedConverter.convertDtoToReq(customerInfo, memberNo);
                    customerDecorator.bindMember(req);
                } else {
                    RegisterCustomerReq registerCustomerReq = memberMedConverter.convertRegisterCustomerReq(wrapper.getMemberInfo());
                    customerDecorator.register(registerCustomerReq);
                }
                return null;
            });

            // 发送事件消息
            MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(masterType, masterCode, memberNo, MemberEventEnum.REGISTER);
            messageMedService.sendMemberEventMsg(traceNo, memberEventMsg);

            return new RegisterMemberResultDto().setMemberNo(memberNo).setMemberCardNo(dto.getMemberCardInfo().getMemberCardNo());
        } catch (ServiceException e) {
            log.error("会员注册失败, 归属类型:{}, 归属值:{}, 手机号:{}", masterType, masterCode, mobile, e);
            throw e;
        } catch (Exception e) {
            log.error("会员注册失败, 归属类型:{}, 归属值:{}, 手机号:{}", masterType, masterCode, mobile, e);
            sendRegisterFailOaContext(traceNo, masterType, masterCode, mobile, e.getMessage());
            throw new ServiceException(RespCodeEnum.MEMBER_10016);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void registerCheck(RegisterMemberDto dto) {
        for (MemberRegisterCheckEnum value : MemberRegisterCheckEnum.values()) {
            AbstractRegisterCheckHandler handler = RegisterCheckHandlerManager.getHandler(value.getAction());
            if (Objects.nonNull(handler)) {
                handler.check(dto);
            }
        }
    }

    public List<MemberDetailDto> listByMobiles(Integer masterType, String masterCode, List<String> mobiles, Integer state) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByMobiles(masterType, masterCode, mobiles, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = memberInfos.stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
        return listByMemberNos(masterType, masterCode, memberNos);
    }

    @Override
    public List<MemberDetailDto> listByIdNos(Integer masterType, String masterCode, Integer idType, List<String> idNos, Integer state) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByIdNos(masterType, masterCode, idType, idNos, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = memberInfos.stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
        return listByMemberNos(masterType, masterCode, memberNos);
    }

    @Override
    public List<MemberDetailDto> listByCardNos(Integer masterType, String masterCode, List<String> cardNos, Integer state) {
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByCardNos(masterType, masterCode, cardNos);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = memberCardInfos.stream().map(MemberCardInfoDto::getMemberNo).collect(Collectors.toList());
        Map<String, List<MemberCardInfoDto>> memberCardInfoMap = memberCardInfos.stream().collect(Collectors.groupingBy(MemberCardInfoDto::getMemberNo));
        List<MemberDetailDto> memberInfos = listByMemberNos(masterType, masterCode, memberNos);
        if (state != null) {
            return memberInfos.stream().filter(member -> member.getState().equals(state)).collect(Collectors.toList());
        }
        memberInfos.forEach(member -> member.setMemberCardInfos(memberCardInfoMap.get(member.getMemberNo())));
        return memberInfos;
    }

    @Override
    public MemberDetailDto getDetailByMemberNo(Integer masterType, String masterCode, String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            return null;
        }
        MemberExtendInfo memberExtendInfo = memberExtendInfoBiz.getByMemberNo(memberNo);
        MemberContactInfo memberContactInfo = memberContactInfoBiz.getByMemberNo(memberNo);
        // 会员标签
        List<MemberProfileTagInfoDto> memberProfileTagInfos = memberProfileMedService.listMemberTag(masterType, masterCode, memberNo);
        List<String> tagNames = memberProfileTagInfos.stream().map(MemberProfileTagInfoDto::getTagName).collect(Collectors.toList());
        // 会员卡
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNo(masterType, masterCode, memberNo);
        // 对象转换
        MemberDetailDto memberDetailDto = memberMedConverter.convertPoToDto(memberInfo, memberExtendInfo, memberContactInfo, tagNames, memberCardInfos);
        // 酒店
        Map<String, HotelBaseInfoResp> hotelMap = mapHotel(memberInfo.getBlocCode());
        // 查询会员入住次数
        CheckInStatisticsDto checkInStatistics = memberDataRecordMedService.getCheckInStatistics(memberInfo.getMemberNo());
        // 黑名单
        boolean blacklistFlag = memberBlacklistMedService.checkWhetherBlacklist(memberNo, null, null);
        // 卡配置
        Map<Long, CardConfigDto> cardConfigMap = mapCardConfig(masterType, masterCode, memberCardInfos);
        memberMedConverter.fillCardConfig(memberDetailDto, cardConfigMap, hotelMap, checkInStatistics, blacklistFlag);
        return memberDetailDto;
    }

    @Override
    public MemberDefaultCardFullInfoDto getUsableMemberInfo(GetUsableMemberDto dto) {
        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode();
        String memberNo = dto.getMemberNo();
        //会员基础信息
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            return null;
        }
        // 会员卡（多张卡）
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNo(masterType, masterCode, memberNo);
        // 获取默认卡信息
        Long cardId = null;
        if (StringUtils.isEmpty(dto.getMemberCardNo())) {
            CardConfigDto defaultConfigResp = cardConfigMedService.getDefaultCard(dto.getMasterType(), dto.getMasterCode());
            if (defaultConfigResp == null) {
                log.error("会员默认卡配置不存在, 归属类型:{}, 归属值:{}", masterType, masterCode);
                throw new ServiceException(RespCodeEnum.MEMBER_10013);
            }
            cardId = defaultConfigResp.getId();
        }else {
            memberCardInfos = memberCardInfos.stream().filter(item -> item.getMemberCardNo().equals(dto.getMemberCardNo())).collect(Collectors.toList());
            if (memberCardInfos.isEmpty()) {
                log.error("会员卡不存在, 会员号:{}, 卡号:{}", memberNo, dto.getMemberCardNo());
                throw new ServiceException(MEMBER_10033);
            }
            cardId =  memberCardInfos.get(0).getCardId();
        }
        //获取会员的卡信息
        Long finalCardId = cardId;
        MemberCardInfoDto memberCardInfo = memberCardInfos.stream().filter(item -> item.getCardId().equals(finalCardId)).findFirst().orElse(null);
        if(memberCardInfo == null){
            log.error("会员卡不存在, 会员号:{}, 卡号:{}", memberNo, finalCardId);
            throw new ServiceException(MEMBER_10033);
        }
        //获取会员卡适用的指定的scopeCode和指定等级对应的权益
        ListCardLevelPrivilegeDto privilegeDto = new ListCardLevelPrivilegeDto();
        privilegeDto.setCardId(finalCardId);
        privilegeDto.setCardLevel(memberCardInfo.getCardLevel());
        privilegeDto.setState(StateEnum.VALID.getState());
        privilegeDto.setScopeValue(dto.getScopeMasterCode());
        List<CardLevelPrivilegeConfigDto> privilegeConfig = cardConfigMedService.listCardLevelPrivilege(privilegeDto);
        // 黑名单
        boolean blacklistFlag = memberBlacklistMedService.checkWhetherBlacklist(memberNo, null, null);
        //查询会员储值资产
        GetMemberUsableReq usableReq = memberMedConverter.convert(dto);
        MemberStoreAccountResp storeAccountResp = new MemberStoreAccountResp();
        try {
             storeAccountResp = assetDecorator.getStoreUsableMasterAccount(usableReq);
        }catch (Exception e){
            log.error("查询会员储值资产失败, 会员号:{}, 归属类型:{}, 归属值:{}, 跟踪号:{}", memberNo, masterType, masterCode, dto.getTrackingId(), e);
        }
        MemberPointAccountResp pointAccountResp = new MemberPointAccountResp();
        try {
            pointAccountResp = assetDecorator.getPointUsableMasterAccount(usableReq);
        }catch (Exception e){
            log.error("查询会员积分资产失败, 会员号:{}, 归属类型:{}, 归属值:{}, 跟踪号:{}", memberNo, masterType, masterCode, dto.getTrackingId(), e);
        }
        //查询可用优惠券数量
        CouponStatisticsResp couponStatisticsResp = new CouponStatisticsResp();
        try {
            StatisticsCouponReq req = memberMedConverter.convertCouReq(dto);
            couponStatisticsResp = couponDecorator.statisticsCoupon(req);
        }catch (Exception e){
            log.error("查询会员优惠券数量失败, 会员号:{}, 归属类型:{}, 归属值:{}, 跟踪号:{}", memberNo, masterType, masterCode, dto.getTrackingId(), e);
        }
        //组装返回
        MemberDefaultCardFullInfoDto cardFullInfoDto = memberMedConverter.convert(memberInfo, memberCardInfo, storeAccountResp,
                pointAccountResp, couponStatisticsResp, privilegeConfig);
        cardFullInfoDto.setOrderPrivileges(memberMedConverter.orderPrivileges(privilegeConfig));
        cardFullInfoDto.setBlackFlag(blacklistFlag ? Constant.ONE : Constant.ZERO);
        return cardFullInfoDto;
    }

    @Override
    public MemberInfoDto getByMemberNo(Integer masterType, String masterCode, String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
        return memberMedConverter.convertPoToDto(memberInfo);
    }

    @Override
    public MemberInfoDto getByMemberNo(String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        return memberMedConverter.convertPoToDto(memberInfo);
    }

    @Override
    public List<MemberDetailDto> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByMemberNos(masterType, masterCode, memberNos);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        // 拓展信息
        List<MemberExtendInfo> memberExtendInfos = memberExtendInfoBiz.listByMemberNos(memberNos);
        Map<String, MemberExtendInfo> memberExtendInfoMap = memberExtendInfos.stream().collect(Collectors.toMap(MemberExtendInfo::getMemberNo, Function.identity()));
        // 联系方式
        List<MemberContactInfo> memberContactInfos = memberContactInfoBiz.listByMemberNo(memberNos);
        Map<String, MemberContactInfo> contactInfoMap = memberContactInfos.stream().collect(Collectors.toMap(MemberContactInfo::getMemberNo, Function.identity()));
        // 会员标签
        List<MemberProfileTagInfoDto> memberProfileTagInfos = memberProfileMedService.listMemberTag(memberNos);
        Map<String, List<String>> memberTagNameMap = memberProfileTagInfos.stream().collect(Collectors.groupingBy(MemberProfileTagInfoDto::getMemberNo, Collectors.mapping(MemberProfileTagInfoDto::getTagName, Collectors.toList())));
        // 会员卡
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNos(masterType, masterCode, memberNos);
        Map<String, List<MemberCardInfoDto>> memberCardInfnMap = memberCardInfos.stream().collect(Collectors.groupingBy(MemberCardInfoDto::getMemberNo));
        // 酒店
        Map<String, HotelBaseInfoResp> hotelMap = mapHotel(memberInfos.get(0).getBlocCode());
        // 卡配置
        Map<Long, CardConfigDto> cardConfigMap = mapCardConfig(masterType, masterCode, memberCardInfos);
        // 入住次数
        List<CheckInStatisticsDto> checkInStatisticsList = memberDataRecordMedService.listByMemberNos(memberNos);
        Map<String, CheckInStatisticsDto> checkInMap = checkInStatisticsList.stream().collect(Collectors.toMap(CheckInStatisticsDto::getMemberNo, Function.identity()));
        // 黑名单
        List<String> blacklistMemberNos = memberBlacklistMedService.listByMemberNos(memberNos);

        return memberInfos.stream().map(memberInfo -> {
            String memberNo = memberInfo.getMemberNo();
            MemberExtendInfo memberExtendInfo = memberExtendInfoMap.get(memberNo);
            MemberContactInfo memberContactInfo = contactInfoMap.get(memberNo);
            List<String> memberTags = memberTagNameMap.getOrDefault(memberNo, Lists.newArrayList());
            List<MemberCardInfoDto> cardInfos = memberCardInfnMap.get(memberNo);
            MemberDetailDto memberDetailDto = memberMedConverter.convertPoToDto(memberInfo, memberExtendInfo, memberContactInfo, memberTags, cardInfos);
            memberMedConverter.fillCardConfig(memberDetailDto, cardConfigMap, hotelMap, checkInMap.get(memberNo), blacklistMemberNos.contains(memberNo));
            return memberDetailDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MemberIdentityBaseInfoDto> listBaseInfoByMemberNo(Integer masterType, String masterCode, List<String> memberNos) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByMemberNos(masterType, masterCode, memberNos);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        // 会员卡
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNos(masterType, masterCode, memberNos);
        Map<String, List<MemberCardInfoDto>> memberCardInfnMap = memberCardInfos.stream().collect(Collectors.groupingBy(MemberCardInfoDto::getMemberNo));
        // 卡配置
        Map<Long, CardConfigDto> cardConfigMap = mapCardConfig(masterType, masterCode, memberCardInfos);

        List<MemberIdentityBaseInfoDto> results = new ArrayList<>();
        memberInfos.forEach(memberInfo -> {
            List<MemberCardInfoDto> cardInfoDtos = memberCardInfnMap.get(memberInfo.getMemberNo());
            MemberIdentityBaseInfoDto baseInfoDto = memberMedConverter.convert(memberInfo, cardInfoDtos, cardConfigMap);
            results.add(baseInfoDto);

        });
        return results;
    }

    private Map<Long, CardConfigDto> mapCardConfig(Integer masterType, String masterCode, List<MemberCardInfoDto> memberCardInfos) {
        List<Long> cardIdList = memberCardInfos.stream().map(MemberCardInfoDto::getCardId).collect(Collectors.toList());
        List<CardConfigDto> cardConfigs = cardConfigMedService.listCardConfig(masterType, masterCode, cardIdList);
        return cardConfigs.stream().collect(Collectors.toMap(CardConfigDto::getId, Function.identity()));
    }

    @Override
    public Pageable<MemberDetailDto> pageMemberByStrongMode(PageMemberParamDto pageMemberParamDto) {

        PageMemberParamBo pageMemberParamBo = memberMedConverter.convertDtoToBo(pageMemberParamDto);
        IPage<String> iPage = memberInfoBiz.pageMember(pageMemberParamBo);
        Pageable<MemberDetailDto> pageable = Pageable.empty();
        pageable.setTotalPage((int) iPage.getPages()).setTotal((int) iPage.getTotal())
                .setCurrPage((int) iPage.getCurrent()).setDatas(Lists.newArrayList());

        List<String> memberNos = iPage.getRecords();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            List<MemberDetailDto> memberInfos = listByMemberNos(pageMemberParamDto.getMasterType(), pageMemberParamDto.getMasterCode(), memberNos);
            pageable.setDatas(memberInfos);
        }
        return pageable;
    }

    @Override
    public Pageable<MemberDetailDto> pageMemberByFinalMode(PageMemberParamDto pageMemberParamDto) {

        Integer pageIndex = pageMemberParamDto.getPageIndex();
        Integer pageSize = pageMemberParamDto.getPageSize();

        PageMemberDocumentDto request = new PageMemberDocumentDto();
        request.setClauses(buildClauses(pageMemberParamDto))
                .setCardClauses(buildCardClauses(pageMemberParamDto))
                .setTagClauses(buildTagClauses(pageMemberParamDto))
                .setFrom((pageIndex - 1) * pageSize).setSize(pageSize);
        Pageable<MemberDetailDto> pageable = new Pageable<>();
        pageable.setCurrPage(pageIndex);
        String data = JSON.toJSONString(request);
        log.info("pageByCondition, templateName:{}, condition:{}", PAGE_MEMBER_TEMPLATE, data);
        SearchRequest<MemberDocumentDto> searchRequest = elasticsearchHandler.getSearchRequest(PAGE_MEMBER_TEMPLATE, data);
        DataResult<MemberDocumentDto> dataResult = ElasticsearchClient.search(searchRequest);
        if (null == dataResult || dataResult.getCount() == 0) {
            return pageable;
        }
        int total = (int) dataResult.getCount();
        pageable.setTotal(total).setTotalPage(Pageable.getTotalPage(pageSize, total));
        List<String> memberNos = dataResult.getList().stream().map(MemberDocumentDto::getMemberNo).collect(Collectors.toList());
        List<MemberDetailDto> memberInfos = listByMemberNos(pageMemberParamDto.getMasterType(), pageMemberParamDto.getMasterCode(), memberNos);
        pageable.setDatas(memberInfos);
        return pageable;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMember(CancelMemberDto cancelMemberDto) {
        Integer masterType = cancelMemberDto.getMasterType();
        String masterCode = cancelMemberDto.getMasterCode();
        String memberNo = cancelMemberDto.getMemberNo();
        String operator = cancelMemberDto.getOperator();
        // 校验会员状态
        checkMemberState(memberNo);
        // TODO 如果有预订或者在住订单，则不能注销

        // 获取会员的所有会员卡信息，用于记录日志
        List<MemberCardInfo> memberCards = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        // 执行注销操作
        memberInfoBiz.cancelMember(masterType, masterCode, memberNo, operator);

        // TODO 是否清空会员资产（储值、积分、优惠券）

        // 记录会员卡等级变化日志
        recordCancelMemberLogs(cancelMemberDto, memberCards);
        // 发送事件消息
        MemberEventMsg memberEventMsg = new MemberEventMsg();
        memberEventMsg.setMasterType(masterType).setMasterCode(masterCode)
                .setMemberNo(memberNo).setEventType(MemberEventEnum.CANCEL);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    /**
     * 记录会员注销的会员卡等级变化日志
     */
    private void recordCancelMemberLogs(CancelMemberDto cancelMemberDto, List<MemberCardInfo> memberCards) {
        if (CollectionUtils.isEmpty(memberCards)) {
            log.info("会员{}没有会员卡，无需记录注销日志");
            return;
        }
        Integer masterType = cancelMemberDto.getMasterType();
        String masterCode = cancelMemberDto.getMasterCode();
        String memberNo = cancelMemberDto.getMemberNo();
        String operator = cancelMemberDto.getOperator();
        for (MemberCardInfo memberCard : memberCards) {
            try {
                // 获取当前卡等级名称
                String currentLevelName = getCurrentCardLevelName(masterType, masterCode,
                        memberCard.getCardId(), memberCard.getCardLevel());
                // 创建注销记录
                MemberCardLevelChangeRecord cancelRecord = new MemberCardLevelChangeRecord();
                cancelRecord.setMasterType(masterType)
                        .setMasterCode(masterCode)
                        .setMemberNo(memberNo)
                        .setMemberCardNo(memberCard.getMemberCardNo())
                        .setCardId(memberCard.getCardId())
                        .setPreLevel(memberCard.getCardLevel())
                        .setPreLevelName(currentLevelName)
                        .setAfterLevel(0) // 注销后等级设为0
                        .setAfterLevelName("已注销")
                        .setChangeType(ChangeTypeEnum.CANCEL.getType())
                        .setReason(StringUtils.isBlank(cancelMemberDto.getReason()) ? "会员注销" : "会员注销;" + cancelMemberDto.getReason())
                        .setCreateUser(operator)
                        .setModifyUser(operator)
                        .setBizType(cancelMemberDto.getBizType())
                        .setBizNo(cancelMemberDto.getBizNo());
                // 保存记录
                memberCardLevelChangeRecordBiz.add(cancelRecord);
                log.info("记录会员{}卡{}注销日志成功", memberNo, memberCard.getMemberCardNo());
            } catch (Exception e) {
                log.error("记录会员{}卡{}注销日志失败: {}", memberNo, memberCard.getMemberCardNo(), e.getMessage(), e);
            }
        }
    }

    @Override
    public void recoverMember(Integer masterType, String masterCode, String memberNo, String operator) {
        MemberInfoDto memberInfo = getByMemberNo(masterType, masterCode, memberNo);
        if (Objects.isNull(memberInfo)) {
            log.info("this member is not exist!!memberNo:{}", memberNo);
            throw new ServiceException(MEMBER_10011);
        }
        if (memberInfo.getState().equals(MemberStateEnum.VALID.getState())) {
            log.info("this member state is invalid!!memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10035);
        }
        if (memberInfoBiz.existByMobile(masterType, masterCode, memberInfo.getMobile(), memberNo)) {
            log.info("this mobile has been used !!memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10036);
        }
        memberInfoBiz.recoverMember(masterType, masterCode, memberNo, operator);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo);
        memberEventMsg.setEventType(MemberEventEnum.RECOVER);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    @Override
    public void updateMember(UpdateMemberInfoDto dto) {

        MemberSaveWrapper wrapper = memberMedConverter.convertDtoToWrapper(dto);
        fillRegionInfo(wrapper.getMemberContactInfo());
        memberService.updateMember(wrapper);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(dto.getMemberInfo(), MemberEventEnum.MODIFY);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    @Override
    public void checkMemberState(String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        if (Objects.isNull(memberInfo)) {
            log.info("this member is not exist!!memberNo:{}", memberNo);
            throw new ServiceException(MEMBER_10011);
        }
        if (memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.info("this member state is invalid!!memberNo:{}", memberNo);
            throw new ServiceException(MEMBER_10033);
        }
    }

    /**
     * 检查会员是否有效（存在且未注销）
     *
     * @param memberNo 会员编号
     * @return true-有效，false-无效
     */
    private boolean isValidMember(String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        if (Objects.isNull(memberInfo)) {
            log.info("会员不存在，memberNo:{}", memberNo);
            return false;
        }
        if (memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.info("会员已注销，memberNo:{}", memberNo);
            return false;
        }
        return true;
    }

    @Override
    public void changePassword(String memberNo, String newPassword, String confirmPassword, String operator) {

        AbstractRegisterCheckHandler handler = RegisterCheckHandlerManager.getHandler(MemberRegisterCheckEnum.PASSWORD.getAction());
        RegisterMemberDto dto = new RegisterMemberDto();
        dto.setPassword(newPassword);
        dto.setConfirmPassword(confirmPassword);
        handler.check(dto);

        // 查询现有的会员信息
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        memberInfo.setPassword(encryptService.encrypt(newPassword));
        memberInfo.setModifyUser(operator);
        memberInfoBiz.update(memberInfo);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo, MemberEventEnum.MODIFY);
        messageMedService.sendMemberEventMsg(UserThreadHolder.getTrackingId(), memberEventMsg);

    }

    @Override
    public MemberInfoDto getByMobile(Integer masterCode, String masterType, String mobile, String memberNo) {
        return memberMedConverter.convertPoToDto(memberInfoBiz.getByMobile(masterCode, masterType, mobile, memberNo));
    }

    @Override
    public String getMemberNoByMobile(Integer masterType, String masterCode, String mobile) {
        MemberInfo memberInfo = memberInfoBiz.getByMobile(masterType, masterCode, mobile);
        return memberInfo == null ? null : memberInfo.getMemberNo();
    }

    @Override
    public List<MemberIdentityBaseInfoDto> fuzzyMatchMember(Integer masterType, String masterCode, String realName, String mobile, Integer size) {
        List<MemberInfo> infos = memberInfoBiz.matchMobileName(masterType, masterCode, realName, mobile, size);
        if (CollectionUtils.isEmpty(infos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = infos.stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
        return this.listBaseInfoByMemberNo(masterType, masterCode, memberNos);
    }

    @Override
    public IssueMemberCardResultDto issueMemberCard(IssueMemberCardRequestDto dto) {
        // 基础参数校验
        validateBasicRequest(dto);
        // 使用策略模式处理不同的发卡场景
        return memberCardIssueStrategyFactory.getStrategy(dto).execute(dto);
    }

    /**
     * 基础参数校验
     *
     * @param dto 请求参数
     */
    private void validateBasicRequest(IssueMemberCardRequestDto dto) {
        // 业务逻辑校验
        if (StringUtils.isBlank(dto.getCustomerNo()) && StringUtils.isBlank(dto.getMemberNo())) {
            throw new ServiceException(RespCodeEnum.CUSTOMER_MEMBER_EMPTY);
        }
        if (StringUtils.isNotBlank(dto.getCustomerNo()) && StringUtils.isNotBlank(dto.getMemberNo())) {
            throw new ServiceException(RespCodeEnum.CUSTOMER_MEMBER_NOT_EMPTY);
        }
        // 校验bizId是否已经处理过
        MemberCardLevelChangeRecord originalRecord = memberCardLevelChangeRecordBiz.getByBizNoAndType(dto.getMasterType(), dto.getMasterCode(),
                dto.getBizNo(), dto.getBizType());
        if (originalRecord != null) {
            throw new ServiceException(RespCodeEnum.ISSUE_REPEAT);
        }
    }

    /**
     * build card clauses
     *
     * @param bo
     * @return
     */
    protected List<JSONObject> buildTagClauses(PageMemberParamDto bo) {

        List<JSONObject> clauses = new ArrayList<>();

        List<Long> tagIds = bo.getTagIds();
        if (!CollectionUtils.isEmpty(tagIds)) {
            clauses.add(elasticsearchHandler.buildTerms("memberTagInfos.tagId", tagIds));
        }

        return clauses;
    }

    /**
     * build card clauses
     *
     * @param bo
     * @return
     */
    protected List<JSONObject> buildCardClauses(PageMemberParamDto bo) {

        List<JSONObject> clauses = new ArrayList<>();

        Integer cardType = bo.getCardType();
        if (!ObjectUtils.isEmpty(cardType)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.cardType", cardType));
        }

        String memberCardNo = bo.getMemberCardNo();
        if (!ObjectUtils.isEmpty(memberCardNo)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.memberCardNo", memberCardNo));
        }

//        cardInfo es里未存储memberNo，挪至memberInfo
//        String memberNo = bo.getMemberNo();
//        if (!ObjectUtils.isEmpty(memberNo)) {
//            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.memberNo", memberNo));
//        }

        Long cardId = bo.getCardId();
        if (!ObjectUtils.isEmpty(cardId)) {
            clauses.add(elasticsearchHandler.buildMatchPhase("memberCardInfos.cardId", cardId));
        }

        Integer cardLevel = bo.getCardLevel();
        if (!ObjectUtils.isEmpty(cardLevel)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.cardLevel", cardLevel));
        }

        Integer cardState = bo.getCardState();
        if (!Objects.isNull(cardState)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.cardState", cardState));
        }

        String cardEffectBeginDate = bo.getCardEffectBeginDate();
        if (StringUtils.isNotBlank(cardEffectBeginDate)) {
            clauses.add(elasticsearchHandler.buildRange("memberCardInfos.cardEffectBeginDate", "gte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(cardEffectBeginDate).atTime(0, 0)).getTime()));
        }

        String cardEffectEndDate = bo.getCardEffectEndDate();
        if (!Objects.isNull(cardEffectEndDate)) {
            clauses.add(elasticsearchHandler.buildRange("memberCardInfos.cardEffectEndDate", "lte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(cardEffectEndDate).atTime(0, 0)).getTime()));
        }

        return clauses;
    }


    /**
     * build clauses
     *
     * @param bo
     * @return
     */
    protected List<JSONObject> buildClauses(PageMemberParamDto bo) {

        List<JSONObject> clauses = new ArrayList<>();

        Integer masterType = bo.getMasterType();
        if (!ObjectUtils.isEmpty(masterType)) {
            clauses.add(elasticsearchHandler.buildTerm("masterType", masterType));
        }

        String masterCode = bo.getMasterCode();
        if (!ObjectUtils.isEmpty(masterCode)) {
            clauses.add(elasticsearchHandler.buildTerm("masterCode", masterCode));
        }

        String memberNo = bo.getMemberNo();
        if (!ObjectUtils.isEmpty(memberNo)) {
            clauses.add(elasticsearchHandler.buildTerm("memberNo", memberNo));
        }

        String realName = bo.getRealName();
        if (!ObjectUtils.isEmpty(realName)) {
            clauses.add(elasticsearchHandler.buildMatchPhase("realName", realName));
        }

        String mobile = bo.getMobile();
        if (!ObjectUtils.isEmpty(mobile)) {
            clauses.add(elasticsearchHandler.buildTerm("mobile", mobile));
        }

        Integer idType = bo.getIdType();
        if (!Objects.isNull(idType)) {
            clauses.add(elasticsearchHandler.buildTerm("idType", idType));
        }

        String idNo = bo.getIdNo();
        if (!ObjectUtils.isEmpty(idNo)) {
            clauses.add(elasticsearchHandler.buildTerm("idNo", idNo));
        }

        Integer state = bo.getState();
        if (!Objects.isNull(state)) {
            clauses.add(elasticsearchHandler.buildTerm("state", state));
        }

        String source = bo.getSource();
        if (!ObjectUtils.isEmpty(source)) {
            clauses.add(elasticsearchHandler.buildTerm("source", source));
        }

        String registerHotel = bo.getRegisterHotel();
        if (!ObjectUtils.isEmpty(registerHotel)) {
            clauses.add(elasticsearchHandler.buildTerm("registerHotel", registerHotel));
        }

        String registerHotelType = bo.getRegisterHotelType();
        if (!ObjectUtils.isEmpty(registerHotelType)) {
            clauses.add(elasticsearchHandler.buildTerm("registerHotelType", registerHotelType));
        }

        String salesman = bo.getSalesman();
        if (!ObjectUtils.isEmpty(salesman)) {
            clauses.add(elasticsearchHandler.buildTerm("salesman", salesman));
        }

        String memberRegisterBeginDate = bo.getMemberRegisterBeginDate();
        if (StringUtils.isNotEmpty(memberRegisterBeginDate)) {
            clauses.add(elasticsearchHandler.buildRange("registerDate", "gte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(memberRegisterBeginDate).atTime(0, 0, 0)).getTime()));
        }

        String memberRegisterEndDate = bo.getMemberRegisterEndDate();
        if (StringUtils.isNotEmpty(memberRegisterEndDate)) {
            clauses.add(elasticsearchHandler.buildRange("registerDate", "lte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(memberRegisterEndDate).atTime(23, 59, 59)).getTime()));
        }

        Integer blackFlag = bo.getBlackFlag();
        if (blackFlag != null) {
            clauses.add(elasticsearchHandler.buildTerm("blackFlag", blackFlag));
        }
        return clauses;
    }

    /**
     * 注册失败通知
     *
     * @param trackingId
     * @param masterType
     * @param masterCode
     * @param mobile
     * @param message
     */
    private void sendRegisterFailOaContext(String trackingId, Integer masterType, String masterCode, String mobile, String message) {
        try {
            String title = "【会员注册失败通知】";
            String content = title + Constant.HTML_RETURN_NEWLINE +
                    "trackingId:" + trackingId + Constant.HTML_RETURN_NEWLINE +
                    "会员归属:" + MasterTypeEnum.getDesc(masterType) + Constant.HTML_RETURN_NEWLINE +
                    "会员归属值:" + masterCode + Constant.HTML_RETURN_NEWLINE +
                    "手机号:" + mobile + Constant.HTML_RETURN_NEWLINE +
                    "失败原因" + message + Constant.HTML_RETURN_NEWLINE;
            messageMedService.sendOaAlertMsg(trackingId, title, content);
        } catch (Exception e) {
            log.error("告警消息发送失败：{}", trackingId, e);
        }
    }

    /**
     * 填充扩展信息
     *
     * @param contactInfo
     */
    private void fillRegionInfo(MemberContactInfo contactInfo) {

        //TODO 后期如果有性能问题，可以封装一个接口或者并行
        Integer countryId = contactInfo.getCountryId();
        if (!Objects.isNull(countryId)) {
            RegionResp resp = regionDecorator.getById(countryId);
            if (!Objects.isNull(resp)) {
                contactInfo.setCountry(resp.getName());
            }
        }
        Integer provinceId = contactInfo.getProvinceId();
        if (!Objects.isNull(provinceId)) {
            RegionResp resp = regionDecorator.getById(provinceId);
            if (!Objects.isNull(resp)) {
                contactInfo.setProvince(resp.getName());
            }
        }
        Integer cityId = contactInfo.getCityId();
        if (!Objects.isNull(cityId)) {
            RegionResp resp = regionDecorator.getById(cityId);
            if (!Objects.isNull(resp)) {
                contactInfo.setCity(resp.getName());
            }
        }
        Integer districtId = contactInfo.getDistrictId();
        if (!Objects.isNull(districtId)) {
            RegionResp resp = regionDecorator.getById(districtId);
            if (!Objects.isNull(resp)) {
                contactInfo.setDistrict(resp.getName());
            }
        }
    }

    private MemberCardLevelChangeRecord assembleChangeRecord(IssueMemberCardDto memberCardInfo, RegisterMemberDto dto) {
        String reason = StringUtils.isNotBlank(dto.getReason()) ? "注册会员发放会员卡;" + dto.getReason() : "注册会员发放会员卡";
        MemberCardLevelChangeRecord record = memberCardMedConverter.convertDtoToPo(reason, memberCardInfo);
        record.setMasterType(dto.getMasterType())
                .setMasterCode(dto.getMasterCode())
                .setChangeType(LevelChangeTypeEnum.REGISTER.getType())
                .setPreLevel(0)
                .setPreLevelName("")
                .setAfterLevel(memberCardInfo.getCardLevel())
                .setAfterLevelName(memberCardInfo.getCardLevelName())
                .setBizNo(dto.getBizNo())
                .setBizType(dto.getBizType());
        return record;
    }

    private MemberIssueCardWrapper assembleIssueCardWrapper(RegisterMemberDto dto, String memberNo) {
        IssueMemberCardDto memberCardInfo = dto.getMemberCardInfo();
        memberCardInfo.setMemberNo(memberNo);
        MemberIssueCardWrapper issueWrapper = new MemberIssueCardWrapper();
        issueWrapper.setMemberCardInfo(memberCardMedConverter.convertDtoToPo(memberCardInfo, LocalDateTime.now(),
                dto.getMasterType(), dto.getMasterCode()));
        issueWrapper.setMemberCardLevelChangeRecord(assembleChangeRecord(memberCardInfo, dto));
        return issueWrapper;
    }

    private Map<String, HotelBaseInfoResp> mapHotel(String blocCode) {
        Map<String, HotelBaseInfoResp> hotelMap = new HashMap<>();
        if (StringUtils.isNotBlank(blocCode)) {
            List<HotelBaseInfoResp> hotelBaseInfoRespList = hotelDecorator.listHotelBaseInfos(blocCode, null);
            hotelMap = hotelBaseInfoRespList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, item -> item));
        }
        return hotelMap;
    }


    /**
     * 获取当前会员卡等级名称
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @param cardLevel
     * @return
     */
    private String getCurrentCardLevelName(Integer masterType, String masterCode, Long cardId, Integer cardLevel) {
        try {
            CardConfigDto memberCardConfig = cardConfigMedService.getCardConfig(masterType, masterCode, cardId);
            if (memberCardConfig != null && CollectionUtils.isNotEmpty(memberCardConfig.getCardLevelConfigs())) {
                return memberCardConfig.getCardLevelConfigs().stream()
                        .filter(config -> config.getCardLevel().equals(cardLevel))
                        .map(CardLevelConfigWithPrivilegeDto::getCardLevelName)
                        .findFirst()
                        .orElse("未知等级");
            }
        } catch (Exception e) {
            log.warn("获取会员卡等级名称失败: cardId={}, cardLevel={}", cardId, cardLevel, e);
        }
        return "未知等级";
    }

    /**
     * 获取会员卡等级名称
     *
     * @param dto
     * @return
     */
    private String getCardLevelName(IssueMemberCardRequestDto dto) {
        try {
            CardConfigDto memberCardConfig = cardConfigMedService.getCardConfig(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
            if (memberCardConfig != null && CollectionUtils.isNotEmpty(memberCardConfig.getCardLevelConfigs())) {
                return memberCardConfig.getCardLevelConfigs().stream()
                        .filter(config -> config.getCardLevel().equals(dto.getCardLevel()))
                        .map(CardLevelConfigWithPrivilegeDto::getCardLevelName)
                        .findFirst()
                        .orElse("未知等级");
            }
        } catch (Exception e) {
            log.warn("获取会员卡等级名称失败", e);
        }
        return "未知等级";
    }

    /**
     * 构建会员卡信息
     *
     * @param dto
     * @return
     */
    private IssueMemberCardDto buildMemberCardInfo(IssueMemberCardRequestDto dto) {
        IssueMemberCardDto memberCardInfo = new IssueMemberCardDto();
        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            // 发放默认卡逻辑
            CardConfigDto defaultMemberCard = cardConfigMedService.getDefaultCard(dto.getMasterType(), dto.getMasterCode());
            memberCardInfo.setCardId(defaultMemberCard.getId());
        } else {
            memberCardInfo.setCardId(dto.getCardId());
        }
        memberCardInfo.setCardLevel(dto.getCardLevel());
        // 查询需要升级的卡信息
        CardConfigDto memberCardConfig = cardConfigMedService.getCardConfig(dto.getMasterType(), dto.getMasterCode(), memberCardInfo.getCardId());
        if (memberCardConfig != null && CollectionUtils.isNotEmpty(memberCardConfig.getCardLevelConfigs())) {
            Optional<String> optionalCardLevelName = memberCardConfig.getCardLevelConfigs().stream()
                    .filter(config -> config.getCardLevel().equals(dto.getCardLevel()))
                    .map(CardLevelConfigWithPrivilegeDto::getCardLevelName)
                    .findFirst();
            if (!optionalCardLevelName.isPresent()) {
                // 卡片等级不存在
                throw new ServiceException(RespCodeEnum.MEMBER_10005);
            }
            memberCardInfo.setCardLevelName(optionalCardLevelName.get());
        }
        memberCardInfo.setCardType(memberCardConfig.getCardType());
        memberCardInfo.setMemberCardNo(memberCardMedService.generateCardNo(dto.getMasterType(), dto.getMasterCode(), memberCardInfo.getCardId()));
        if (StringUtils.isNotBlank(dto.getHotelCode())) {
            memberCardInfo.setIssueHotelType("HOTEL");
            memberCardInfo.setIssueHotel(dto.getHotelCode());
        } else {
            memberCardInfo.setIssueHotelType("BLOC");
        }
        memberCardInfo.setChangeType(ChangeTypeEnum.REGISTER.getType());
        memberCardInfo.setIssueUser(dto.getBizNo());
        // 卡片有效期
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto().setMasterType(dto.getMasterType()).setMasterCode(dto.getMasterCode())
                .setCardId(memberCardInfo.getCardId()).setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> cardLevelList = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Optional<CardLevelConfigWithPrivilegeDto> optionCardLevel = cardLevelList
                .stream()
                .filter(e -> e.getCardLevel().equals(dto.getCardLevel()))
                .findFirst();
        if (!optionCardLevel.isPresent()) {
            throw new ServiceException(RespCodeEnum.MEMBER_10018);
        }
        CardLevelConfigWithPrivilegeDto carLevelConfig = optionCardLevel.get();
        memberCardInfo.setIsLongTerm(carLevelConfig.getIsLongTerm());
        if (carLevelConfig.getIsLongTerm() == 0 && carLevelConfig.getValidPeriod() > 0) {
            // 非长期有效
            Integer validPeriod = carLevelConfig.getValidPeriod();
            memberCardInfo.setEffectBeginDate(DateUtil.formatDate(new Date()));
            memberCardInfo.setEffectEndDate(DateUtil.offset(new Date(), DateField.DAY_OF_YEAR, validPeriod).toDateStr());
        }
        return memberCardInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RecycleMemberCardResultDto recycleMemberCard(RecycleMemberCardRequestDto dto) {
        // 1. 根据bizNo查询之前的下发操作记录
        MemberCardLevelChangeRecord originalRecord = memberCardLevelChangeRecordBiz.getByBizNoAndType(dto.getMasterType(), dto.getMasterCode(),
                dto.getBizNo(), dto.getBizType());
        if (originalRecord == null) {
            throw new ServiceException(RespCodeEnum.RECYCLE_NO_BIZ);
        }
        dto.setBizNo(dto.getBizNo() + "_RECYCLE");
        originalRecord.setBizNo(dto.getBizNo());
        MemberCardLevelChangeRecord repeatRecord = memberCardLevelChangeRecordBiz.getByBizNoAndType(dto.getMasterType(), dto.getMasterCode(),
                dto.getBizNo(), dto.getBizType());
        if (repeatRecord != null) {
            throw new ServiceException(RespCodeEnum.ISSUE_REPEAT);
        }
        // 2. 根据原始操作类型决定回收操作
        String operationType;
        String memberNo = originalRecord.getMemberNo();
        String memberCardNo = originalRecord.getMemberCardNo();
        Integer cardLevel = originalRecord.getAfterLevel();
        String cardLevelName = originalRecord.getAfterLevelName();
        if (ChangeTypeEnum.REGISTER.getType().equals(originalRecord.getChangeType())) {
            // 如果是注册，则做注销会员
            CancelMemberDto cancelMemberDto = new CancelMemberDto();
            cancelMemberDto.setMasterType(originalRecord.getMasterType());
            cancelMemberDto.setMasterCode(originalRecord.getMasterCode());
            cancelMemberDto.setMemberNo(memberNo);
            cancelMemberDto.setOperator(dto.getBizType());
            cancelMemberDto.setBizType(dto.getBizType());
            cancelMemberDto.setBizNo(dto.getBizNo());
            cancelMemberDto.setReason(dto.getReason());
            cancelMember(cancelMemberDto);
            operationType = "CANCEL_MEMBER";
        } else if (ChangeTypeEnum.UPGRADE_PURCHASE.getType().equals(originalRecord.getChangeType())) {
            // 如果是升级，则做降级操作
            downgradeMemberCard(originalRecord, dto.getReason());
            operationType = "DOWNGRADE";
            cardLevel = originalRecord.getPreLevel();
            cardLevelName = originalRecord.getPreLevelName();
        } else if (ChangeTypeEnum.ISSUE.getType().equals(originalRecord.getChangeType())) {
            // 如果是下发会员卡，则直接回收会员卡
            recycleMemberCardDirect(originalRecord, dto.getReason());
            operationType = "RECYCLE_CARD";
        } else {
            throw new ServiceException(RespCodeEnum.RECYCLE_NO_BIZ);
        }
        return RecycleMemberCardResultDto.success(memberNo, memberCardNo, cardLevel, cardLevelName, operationType);
    }

    /**
     * 降级会员卡
     *
     * @param originalRecord 原始记录
     * @param reason         降级原因
     */
    private void downgradeMemberCard(MemberCardLevelChangeRecord originalRecord, String reason) {
        // 查询会员卡信息
        MemberCardInfo memberCardInfo = memberCardInfoBiz.getByMemberCardNo(
                originalRecord.getMasterType(),
                originalRecord.getMasterCode(),
                originalRecord.getMemberCardNo()
        );
        if (memberCardInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10013);
        }
        // 更新会员卡等级为原来的等级
        memberCardInfo.setCardLevel(originalRecord.getPreLevel());
        memberCardInfo.setModifyUser(originalRecord.getBizNo());
        memberCardInfoBiz.update(memberCardInfo);
        // 记录降级日志
        MemberCardLevelChangeRecord downgradeRecord = new MemberCardLevelChangeRecord()
                .setMasterType(originalRecord.getMasterType())
                .setMasterCode(originalRecord.getMasterCode())
                .setMemberNo(originalRecord.getMemberNo())
                .setMemberCardNo(originalRecord.getMemberCardNo())
                .setPreLevel(originalRecord.getAfterLevel())
                .setPreLevelName(originalRecord.getAfterLevelName())
                .setAfterLevel(originalRecord.getPreLevel())
                .setAfterLevelName(originalRecord.getPreLevelName())
                .setChangeType(ChangeTypeEnum.DOWN_AUTO.getType())
                .setReason(reason)
                .setCardId(originalRecord.getCardId())
                .setBizType(originalRecord.getBizType())
                .setBizNo(originalRecord.getBizNo())
                .setCreateUser(originalRecord.getBizType());
        memberCardLevelChangeRecordBiz.add(downgradeRecord);
    }

    /**
     * 直接回收会员卡
     *
     * @param originalRecord 原始记录
     * @param reason         回收原因
     */
    private void recycleMemberCardDirect(MemberCardLevelChangeRecord originalRecord, String reason) {
        // 删除会员卡
        memberCardInfoBiz.deleteByMemberCard(
                originalRecord.getMemberNo(),
                originalRecord.getMemberCardNo(),
                originalRecord.getBizType()
        );
        // 记录回收日志
        MemberCardLevelChangeRecord recycleRecord = new MemberCardLevelChangeRecord()
                .setMasterType(originalRecord.getMasterType())
                .setMasterCode(originalRecord.getMasterCode())
                .setMemberNo(originalRecord.getMemberNo())
                .setMemberCardNo(originalRecord.getMemberCardNo())
                .setPreLevel(originalRecord.getAfterLevel())
                .setPreLevelName(originalRecord.getAfterLevelName())
                .setAfterLevel(0)
                .setAfterLevelName("已回收")
                .setChangeType(ChangeTypeEnum.RECYCLE.getType())
                .setReason(reason)
                .setCardId(originalRecord.getCardId())
                .setBizType(originalRecord.getBizType())
                .setBizNo(originalRecord.getBizNo())
                .setCreateUser(originalRecord.getBizType());
        memberCardLevelChangeRecordBiz.add(recycleRecord);
    }

}
