package com.ly.titc.pms.member.mediator.entity.dto.point;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class ListBlocScopeUsageDto {


    /**
     * 主体类型（创建） 1:集团 2:门店 3：艺龙
     */
    private Integer masterType;

    /**
     * 主体类型编码（创建） ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 适用来源
     */
    private List<String> scopeSources;

    /**
     * 适用来源code
     */
    private List<String> scopeSourceCodes;

    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

}
