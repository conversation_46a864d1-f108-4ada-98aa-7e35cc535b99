package com.ly.titc.pms.member.mediator.entity.dto.privilege;

import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.BasePageDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/29 13:34
 */
@Data
public class PagePrivilegeConfigDto extends BasePageDto {

    /**
     * 权益名称
     */
    private String privilegeName;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

}
