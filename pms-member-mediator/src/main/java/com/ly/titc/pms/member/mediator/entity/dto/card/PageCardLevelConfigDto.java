package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;

/**
 * 分页查询会员等级入参
 *
 * @Author：rui
 * @name：MemberCardPrivilegeConfigInfoQueryReq
 * @Date：2024-11-7 17:52
 * @Filename：MemberCardPrivilegeConfigInfoQueryReq
 */
@Data
public class PageCardLevelConfigDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 卡等级
     */
    private Integer level;

    /**
     * 状态
     */
    private Integer state;


    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;
}
