package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.biz.PointConfigBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.enums.MemberDateUnitEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.PointConfigInfo;
import com.ly.titc.pms.member.mediator.converter.PointConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.point.PointConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.SavePointConfigDto;
import com.ly.titc.pms.member.mediator.service.PointConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 积分配置
 *
 * <AUTHOR>
 * @date 2025/6/25 19:24
 */
@Slf4j
@Service
public class PointConfigMedServiceImpl implements PointConfigMedService {

    @Resource
    private PointConfigBiz pointConfigBiz;

    @Resource
    private PointConfigMedConverter pointConfigMedConverter;

    @Resource
    private RedisFactory redisFactory;

    @Override
    public void savePointConfig(SavePointConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.POINT_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this point config save is processing...masterType:{},masterCode:{}", dto.getMasterType(), dto.getMasterCode());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            PointConfigInfo existInDB = pointConfigBiz.getByMaster(dto.getMasterType(), dto.getMasterCode());
            PointConfigInfo configInfo = pointConfigMedConverter.convertDtoToPo(dto);
            if (existInDB == null) {
                configInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                pointConfigBiz.insert(configInfo);
            } else {
                configInfo.setId(existInDB.getId());
                configInfo.setModifyUser(dto.getOperator());
                pointConfigBiz.update(configInfo);
            }
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public String getPointExpireDate(Integer masterType, String masterCode) {
        LocalDate now = LocalDate.now();
        PointConfigDto resp = getPointConfig(masterType, masterCode);
        //如果是长期有效
        if (resp.getPointLimitLong() == 1) {
            return SystemConstant.PERPETUAL_EFFECT_DATE;
        } else {
            if (resp.getPointLimit() <= 0) {
                throw new ServiceException("积分过期时间设置错误", RespCodeEnum.CODE_400.getCode());
            }
            if (resp.getPointLimitUnit().equals(MemberDateUnitEnum.YEAR.getCode())) {
                return now.plusYears(resp.getPointLimit()).toString();
            } else if (resp.getPointLimitUnit().equals(MemberDateUnitEnum.MONTH.getCode())) {
                return now.plusMonths(resp.getPointLimit()).toString();
            } else {
                return now.plusDays(resp.getPointLimit()).toString();
            }
        }
    }

    @Override
    public PointConfigDto getPointConfig(Integer masterType, String masterCode) {
        PointConfigInfo configInfo = pointConfigBiz.getByMaster(masterType, masterCode);
        return pointConfigMedConverter.convertPoToDto(configInfo);
    }
}
