package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.pms.member.biz.MemberProfileTagInfoBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.CycleTypeEnum;
import com.ly.titc.pms.member.com.enums.SuccessfulPerformTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.ListMemberTagConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.MemberTagConfigDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.AddMemberProfileTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberTagDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.common.OperationLogDubboInterface;
import com.ly.titc.pms.member.mediator.service.MemberConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberTagHandler
 * @Date：2024-11-21 14:25
 * @Filename：MemberTagHandler
 */
@Component
@Slf4j
public class MemberTagHandler extends AbstractScheduleHandler<MemberTagDto> {

    @Resource
    private MemberConfigMedService memberConfigMedService;

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private MemberProfileTagInfoBiz memberProfileTagInfoBiz;

    @Resource
    private MemberProfileMedService memberProfileMedService;

    @Resource
    private OperationLogDubboInterface operationLogDubboInterface;

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询所有的标签
        ListMemberTagConfigDto configDto = new ListMemberTagConfigDto();
        configDto.setMasterType(masterType);
        configDto.setMasterCode(masterCode);
        List<MemberTagConfigDetailDto> memberTagConfigInfos = memberConfigMedService.listTagConfig(configDto);
        List<MemberTagDto> memberTagDtos = scheduleMedConverter.convertTagDto(memberTagConfigInfos);
        // 创建缓存对象，避免重复查询
        MemberRecordsCache cache = new MemberRecordsCache();
        for (MemberTagDto ruleDto : memberTagDtos) {
            Boolean flag;
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER)) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
            } else {
                // 获取这个会员的上次升降级日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType()));
            }
            LocalDateTime startDate = memberCardLevelChangeRecord.getGmtCreate();
            LocalDateTime endDate = LocalDateTime.now();

            // 获取会员的各种记录数据（使用缓存优化，避免重复查询）
            Map<String, List<BaseCheckDto>> memberRecords = getAllMemberRecords(masterType, masterCode, memberNo, startDate, endDate, cache);
            ConditionCheckResult checkResult;
            if (ruleDto.getSatisfyPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                checkResult = super.checkAllConditionsWithDetail(ruleDto.getMarkRuleList(), memberNo,
                        memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                        memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                        memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
            } else {
                checkResult = super.checkAnyConditionWithDetail(ruleDto.getMarkRuleList(), memberNo,
                        memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                        memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                        memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
            }
            flag = checkResult.isPassed();
            if (flag) {
                // 打标
                AddMemberProfileTagDto addMemberProfileTagDto = new AddMemberProfileTagDto();
                addMemberProfileTagDto.setMasterType(ruleDto.getMasterType());
                addMemberProfileTagDto.setMasterCode(ruleDto.getMasterCode());
                addMemberProfileTagDto.setTagId(ruleDto.getId());
                addMemberProfileTagDto.setTagNo(ruleDto.getId());
                addMemberProfileTagDto.setTagName(ruleDto.getName());
                addMemberProfileTagDto.setMarkType(2);
                addMemberProfileTagDto.setCreateUser("自动打标");
                addMemberProfileTagDto.setTagType(ruleDto.getType());
                addMemberProfileTagDto.setMemberNo(memberNo);
                memberProfileMedService.addMemberTag(addMemberProfileTagDto);
                // 记录操作日志
                log.info("会员标签添加成功 - 会员编号: {}, 标签名称: {}, 原因: {}",
                    memberNo, ruleDto.getName(), checkResult.generateReason());
                // TODO: 添加操作日志记录
                // recordOperationLog("ADD_TAG", memberNo, ruleDto.getName(), checkResult.generateReason());
            } else {
                if (ruleDto.getAutoDelete().equals(1)) {
                    // 不满足条件，删除
                    memberProfileMedService.deleteMemberTag(ruleDto.getMasterType(), ruleDto.getMasterCode(), memberNo, ruleDto.getId(), "schedule");
                    // 记录操作日志
                    log.info("会员标签删除成功 - 会员编号: {}, 标签名称: {}, 原因: {}",
                        memberNo, ruleDto.getName(), checkResult.generateReason());
                    // TODO: 添加操作日志记录
                    // recordOperationLog("REMOVE_TAG", memberNo, ruleDto.getName(), checkResult.generateReason());
                }
            }
        }
    }

    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_TAG.getAction();
    }


//    public void addRecordLogRecord(String tenantCode, LogConstants.Module bizModule, LogConstants.Action action, String tagName, String opContent, String operator) {
//        //TenantBlocDetailResp detail = tenantBlocProcessor.getInfoByCode(blocCode, UUID.randomUUID().toString());
//        // String tenantCode = detail.getTenantCode();
//        RecordReq recordReq = new RecordReq();
//        recordReq.setTenant(PROJECT_CODE);
//        // 操作模块
//        recordReq.setBizCode(bizModule.getCode());
//        recordReq.setBizName(bizModule.getDesc());
//        // 行为
//        recordReq.setCategory(action.getCode());
//        recordReq.setCategoryName(action.getDesc());
//        // 操作对象
//        Map<String, Object> tagNamemap = new HashMap<>();
//        tagNamemap.put(LogBuilder.DefaultKey.Name, tagName);
//        tagNamemap.put(LogBuilder.DefaultKey.Bloc_Code, "");
//        tagNamemap.put(LogBuilder.DefaultKey.Tenant_Code, tenantCode);
//        recordReq.setTags(tagNamemap);
//        // 操作时间
//        recordReq.setTimestamp(System.currentTimeMillis());
//        // 操作内容
//        recordReq.setOpContent(opContent);
//        recordReq.setTrackingId(UUID.randomUUID().toString());
//        // 操作用户
//        recordReq.setOperator(operator);
//        recordReq.setState("SUCCESS");
//        operationLogDubboInterface.record(recordReq);
//    }
}
