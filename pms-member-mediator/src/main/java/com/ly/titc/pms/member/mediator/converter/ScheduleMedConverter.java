package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.MemberTagConfigDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDto;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Author：rui
 * @name：ScheduleConverter
 * @Date：2024-11-21 16:19
 * @Filename：ScheduleConverter
 */
@Mapper(componentModel = "spring")
public interface ScheduleMedConverter {

    List<MemberTagDto> convertTagDto(List<MemberTagConfigDetailDto> respList);

    List<MemberRelegationRuleDto> convertRelegationRuleDto(List<CardLevelRelegationRuleDto> respList);

    MemberRelegationRuleDto convertRelegationRuleDto(CardLevelRelegationRuleDto resp);

    List<MemberUpgradeRuleDto> convertUpgradeRuleDto(List<CardLevelUpgradeRuleDto> respList);

    MemberUpgradeRuleDto convertUpgradeRuleDto(CardLevelUpgradeRuleDto resp);

    MemberTagDto convertDtoToDto(MemberTagConfigDetailDto memberTagConfigDetailDto);

}
