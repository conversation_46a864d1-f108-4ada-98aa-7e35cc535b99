package com.ly.titc.pms.member.mediator.converter;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.account.dubbo.com.enums.PayChannelEnum;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.bo.PageMemberCheckInParamBo;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderPayDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberDataRecordConverter
 * @Date：2024-12-11 21:43
 * @Filename：MemberDataRecordConverter
 */
@Mapper(componentModel = "spring", imports = {Arrays.class})
public interface MemberDataRecordMedConverter extends BaseConverter {

    default List<CheckInRecordDto> convertPoToDto(List<MemberCheckInRecord> memberCheckInRecords, Map<String, String> hotelNameMap) {
        return memberCheckInRecords.stream().map(e -> {
            CheckInRecordDto checkInRecordDto = convertPoToDto(e);
            checkInRecordDto.setHotelName(hotelNameMap.get(e.getHotelCode()));
            return checkInRecordDto;
        }).collect(Collectors.toList());
    }

    CheckInRecordDto convertPoToDto(MemberCheckInRecord memberCheckInRecord);

    CheckInStatisticsDto convertPoToDto(MemberCheckInStatistics memberCheckInStatistics);

    List<PurchaseCardRecordDto> convertPoToPurchaseCardRecordDto(List<MemberOrderInfo> memberCheckInStatistics);

    @Mappings({
            @Mapping(target = "gmtCreate", source = "gmtCreate", qualifiedByName = "timestampToString"),
            @Mapping(target = "gmtModified", source = "gmtModified", qualifiedByName = "timestampToString")
    })
    PurchaseCardRecordDto convertPoToPurchaseCardRecordDto(MemberOrderInfo memberCheckInStatistics);

    default List<PurchaseCardRecordDto> convertPoToPurchaseCardRecordDto(List<MemberOrderInfo> orderInfoList,
                                                                         List<MemberOrderDetailInfo> memberOrderDetailInfo,
                                                                         List<MemberOrderPayInfo> memberOrderPayInfoList,
                                                                         MemberInfo memberInfo,
                                                                         Map<String, String> hotelBaseInfoMap) {
        List<PurchaseCardRecordDto> result = convertPoToPurchaseCardRecordDto(orderInfoList);
        Map<String, MemberOrderDetailInfo> map = memberOrderDetailInfo.stream().collect(Collectors.toMap(MemberOrderDetailInfo::getMemberOrderNo, detailInfo -> detailInfo));
        Map<String, MemberOrderPayInfo> payInfoMap = memberOrderPayInfoList.stream().collect(Collectors.toMap(MemberOrderPayInfo::getMemberOrderNo, payInfo -> payInfo));
        for (PurchaseCardRecordDto purchaseCardRecordDto : result) {
            purchaseCardRecordDto.setMemberName(memberInfo.getRealName());
            purchaseCardRecordDto.setDetail(convertPoToPurchaseCardRecordDetailDto(map.get(purchaseCardRecordDto.getMemberOrderNo())));
            MemberOrderPayDto memberOrderPayDto = convertPoToPurchaseCardPayInfoDto(payInfoMap.get(purchaseCardRecordDto.getMemberOrderNo()));
            memberOrderPayDto.setPayChannelDesc(PayChannelEnum.getDescByCode(memberOrderPayDto.getPayChannel()));
            purchaseCardRecordDto.setPayInfo(memberOrderPayDto);
            purchaseCardRecordDto.setPlatformName(PlatformChannelEnum.getByPlatformChannel(purchaseCardRecordDto.getPlatformChannel()).getPlatformChannelDesc());
            if (purchaseCardRecordDto.getMasterType().equals(MasterTypeEnum.HOTEL.getType())) {
                purchaseCardRecordDto.setSource(hotelBaseInfoMap.getOrDefault(purchaseCardRecordDto.getMasterCode(), ""));
            } else if (purchaseCardRecordDto.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                purchaseCardRecordDto.setSource("集团");
            }
            purchaseCardRecordDto.setMemberSceneName(MemberSceneEnum.getDesc(purchaseCardRecordDto.getMemberScene()));
            if (Objects.nonNull(purchaseCardRecordDto.getDetail()) && StringUtils.isNotEmpty(purchaseCardRecordDto.getDetail().getGiftPack())) {
                List<BaseGearDto> discountBenefits = JSONArray.parseArray(purchaseCardRecordDto.getDetail().getGiftPack(), BaseGearDto.class);
                purchaseCardRecordDto.setBenefitDtoList(discountBenefits);
            }
            purchaseCardRecordDto.setGmtCreate(purchaseCardRecordDto.getGmtCreate());
            purchaseCardRecordDto.setGmtModified(purchaseCardRecordDto.getGmtModified());
            purchaseCardRecordDto.getPayInfo().setPayAmount(purchaseCardRecordDto.getAmount());
        }
        return result;
    }

    PurchaseCardRecordDetailDto convertPoToPurchaseCardRecordDetailDto(MemberOrderDetailInfo memberOrderDetailInfo);

    @Mappings({
            @Mapping(target = "payState", source = "orderPayState")
    })
    MemberOrderPayDto convert(MemberOrderPayInfo orderPayInfo);

    @Mappings({
            @Mapping(target = "payState", source = "orderPayState")
    })
    MemberOrderPayDto convertPoToPurchaseCardPayInfoDto(MemberOrderPayInfo memberOrderPayInfo);

    default List<MemberLevelChangeRecordDto> convertPoToMemberLevelChangeRecordDto(List<MemberCardLevelChangeRecord> memberOrderInfos,
                                                                                   List<CardConfigDto> memberCardConfigRespList,
                                                                                   List<CardLevelUpgradeRuleDto> memberCardLevelUpgradeRules,
                                                                                   List<CardLevelRelegationRuleDto> memberCardLevelRelegationRules) {
        Map<Long, String> memberCardConfigMap = memberCardConfigRespList.stream().collect(Collectors.toMap(CardConfigDto::getId, CardConfigDto::getCardName));
        Map<Long, String> memberCardLevelUpgradeRuleMap = memberCardLevelUpgradeRules.stream().collect(Collectors.toMap(CardLevelUpgradeRuleDto::getId, CardLevelUpgradeRuleDto::getName));
        Map<Long, String> memberCardLevelRelegationRuleMap = memberCardLevelRelegationRules.stream().collect(Collectors.toMap(CardLevelRelegationRuleDto::getId, CardLevelRelegationRuleDto::getName));
        return memberOrderInfos.stream().map(memberOrderInfo -> {
            MemberLevelChangeRecordDto memberLevelChangeRecordDto = convertPoToMemberLevelChangeRecordDto(memberOrderInfo);
            memberLevelChangeRecordDto.setCardName(memberCardConfigMap.getOrDefault(memberOrderInfo.getCardId(), ""));
            if (memberOrderInfo.getChangeType().equals(ChangeTypeEnum.DOWN_AUTO.getType())) {
                memberLevelChangeRecordDto.setRuleName(memberCardLevelRelegationRuleMap.getOrDefault(memberOrderInfo.getRuleId(), ""));
            } else if (memberOrderInfo.getChangeType().equals(ChangeTypeEnum.UPGRADE_AUTO.getType())) {
                memberLevelChangeRecordDto.setRuleName(memberCardLevelUpgradeRuleMap.getOrDefault(memberOrderInfo.getRuleId(), ""));
            }
            memberLevelChangeRecordDto.setChangeTypeName(ChangeTypeEnum.getDescByType(memberOrderInfo.getChangeType()));
            return memberLevelChangeRecordDto;
        }).collect(Collectors.toList());
    }

    MemberLevelChangeRecordDto convertPoToMemberLevelChangeRecordDto(MemberCardLevelChangeRecord memberOrderInfo);

    PageMemberCheckInParamBo convertDtoToBo(PageCheckinRecordDto dto);

    MemberCheckInRecord convertDtoToPo(AddMemberCheckInRecordDto dto);

    MemberCheckInStatistics convertDtoToPo(CheckInStatisticsDto statistics);
}
