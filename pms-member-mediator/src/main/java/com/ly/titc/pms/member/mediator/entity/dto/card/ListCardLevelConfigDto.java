package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 14:11
 */
@Data
@Accessors(chain = true)
public class ListCardLevelConfigDto {

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 卡配置ID
     */
    private Long cardId;

    /**
     * 卡配置ID列表
     */
    private List<Long> cardIds;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 卡等级配置
     */
    private String name;
}
