package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.google.common.collect.Lists;
import com.ly.spat.dsf.utils.StringUtil;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.HotelService;
import com.ly.titc.mdm.entity.request.hotel.*;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.HotelInfoResp;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName: HotelDecorator
 * @Description:
 * @since 2022/11/28 13:47
 */
@Slf4j
@Component
public class HotelDecorator extends AbstractDsfServiceProxy {

    private static final String S_NAME = "hotel";

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private HotelService hotelService;

    /**
     * 获取门店信息
     *
     * @param req
     * @return
     */
    public HotelInfoResp getHotelByVid(GetHotelByVidReq req) {
        return Response.getValidateData(hotelService.getHotelByVid(req));
    }


    public List<HotelBaseInfoResp> listHotelBaseInfos(String blocCode, List<String> hotelCodes) {
        return listHotelBaseInfos(blocCode, null, null, hotelCodes);
    }

    public List<HotelBaseInfoResp> listHotelBaseInfos(String blocCode, String hotelName, List<String> hotelCodes) {
        return listHotelBaseInfos(blocCode, null, hotelName, hotelCodes);
    }

    public List<HotelBaseInfoResp> listHotelBaseInfos(String blocCode, String brandCode, String hotelName,
                                                      List<String> hotelCodes) {
        List<Long> hotelVids = null;
        if (!CollectionUtils.isEmpty(hotelCodes)) {
            hotelVids = hotelCodes.stream().map(Long::valueOf).collect(Collectors.toList());
        }
        if (StringUtil.isBlank(blocCode) && StringUtils.isEmpty(brandCode)) {
            return Lists.newArrayList();
        }
        ListHotelBaseInfosReq req = new ListHotelBaseInfosReq();
        req.setHotelVids(hotelVids)
                .setHotelName(hotelName)
                .setBlocCode(blocCode)
                .setBrandCode(brandCode)
                .setTrackingId(UUID.randomUUID().toString());
        ;

        log.info("mdm查询酒店基础信息请求参数，blocCode:{}", blocCode);
        HotelService service = getProxy(HotelService.class, MDM_DSF_GS_NAME, S_NAME, mdmVersion);
        Response<List<HotelBaseInfoResp>> listResponse = service.listHotelBaseInfos(req);
        if (null == listResponse || !RespCodeEnum.CODE_200.getCode().equals(listResponse.getCode())) {
            log.info("mdm查询酒店基础信息请求返回错误，返回值：{}，blocCode:{}", listResponse, blocCode);
            return null;
        }
        return listResponse.getData();
    }

    /**
     * 分页酒店
     *
     * @param req
     * @return
     */
    public Pageable<PageHotelsResp> pageHotels(PageHotelsReq req) {
        //cms和eCms都按hotel_code排序
        req.setIsOrderByHotelCode(1);
        return Response.getValidateData(hotelService.pageHotels(req));
    }

    public List<PageHotelsResp> selectHotelsByFuzzy(SelectHotelsByFuzzyReq req) {
        Response<List<PageHotelsResp>> pageableResponse = hotelService.selectHotelsByFuzzy(req);
        return Response.getValidateData(pageableResponse);
    }

    public List<PageHotelsResp> selectHotelsByFuzzy(String blocCode, String hotelFuzzyNameOrCode, Integer state) {
        SelectHotelsByFuzzyReq req = new SelectHotelsByFuzzyReq();
        req.setTrackingId(UUID.randomUUID().toString());
        req.setBlocCode(blocCode);
        req.setFuzzyNameAndCode(hotelFuzzyNameOrCode);
        req.setState(state);
        Response<List<PageHotelsResp>> pageableResponse = hotelService.selectHotelsByFuzzy(req);
        return Response.getValidateData(pageableResponse);
    }

    /**
     * 获取酒店基础信息列表
     *
     * @param blocCode
     * @param brandCodes
     * @param hotelName
     * @return
     */
    public List<SelectHotelResp> selectHotels(String blocCode, List<String> brandCodes, String hotelName, List<String> hotelCodeList) {
        SelectHotelReq hotelReq = new SelectHotelReq();
        hotelReq.setBlocCode(blocCode)
                .setBrandCodes(brandCodes)
                .setHotelName(hotelName)
                .setTrackingId(UUID.randomUUID().toString());
        if (!CollectionUtils.isEmpty(hotelCodeList)) {
            hotelReq.setHotelVids(hotelCodeList.stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        Response<List<SelectHotelResp>> response = hotelService.selectHotels(hotelReq);
        return Response.getValidateData(response);
    }


    public String getHotelName(String blocCode, String hotelCode) {
        GetHotelByVidReq getHotelByVidReq = new GetHotelByVidReq();
        getHotelByVidReq.setHotelVid(Long.parseLong(hotelCode))
                .setBlocCode(blocCode);
        HotelInfoResp resp = getHotelByVid(getHotelByVidReq);
        return resp.getHotelName();
    }
}
