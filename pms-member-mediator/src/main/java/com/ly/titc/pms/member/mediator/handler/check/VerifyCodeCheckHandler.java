package com.ly.titc.pms.member.mediator.handler.check;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.MemberRelatedConfigEnum;
import com.ly.titc.pms.member.com.enums.SmsSceneEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.GetMemberRelatedConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.MemberRelatedConfigDto;
import com.ly.titc.pms.member.mediator.service.MemberConfigMedService;
import com.ly.titc.pms.member.mediator.service.MessageMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author：rui
 * @name：VerifyCodeCheckHandler
 * @Date：2024-11-25 16:33
 * @Filename：VerifyCodeCheckHandler
 */
@Component
@Slf4j
public class VerifyCodeCheckHandler extends AbstractRegisterCheckHandler {

    @Resource
    private MemberConfigMedService memberConfigMedService;

    @Resource
    private MessageMedService messageMedService;

    @Resource
    private RedisFactory redisFactory;

    @Override
    public Integer getAction() {
        return MemberRegisterCheckEnum.VERIFY_CODE.getAction();
    }

    @Override
    public void check(RegisterMemberDto dto) {
        GetMemberRelatedConfigDto getMemberRelatedConfigDto = new GetMemberRelatedConfigDto();
        getMemberRelatedConfigDto.setMasterType(dto.getMasterType())
                .setMasterCode(dto.getMasterCode())
                .setType(MemberRelatedConfigEnum.PHONE_VERIFY.getCode());
        MemberRelatedConfigDto config = memberConfigMedService.getMemberRelatedConfig(getMemberRelatedConfigDto);
        if (Objects.isNull(config)) {
            log.info("masterType {} masterCode{}  尚未配置短信验证码校验", dto.getMasterType(), dto.getMasterCode());
            return;
        }
        String key = CommonUtil.concat(CommonConstant.REGISTER_VERIFY_CODE_PREFIX, SmsSceneEnum.REGISTER.getScene(),dto.getMobile());
        String verifyCode = redisFactory.getString(key);
        // 如果在校验的过程中，修改了配置改成了不需要，但是之前已经发送了验证码还未过期，这里认为还是需要校验的
        List<String> params = JSON.parseArray(config.getContent(), String.class);
        if (CollectionUtils.isEmpty(params)) {
            log.info("masterType {} masterCode {}  无需短信验证码校验", dto.getMasterType(), dto.getMasterCode());
            return;
        }
        messageMedService.verifyRegisterSms(dto.getMobile(), dto.getVerifyCode(), SmsSceneEnum.REGISTER.getScene());
        redisFactory.del(key);
    }
}
