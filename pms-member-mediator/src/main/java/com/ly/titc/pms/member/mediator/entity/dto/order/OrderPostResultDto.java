package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 11:53
 */
@Data
@Accessors(chain = true)
public class OrderPostResultDto {
    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;


}
