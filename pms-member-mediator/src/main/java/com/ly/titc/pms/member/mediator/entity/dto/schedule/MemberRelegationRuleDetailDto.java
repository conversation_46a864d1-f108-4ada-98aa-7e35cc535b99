package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import com.ly.titc.pms.member.mediator.handler.schedule.AbstractScheduleHandler;
import lombok.Data;

/**
 * @Author：rui
 * @name：MemberRelegationRuleDetailDto
 * @Date：2024-11-21 14:46
 * @Filename：MemberRelegationRuleDetailDto
 */
@Data
public class MemberRelegationRuleDetailDto implements AbstractScheduleHandler.ConditionDetail {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 条件类型： 0-入住次数 1-入住房晚 2-充值金额 3-消费金额（不含赠送） 4-积分 5-平均房费 6-未入住天数 7-注册天数  8-成长值
     */
    private Integer conditionType;

    /**
     * 条件值
     */
    private String conditionValue;

    /**
     * 计算方式计算方式 1 大于等于 2 大于 3 小于等于 4 小于
     */
    private Integer calculateType;

    /**
     * 是否限制渠道：0-不限制，1-限制
     */
    private Integer isRestrictChannel;

    /**
     * 限制渠道；多个渠道英文分号;分隔
     */
    private String restrictChannelCodes;
}
