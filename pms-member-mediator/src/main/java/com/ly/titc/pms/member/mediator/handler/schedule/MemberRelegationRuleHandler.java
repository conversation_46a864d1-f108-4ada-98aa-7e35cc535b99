package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.biz.CardLevelConfigBiz;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.CycleTypeEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.enums.SuccessfulPerformTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDto;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberRelegationRuleHandler
 * @Date：2024-11-21 14:25
 * @Filename：MemberRelegationRuleHandler
 */
@Slf4j
@Component
public class MemberRelegationRuleHandler extends AbstractScheduleHandler<MemberRelegationRuleDto> {

    @Resource
    private ScheduleMedConverter scheduleMedConverter;
    @Resource
    private CardConfigMedService cardConfigMedService;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private MemberCardMedService memberCardMedService;
    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;
    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }
        // 查询每个卡对应的规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelRelegationRuleDto> rules = cardConfigMedService.listCardLevelRelegationRule(relegationRuleDto);
        Map<String, CardLevelRelegationRuleDto> ruleMap = rules.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()), Function.identity()));
        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));
        // 创建缓存对象，避免重复查询
        MemberRecordsCache cache = new MemberRecordsCache();
        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            CardLevelRelegationRuleDto rule = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的保级规则，跳过这张卡
            if (rule == null) {
                log.info("无对应的保级规则，memberNo: {}, cardId: {}, cardLevel: {}", memberNo, cardId, cardLevel);
                continue;
            }
            MemberRelegationRuleDto ruleDto = scheduleMedConverter.convertRelegationRuleDto(rule);
            if (ruleDto == null) {
                continue;
            }
            // 根据统计周期确定开始时间
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            LocalDateTime startDate;
            LocalDateTime endDate = LocalDateTime.now();
            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER.getType())) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            } else {
                // 获取这个会员的上次升降级日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(),
                                ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(), ChangeTypeEnum.SUCCESS.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            }
            log.info("memberNo: {}, startDate: {}", memberNo, startDate);
            // 获取会员的各种记录数据（使用缓存优化，避免重复查询）
            Map<String, List<BaseCheckDto>> memberRecords = getAllMemberRecords(masterType, masterCode, memberNo, startDate, endDate, cache);
            // 检查保级条件
            ConditionCheckResult checkResult;
            if (ruleDto.getRelegationSuccessfulPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                checkResult = checkAllConditionsWithDetail(ruleDto.getDetails(), memberNo,
                        memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                        memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                        memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
            } else {
                checkResult = checkAnyConditionWithDetail(ruleDto.getDetails(), memberNo,
                        memberRecords.get("pointRecords"), memberRecords.get("consumptionRecords"), memberRecords.get("rechargeRecords"),
                        memberRecords.get("checkoutRecords"), memberRecords.get("stayRecords"), memberRecords.get("unstayRecords"),
                        memberRecords.get("avgRoomFeeRecords"), memberRecords.get("registerDaysRecords"));
            }
            log.info("memberNo: {}, checkResult: {}", memberNo, checkResult);
            // 对于降级规则，需要反转结果：如果条件满足则保级成功，如果条件不满足则降级
            boolean shouldDowngrade = !checkResult.isPassed();
            CardLevelConfigWithPrivilegeDto currentLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
            if (currentLevelInfo == null) {
                continue;
            }
            if (shouldDowngrade) {
                // 保级失败，执行降级 - 计算降级目标等级
                Integer targetLevel = calculateDowngradeTargetLevel(memberNo, memberCardInfo.getMemberCardNo(), cardLevel);
                log.info("保级失败，执行降级, memberNo: {}, targetLevel: {}", memberNo, targetLevel);
                // 如果已经是1级或者计算出的目标等级无效，则不降级
                if (targetLevel == null || targetLevel >= cardLevel) {
                    log.info("会员已经是1级或者计算出的目标等级无效，memberNo: {}, targetLevel: {}", memberNo, targetLevel);
                    continue;
                }
                CardLevelConfigInfo cardLevelConfig = cardLevelConfigBiz.getByCardLevel(cardId, targetLevel);
                if (cardLevelConfig == null) {
                    log.info("需要降级的会员卡等级不存在，memberNo: {}, targetLevel: {}", memberNo, targetLevel);
                    continue;
                }
                // 降级会员卡等级
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(currentLevelInfo.getCardLevelName())
                        .setAfterLevel(targetLevel)
                        .setAfterLevelName(cardLevelConfig.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(cardLevelConfig.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(cardLevelConfig.getValidPeriod())))
                        .setIsLongTerm(cardLevelConfig.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.DOWN_AUTO.getType())
                        .setReason("自动降级：" + checkResult.generateReason())
                        .setOperator("自动降级，规则名称：" + ruleDto.getName())
                        .setBizType("SCHEDULE")
                        .setBizNo(String.valueOf(ruleDto.getId()));
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            } else {
                // 保级成功，记录保级成功信息
                String successReason = "保级成功：" + checkResult.generateReason();
                MemberCardLevelChangeRecord changeRecord = new MemberCardLevelChangeRecord();
                changeRecord.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(currentLevelInfo.getCardLevelName())
                        .setAfterLevel(cardLevel) // 保级成功，等级不变
                        .setAfterLevelName(currentLevelInfo.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(currentLevelInfo.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(currentLevelInfo.getValidPeriod())))
                        .setChangeType(ChangeTypeEnum.SUCCESS.getType())
                        .setReason(successReason)
                        .setBizType("SCHEDULE")
                        .setBizNo(String.valueOf(ruleDto.getId()));
                memberCardLevelChangeRecordBiz.add(changeRecord);
            }
        }
    }

    /**
     * 计算降级目标等级
     * 逻辑：降级到升级之前的等级，也就是查询member_card_level_change_record表获取之前的等级，
     * 如果查询不到就减1，但如果已经是1了，就不降级
     */
    private Integer calculateDowngradeTargetLevel(String memberNo, String memberCardNo, Integer currentLevel) {
        // 如果当前等级已经是1，不能再降级
        if (currentLevel <= 1) {
            return null;
        }
        // 查询该会员卡的最近一次升级记录
        MemberCardLevelChangeRecord lastUpgradeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                Arrays.asList(ChangeTypeEnum.UPGRADE_AUTO.getType(), ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(),
                        ChangeTypeEnum.UPGRADE_PURCHASE.getType()));
        // 如果找到了升级记录，且升级后的等级是当前等级，则降级到升级前的等级
        if (lastUpgradeRecord != null && lastUpgradeRecord.getMemberCardNo().equals(memberCardNo)
                && lastUpgradeRecord.getAfterLevel().equals(currentLevel)) {
            return lastUpgradeRecord.getPreLevel();
        }
        // 如果没有找到升级记录，则降级到当前等级-1
        return currentLevel - 1;
    }




    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_RELEGATION_RULE.getAction();
    }
}
