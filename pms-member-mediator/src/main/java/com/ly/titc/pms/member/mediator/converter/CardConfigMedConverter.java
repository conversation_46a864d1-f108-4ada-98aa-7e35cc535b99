package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.bo.*;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PagePrivilegeConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDto;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25 14:21
 */
@Mapper(componentModel = "spring")
public interface CardConfigMedConverter {

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardConfigInfo convertDtoToPo(SaveCardConfigDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardNoRuleInfo convertDtoToPo(SaveCardNoRuleDto dto, Long cardId, String operator);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardApplicableDataMapping convertDtoToPo(SaveCardApplicableDataMappingDto mapping, Long cardId, String operator);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelUpgradeRuleInfo convertDtoToPo(SaveCardLevelUpgradeRuleDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "upgradeRuleId", source = "ruleId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelUpgradeRuleDetailInfo convertDtoToPo(SaveCardLevelUpgradeRuleDetailDto ruleDetail, Long cardId, Long ruleId, String operator);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelRelegationRuleInfo convertDtoToPo(SaveCardLevelRelegationRuleDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "relegationRuleId", source = "ruleId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelRelegationRuleDetailInfo convertDtoToPo(SaveCardLevelRelegationRuleDetailDto detail, Long cardId, Long ruleId, String operator);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelConfigInfo convertDtoToPo(SaveCardLevelConfigDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "cardLevel", source = "cardLevel"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelPrivilegeConfigInfo convertDtoToPo(SaveCardLevelPrivilegeConfigDto config, Long cardId, Integer cardLevel, String operator);

    ListCardLevelConfigBo convertDtoToBo(ListCardLevelConfigDto dto);

    CardLevelConfigWithPrivilegeDto convertPoToDto(CardLevelConfigInfo cardLevelConfigInfo);

    CardConfigDto convertPoToDto(CardConfigInfo defaultCard);

    PageCardLevelConfigBo convertDtoToBo(PageCardLevelConfigDto dto);

    CardNoRuleDto convertPoToDto(CardNoRuleInfo cardNoRuleInfo);

    CardLevelUpgradeRuleDto convertPoToDto(CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo);

    CardLevelUpgradeRuleDetailDto convertPoToDto(CardLevelUpgradeRuleDetailInfo cardLevelUpgradeRuleDetailInfo);

    PageCardLevelUpgradeRuleBo convertDtoToBo(PageCardLevelUpgradeRuleDto dto);

    default List<CardLevelUpgradeRuleDto> convertUpgradeRuleResp(List<CardLevelUpgradeRuleInfo> ruleInfos,
                                                                 List<CardConfigInfo> cardConfigInfos,
                                                                 Map<String, String> cardLevelMap) {
        List<CardLevelUpgradeRuleDto> respList = ruleInfos.stream().map(this::convertPoToDto).collect(Collectors.toList());
        Map<Long, CardConfigInfo> cardConfigMap = cardConfigInfos.stream().collect(Collectors.toMap(CardConfigInfo::getId, Function.identity()));
        respList.forEach(item -> {
            CardConfigInfo cardConfigInfoDto = cardConfigMap.get(item.getCardId());
            if (cardConfigInfoDto != null) {
                item.setCardName(cardConfigInfoDto.getCardName());
            }
            item.setSourceLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getSourceLevel())));
            item.setTargetLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getTargetLevel())));
        });
        return respList;
    }

    CardLevelRelegationRuleDto convertPoToDto(CardLevelRelegationRuleInfo cardLevelRelegationRuleInfo);

    CardLevelRelegationRuleDetailDto convertPoToDto(CardLevelRelegationRuleDetailInfo cardLevelRelegationRuleDetailInfo);

    PageCardLevelRelegationRuleBo convertDtoToBo(PageCardLevelRelegationRuleDto dto);

    default List<CardLevelRelegationRuleDto> convertRelegationRuleResp(List<CardLevelRelegationRuleInfo> ruleInfos,
                                                                       List<CardConfigInfo> cardConfigInfos,
                                                                       Map<String, String> cardLevelMap) {
        List<CardLevelRelegationRuleDto> respList = ruleInfos.stream().map(this::convertPoToDto).collect(Collectors.toList());
        Map<Long, CardConfigInfo> cardConfigMap = cardConfigInfos.stream().collect(Collectors.toMap(CardConfigInfo::getId, Function.identity()));
        respList.forEach(item -> {
            CardConfigInfo cardConfigInfoDto = cardConfigMap.get(item.getCardId());
            if (cardConfigInfoDto != null) {
                item.setCardName(cardConfigInfoDto.getCardName());
            }
            item.setSourceLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getSourceLevel())));
            item.setTargetLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getTargetLevel())));
        });
        return respList;
    }

    CardLevelPrivilegeConfigDto convertPoToDto(CardLevelPrivilegeConfigInfo info);

    @Mappings({
            @Mapping(target = "scopeValues", ignore = true)
    })
    CardFullConfigDto convertCardConfigDetailDto(CardConfigDto cardConfigResp);


    default List<CardFullConfigDto> convertCardFullConfig(List<CardConfigDto> memberCardConfigs,
                                                          List<CardLevelPrivilegeConfigInfo> cardLevelPrivilegeConfigInfos,
                                                          List<PrivilegeConfigInfo> privilegeConfigInfos,
                                                          List<CardLevelUpgradeRuleDto> cardLevelUpgradeRules,
                                                          List<CardLevelRelegationRuleDto> cardLevelRelegationRules){
        Map<String, List<CardLevelPrivilegeConfigInfo>> cardPrivilegeConfigMap = cardLevelPrivilegeConfigInfos.stream().collect(Collectors.groupingBy(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel())));
        Map<Long, PrivilegeConfigInfo> privilegeConfigMap = privilegeConfigInfos.stream().collect(Collectors.toMap(PrivilegeConfigInfo::getId, Function.identity()));
        Map<Long, List<CardLevelUpgradeRuleDto>> upgradeRuleMap = cardLevelUpgradeRules.stream().collect(Collectors.groupingBy(CardLevelUpgradeRuleDto::getCardId));
        Map<Long, List<CardLevelRelegationRuleDto>> relegationRuleMap = cardLevelRelegationRules.stream().collect(Collectors.groupingBy(CardLevelRelegationRuleDto::getCardId));
        return memberCardConfigs.stream().map(cardConfigResp -> {
            CardFullConfigDto memberCardConfigResponse = convertCardConfigDetailDto(cardConfigResp);
            if (CollectionUtils.isNotEmpty(cardConfigResp.getScopeValues())) {
                memberCardConfigResponse.setScopeValues(cardConfigResp.getScopeValues());
            }
            for (CardLevelConfigWithPrivilegeDto cardLevelConfig : memberCardConfigResponse.getCardLevelConfigs()) {
                List<CardLevelPrivilegeConfigInfo> privilegeItemList = cardPrivilegeConfigMap.getOrDefault(String.format("%s-%s", cardConfigResp.getId(), cardLevelConfig.getCardLevel()), new ArrayList<>());
                if (CollectionUtils.isNotEmpty(privilegeItemList)) {
                    cardLevelConfig.setPrivileges(privilegeItemList.stream().map(item -> {
                        PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigMap.get(item.getPrivilegeId());
                        return convertPoToDto(privilegeConfigInfo);
                    }).collect(Collectors.toList()));
                }
            }
            Map<Integer, String> cardLevelNameMap =  cardConfigResp.getCardLevelConfigs().stream().collect(Collectors.toMap(CardLevelConfigWithPrivilegeDto::getCardLevel, CardLevelConfigWithPrivilegeDto::getCardLevelName));
            memberCardConfigResponse.setCardLevelUpgradeRules(upgradeRuleMap.getOrDefault(cardConfigResp.getId(), Collections.emptyList())
                    .stream().sorted(Comparator.comparing(CardLevelUpgradeRuleDto::getSort)
                            .reversed().thenComparing(CardLevelUpgradeRuleDto::getSourceLevel).reversed()).collect(Collectors.toList()));
            memberCardConfigResponse.getCardLevelUpgradeRules().forEach(item -> {
                item.setSourceLevelName(cardLevelNameMap.get(item.getSourceLevel()));
                item.setTargetLevelName(cardLevelNameMap.get(item.getTargetLevel()));
            });
            memberCardConfigResponse.setCardLevelRelegationRules(relegationRuleMap.getOrDefault(cardConfigResp.getId(), Collections.emptyList())
                    .stream().sorted(Comparator.comparing(CardLevelRelegationRuleDto::getSort)
                            .reversed().thenComparing(CardLevelRelegationRuleDto::getTargetLevel)).collect(Collectors.toList()));
            memberCardConfigResponse.getCardLevelRelegationRules().forEach(item -> {
                item.setTargetLevelName(cardLevelNameMap.get(item.getTargetLevel()));
                item.setSourceLevelName(cardLevelNameMap.get(item.getSourceLevel()));
            });
            memberCardConfigResponse.setCardLength(cardConfigResp.getCardNoRule().getCardLength());
            memberCardConfigResponse.setExcludeNumber(cardConfigResp.getCardNoRule().getExcludeNumber());
            memberCardConfigResponse.setCardPrefix(cardConfigResp.getCardNoRule().getCardPrefix());
            return memberCardConfigResponse;
        }).collect(Collectors.toList());
    }

    PrivilegeConfigDto convertPoToDto(PrivilegeConfigInfo privilegeConfigInfo);

    default PrivilegeConfigDto convertPoToDto(PrivilegeConfigInfo privilegeConfigInfo, List<PrivilegeApplicableDataMapping> mappings){
        PrivilegeConfigDto resp = convertPoToDto(privilegeConfigInfo);
        Map<String, List<PrivilegeApplicableDataMapping>> map = CollectionUtils.isEmpty(mappings) ? Collections.emptyMap() : mappings.stream().collect(Collectors.groupingBy(item -> String.format("$%s-%s-%s", item.getClassification(), item.getValue(), item.getScopeType())));
        List<PrivilegeConfigDetailDto> scopeValues = new ArrayList<>();
        map.forEach((key, value) -> {
            PrivilegeConfigDetailDto detailResp = new PrivilegeConfigDetailDto()
                    .setScopeType(value.get(0).getScopeType())
                    .setValue(value.get(0).getValue())
                    .setApplicationType(value.get(0).getApplicationType())
                    .setPrivilegeId(resp.getId())
                    .setClassification(value.get(0).getClassification())
                    .setScopeType(value.get(0).getScopeType());
            if (value.get(0).getScopeType() == 0) {
                detailResp.setScopeValues(value.stream().map(PrivilegeApplicableDataMapping::getScopeValue).collect(Collectors.toList()));
            }
            scopeValues.add(detailResp);
        });
        resp.setScopeValues(scopeValues);
        return resp;
    }

    PagePrivilegeConfigBo convertDtoToBo(PagePrivilegeConfigDto dto);
}
