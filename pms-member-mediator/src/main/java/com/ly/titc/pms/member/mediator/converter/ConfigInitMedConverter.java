package com.ly.titc.pms.member.mediator.converter;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.enums.*;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27 09:36
 */
@Mapper(componentModel = "spring")
public interface ConfigInitMedConverter {
    default List<PrivilegeConfigInfo> initCommonPrivilege(Integer masterType, String masterCode) {
        return Arrays.asList(
                new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.OFFLINE.getType()).setClassification(PrivilegeClassificationEnum.SHOW.getType()).setState(StatusEnum.VALID.getStatus()).setName("免费早餐").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.INTEGRAL.getType()).setClassification(PrivilegeClassificationEnum.SHOW.getType()).setState(StatusEnum.VALID.getStatus()).setName("多倍积分").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.OFFLINE.getType()).setClassification(PrivilegeClassificationEnum.SHOW.getType()).setState(StatusEnum.VALID.getStatus()).setName("免押金").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.OFFLINE.getType()).setClassification(PrivilegeClassificationEnum.SHOW.getType()).setState(StatusEnum.VALID.getStatus()).setName("免查房").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.OFFLINE.getType()).setClassification(PrivilegeClassificationEnum.SHOW.getType()).setState(StatusEnum.VALID.getStatus()).setName("生日好礼").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.OFFLINE.getType()).setClassification(PrivilegeClassificationEnum.SHOW.getType()).setState(StatusEnum.VALID.getStatus()).setName("免费取消").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER)
        );
    }

    default PrivilegeConfigInfo initPricePrivilege(Integer masterType, String masterCode) {
        return new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId())
                .setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.PRICE.getType())
                .setClassification(PrivilegeClassificationEnum.DISCOUNT.getType()).setState(StatusEnum.VALID.getStatus()).setName("房费折扣")
                .setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }

    default PrivilegeApplicableDataMapping initPricePrivilegeApplicableDataMapping(Long privilegeId) {
        return new PrivilegeApplicableDataMapping().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId())
                .setPrivilegeId(privilegeId).setApplicationType(3).setScopeType(ApplicationScopeTypeEnum.ALL.getType())
                .setValue("10").setClassification(PrivilegeClassificationEnum.DISCOUNT.getType()).setCreateUser(SystemConstant.SYSTEM_USER)
                .setModifyUser(SystemConstant.SYSTEM_USER);
    }

    default PrivilegeConfigInfo initReservePrivilege(Integer masterType, String masterCode) {
        return new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId())
                .setMasterType(masterType).setMasterCode(masterCode).setType(PrivilegeTypeEnum.OFFLINE.getType())
                .setClassification(PrivilegeClassificationEnum.RESERVE.getType()).setState(StatusEnum.VALID.getStatus()).setName("预订保留")
                .setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }
    default PrivilegeApplicableDataMapping initReservePrivilegeApplicableDataMapping(Long privilegeId) {
        return new PrivilegeApplicableDataMapping().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId())
                .setPrivilegeId(privilegeId).setApplicationType(3).setScopeType(ApplicationScopeTypeEnum.ALL.getType())
                .setValue("18:00").setClassification(PrivilegeClassificationEnum.RESERVE.getType())
                .setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }

    default PrivilegeConfigInfo initDelayPrivilege(Integer masterType, String masterCode) {
        return new PrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode)
                .setType(PrivilegeTypeEnum.OFFLINE.getType()).setClassification(PrivilegeClassificationEnum.DELAY.getType()).setState(StatusEnum.VALID.getStatus())
                .setName("延迟退房").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }

    default PrivilegeApplicableDataMapping initDelayPrivilegeApplicableDataMapping(Long privilegeId) {
        return new PrivilegeApplicableDataMapping().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setPrivilegeId(privilegeId).setApplicationType(3).setScopeType(ApplicationScopeTypeEnum.ALL.getType())
                .setValue("12:00").setClassification(PrivilegeClassificationEnum.DELAY.getType()).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }

    default List<MemberTagConfigInfo> initMemberTag(Integer masterType, String masterCode) {
        return Arrays.asList(
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.CUSTOMER.getType()).setName("新客户").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.CUSTOMER.getType()).setName("高价值客户").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.CUSTOMER.getType()).setName("流失客户").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.ROOM_PREFERENCE.getType()).setName("无烟房").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.ROOM_PREFERENCE.getType()).setName("低楼层").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.DINING_PREFERENCE.getType()).setName("不吃辣").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.DINING_PREFERENCE.getType()).setName("不吃香菜").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.INTERESTS.getType()).setName("运动").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.INTERESTS.getType()).setName("游戏").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new MemberTagConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setType(MemberTagEnum.INTERESTS.getType()).setName("旅游").setSort(0).setState(1).setMarkType(2).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER)
        );
    }

    /**
     * 初始化会员卡
     */
    default CardConfigInfo initMemberCard(Integer masterType, String masterCode) {
        return new CardConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode)
                .setCardName("会员卡").setBlocCode(masterType.equals(MasterTypeEnum.BLOC.getType()) ? masterCode : "").setCardType(MemberCardTypeEnum.BASIC.getType()).setState(1).setSort(0).setIsDefault(1)
                .setApplicationScope(ApplicationScopeTypeEnum.ALL.getType()).setApplicationType(3).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }

    /**
     * 初始化会员卡规则
     */
    default CardNoRuleInfo initMemberCardNoRule(Long cardId) {
        return new CardNoRuleInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setCardId(cardId)
                .setCardPrefix("").setCardLength(9).setExcludeNumber("").setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }

    /**
     * 初始化会员卡等级
     */
    default List<CardLevelConfigInfo> initMemberCardLevel(Integer masterType, String masterCode, Long cardId) {
        return Arrays.asList(
                new CardLevelConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setCardId(cardId).setCardDiscount(100).setCardLevelName("普通会员").setCardLevelDesc("普通会员").setCardLevel(1).setIsLongTerm(1).setState(1).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new CardLevelConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setCardId(cardId).setCardDiscount(100).setCardLevelName("银会员").setCardLevelDesc("银会员").setCardLevel(2).setIsLongTerm(1).setState(1).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new CardLevelConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setCardId(cardId).setCardDiscount(100).setCardLevelName("金会员").setCardLevelDesc("金会员").setCardLevel(3).setIsLongTerm(1).setState(1).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new CardLevelConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setCardId(cardId).setCardDiscount(100).setCardLevelName("白金会员").setCardLevelDesc("白金会员").setCardLevel(4).setIsLongTerm(1).setState(1).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER),
                new CardLevelConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setCardId(cardId).setCardDiscount(100).setCardLevelName("钻石会员").setCardLevelDesc("钻石会员").setCardLevel(5).setIsLongTerm(1).setState(1).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER)
        );
    }

    default List<CardLevelPrivilegeConfigInfo> initMemberCardPrivilege(List<CardLevelConfigInfo> levelList, Long pricePrivilegeId) {
        return levelList.stream().map(level -> new CardLevelPrivilegeConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setCardLevel(level.getCardLevel()).setCardId(level.getCardId()).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER)
                .setPrivilegeId(pricePrivilegeId)).collect(Collectors.toList());
    }

    default MemberRelatedConfigInfo initMemberRelatedConfig(Integer masterType, String masterCode) {
        List<String> keys = Arrays.asList("realName", "mobile", "cardLevel", "cardNo", "idNo",
                "nickName", "gender", "email", "birthday", "nation", "nationality", "nativePlace", "address", "remark");
        //  姓名、称呼、手机号、邮箱、会员等级、会员卡号、证件号、性别、出生日期、民族、国家、籍贯、地址、备注
        // 姓名、手机号、会员等级、会员卡号、证件号 必填, 称呼、性别、 邮箱、出生日期、民族、国家、籍贯、地址、备注非必填
        return new MemberRelatedConfigInfo().setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()).setMasterType(masterType).setMasterCode(masterCode).setContent(JSON.toJSONString(keys)).setType(MemberRelatedConfigEnum.REGISTER.getCode()).setCreateUser(SystemConstant.SYSTEM_USER).setModifyUser(SystemConstant.SYSTEM_USER);
    }
}
