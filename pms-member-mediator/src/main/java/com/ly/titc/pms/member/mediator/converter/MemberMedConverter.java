package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.BindMemberReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.RegisterCustomerReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.member.com.enums.GenderEnum;
import com.ly.titc.pms.member.com.enums.IdTypeEnum;
import com.ly.titc.pms.member.com.enums.PrivilegeTypeEnum;
import com.ly.titc.pms.member.dal.entity.bo.PageMemberParamBo;
import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.HotelTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.entity.wrapper.MemberRegisterWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberSaveWrapper;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelPrivilegeConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.CheckInStatisticsDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.PageMemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.StatisticsCouponReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.CouponStatisticsResp;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员medConverter
 *
 * <AUTHOR>
 * @date 2024/10/29 10:54
 */
@Mapper(componentModel = "spring")
public interface MemberMedConverter {

    @Mappings({
            @Mapping(target = "memberNo", source = "memberInfo.memberNo"),
            @Mapping(target = "createUser", source = "memberInfo.createUser"),
            @Mapping(target = "modifyUser", source = "memberInfo.modifyUser"),
            @Mapping(target = "gmtCreate", source = "memberInfo.gmtCreate"),
            @Mapping(target = "gmtModified", source = "memberInfo.gmtModified"),
            @Mapping(target = "tagNames", source = "tagNames"),
            @Mapping(target = "memberCardInfos", source = "memberCardInfos"),
    })
    MemberDetailDto convertPoToDto(MemberInfo memberInfo, MemberExtendInfo memberExtendInfo, MemberContactInfo memberContactInfo, List<String> tagNames, List<MemberCardInfoDto> memberCardInfos);

    default MemberRegisterWrapper convertDtoToWrapper(RegisterMemberDto dto, String memberNo) {
        MemberInfo memberInfo = convertDtoToPo(dto, memberNo);
        MemberExtendInfo memberExtendInfo = convertDtoToExtendPo(dto, memberNo);
        MemberContactInfo memberContactInfo = convertDtoToContactPo(dto.getMemberContactInfo(), memberNo);
        return convertPoToWrapper(memberInfo, memberExtendInfo, memberContactInfo);
    }

    MemberRegisterWrapper convertPoToWrapper(MemberInfo memberInfo, MemberExtendInfo memberExtendInfo, MemberContactInfo memberContactInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "dto.operator"),
            @Mapping(target = "modifyUser", source = "dto.operator"),
            @Mapping(target = "gmtCreate", source = "dto.registerDate"),
            @Mapping(target = "memberNo", source = "memberNo")
    })
    MemberInfo convertDtoToPo(RegisterMemberDto dto, String memberNo);

    @Mappings({
            @Mapping(target = "memberNo", source = "memberNo")
    })
    MemberExtendInfo convertDtoToExtendPo(RegisterMemberDto dto, String memberNo);

    @Mappings({
            @Mapping(target = "memberNo", source = "memberNo")
    })
    MemberContactInfo convertDtoToContactPo(MemberContactInfoDto dto, String memberNo);

    MemberSaveWrapper convertDtoToWrapper(UpdateMemberInfoDto dto);

    MemberEventMsg convertPoToEventMsg(MemberInfoDto memberInfo);

    MemberEventMsg convertPoToEventMsg(MemberInfo memberInfo, MemberEventEnum eventType);

    MemberEventMsg convertPoToEventMsg(Integer masterType, String masterCode, String memberNo, MemberEventEnum eventType);

    MemberEventMsg convertPoToEventMsg(IssueMemberCardDto dto);

    MemberInfoDto convertPoToDto(MemberInfo memberInfo);

    List<MemberInfoDto> convert(List<MemberInfo> infos);

    PageMemberParamBo convertDtoToBo(PageMemberParamDto pageMemberParamDto);

    GetByCustomerNoReq convertDtoToReq(CustomerInfoDto customerInfo);

    BindMemberReq convertDtoToReq(CustomerInfoDto customerInfo, String memberNo);


    default void fillCardConfig(MemberDetailDto memberDetailDto,
                                Map<Long, CardConfigDto> cardConfigMap,
                                Map<String, HotelBaseInfoResp> hotelMap,
                                CheckInStatisticsDto checkInStatistics,
                                boolean blacklistFlag) {
        memberDetailDto.setIdTypeStr(IdTypeEnum.getNameByType(memberDetailDto.getIdType()));
        memberDetailDto.setGenderStr(GenderEnum.getNameByType(memberDetailDto.getGender()));
        memberDetailDto.setCheckInCount(checkInStatistics == null ? 0 : checkInStatistics.getCheckInCount());
        memberDetailDto.setBlackFlag(blacklistFlag ? Constant.ONE : Constant.ZERO);
        for (MemberCardInfoDto memberCardInfo : memberDetailDto.getMemberCardInfos()) {
            CardConfigDto memberCardConfigResp = cardConfigMap.get(memberCardInfo.getCardId());
            if (Objects.nonNull(memberCardConfigResp) && CollectionUtils.isNotEmpty(memberCardConfigResp.getCardLevelConfigs())) {
                Map<Integer, CardLevelConfigWithPrivilegeDto> cardLevelMap = memberCardConfigResp.getCardLevelConfigs().stream()
                        .collect(Collectors.toMap(CardLevelConfigWithPrivilegeDto::getCardLevel, Function.identity()));
                memberCardInfo.setCardName(memberCardConfigResp.getCardName());
                CardLevelConfigWithPrivilegeDto memberCardLevelConfigInfo = cardLevelMap.get(memberCardInfo.getCardLevel());
                if (memberCardLevelConfigInfo != null) {
                    memberCardInfo.setCardLevelName(memberCardLevelConfigInfo.getCardLevelName());
                    memberCardInfo.setValidPeriod(memberCardLevelConfigInfo.getValidPeriod());
                }
            }

            if (memberCardInfo.getIssueHotelType().equals("BLOC")) {
                memberCardInfo.setIssueHotelName("集团");
            } else {
                if (Objects.nonNull(hotelMap.get(memberCardInfo.getIssueHotel()))) {
                    memberCardInfo.setIssueHotelName(hotelMap.get(memberCardInfo.getIssueHotel()).getHotelName());
                } else {
                    memberCardInfo.setIssueHotelName("");
                }
            }
        }
        if (memberDetailDto.getRegisterHotelType().equals(HotelTypeEnum.HOTEL.getType())) {
            if (Objects.nonNull(hotelMap.get(memberDetailDto.getRegisterHotel()))) {
                memberDetailDto.setRegisterHotelName(hotelMap.get(memberDetailDto.getRegisterHotel()).getHotelName());
            } else {
                memberDetailDto.setRegisterHotelName("");
            }
        } else {
            memberDetailDto.setRegisterHotelName("集团");
        }

    }

    default MemberIdentityBaseInfoDto convert(MemberInfo memberInfo, List<MemberCardInfoDto> cardInfoDtos, Map<Long, CardConfigDto> cardConfigMap) {
        MemberIdentityBaseInfoDto memberIdentityBaseInfoDto = convertPoToBaseDto(memberInfo);
        for (MemberCardInfoDto cardInfoDto : cardInfoDtos) {
            CardConfigDto memberCardConfigResp = cardConfigMap.get(cardInfoDto.getCardId());
            if (Objects.nonNull(memberCardConfigResp) && CollectionUtils.isNotEmpty(memberCardConfigResp.getCardLevelConfigs())) {
                if (memberCardConfigResp.getIsDefault().equals(StateEnum.VALID.getState())) {
                    Map<Integer, CardLevelConfigWithPrivilegeDto> cardLevelMap = memberCardConfigResp.getCardLevelConfigs().stream()
                            .collect(Collectors.toMap(CardLevelConfigWithPrivilegeDto::getCardLevel, Function.identity()));
                    cardInfoDto.setCardName(memberCardConfigResp.getCardName());
                    CardLevelConfigWithPrivilegeDto memberCardLevelConfigInfo = cardLevelMap.get(cardInfoDto.getCardLevel());
                    if (memberCardLevelConfigInfo != null) {
                        cardInfoDto.setCardLevelName(memberCardLevelConfigInfo.getCardLevelName());
                        cardInfoDto.setValidPeriod(memberCardLevelConfigInfo.getValidPeriod());
                    }
                    memberIdentityBaseInfoDto.setMemberCardInfo(cardInfoDto);
                }
            }

        }
        return memberIdentityBaseInfoDto;


    }

    MemberIdentityBaseInfoDto convertPoToBaseDto(MemberInfo memberInfo);

    default GetMemberUsableReq convert(GetUsableMemberDto dto) {
        GetMemberUsableReq req = new GetMemberUsableReq();
        req.setMemberNo(dto.getMemberNo());
        req.setMasterType(dto.getScopeMasterType());
        req.setMasterCode(dto.getScopeMasterCode());
        req.setPlatformChannel(dto.getPlatformChannel());
        req.setTrackingId(dto.getTrackingId());
        return req;
    }

    default MemberDefaultCardFullInfoDto convert(MemberInfo memberInfo, MemberCardInfoDto memberCardInfo,
                                                 MemberStoreAccountResp storeAccountResp,
                                                 MemberPointAccountResp pointAccountResp,
                                                 CouponStatisticsResp couponStatisticsResp,
                                                 List<CardLevelPrivilegeConfigDto> privilegeConfigResps) {
        MemberDefaultCardFullInfoDto dto = convertBase(memberInfo);
        dto.setIdTypeStr(IdTypeEnum.getNameByType(memberInfo.getIdType()));
        dto.setGenderStr(GenderEnum.getNameByType(memberInfo.getGender()));
        dto.setMemberCardInfo(memberCardInfo);
        dto.setStoreAccount(storeAccountResp);
        dto.setPointAccount(pointAccountResp);
        dto.setCouponStatisticsResp(couponStatisticsResp);
        dto.setPrivileges(privilegeConfigResps);
        return dto;

    }

    default List<CardLevelPrivilegeConfigDto> orderPrivileges(List<CardLevelPrivilegeConfigDto> privileges) {
        List<CardLevelPrivilegeConfigDto> orderPrivileges = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(privileges)) {
            return privileges.stream().filter(privilege -> PrivilegeTypeEnum.getOrderPrivilegeType().contains(privilege.getType())).collect(Collectors.toList());
        }
        return orderPrivileges;
    }

    MemberDefaultCardFullInfoDto convertBase(MemberInfo memberInfo);

    default StatisticsCouponReq convertCouReq(GetUsableMemberDto dto) {
        StatisticsCouponReq apiReq = new StatisticsCouponReq();
        apiReq.setReceiverType(ReceiverTypeEnum.MEMBER.getCode());
        apiReq.setReceiverCode(dto.getMemberNo());
        apiReq.setBlocCode(dto.getBlocCode());
        apiReq.setHotelCode(dto.getScopeMasterCode());
        apiReq.setTrackingId(dto.getTrackingId());
        return apiReq;
    }

    RegisterCustomerReq convertRegisterCustomerReq(MemberInfo memberInfo);

    PageMemberDocumentDto convertPageMemberDocumentDto(PageMemberParamDto pageMemberParamDto);
}
