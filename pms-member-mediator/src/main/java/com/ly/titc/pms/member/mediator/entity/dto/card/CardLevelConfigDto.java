package com.ly.titc.pms.member.mediator.entity.dto.card;

import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDto;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员卡等级配置
 *
 * @Author：rui
 * @name：MemberCardLevelConfigInfoResponse
 * @Date：2024-11-7 17:55
 * @Filename：MemberCardLevelConfigInfoResponse
 */
@Data
public class CardLevelConfigDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡名称
     */
    private String cardName;

    /**
     * 会员等级
     */
    private Integer cardLevel;

    /**
     * 会员等级名称
     */
    private String cardLevelName;

    /**
     * 会员等级描述信息
     */
    private String cardLevelDesc;

    /**
     * 有效期
     */
    private Integer validPeriod;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 卡费
     */
    private BigDecimal cardPrice;

    /**
     * 折扣
     */
    private Integer cardDiscount;

    /**
     * 会员保级类型：0-永久保级;1-不保级;2-条件保级
     */
    private Integer relegationType;

    /**
     * 等级背景图标
     */
    private String levelImage;

    /**
     * 状态；0 无效 1 正常
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


}
