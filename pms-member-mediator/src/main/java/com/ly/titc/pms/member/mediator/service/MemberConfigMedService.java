package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.*;

import java.util.List;

/**
 * 会员配置
 *
 * <AUTHOR>
 * @date 2025/6/25 17:05
 */
public interface MemberConfigMedService {

    /**
     * 保存会员相关配置
     *
     * @param dto
     * @return
     */
    Long saveMemberRelatedConfig(SaveMemberRelatedConfigDto dto);

    /**
     * 查询会员相关配置
     *
     * @param dto
     * @return
     */
    MemberRelatedConfigDto getMemberRelatedConfig(GetMemberRelatedConfigDto dto);

    /**
     * 保存标签
     *
     * @return dto
     */
    Long saveTagConfig(SaveMemberTagConfigDto dto);

    /**
     * 删除标签
     *
     * @param dto
     * @return
     */
    void deleteTagConfig(DeleteMemberTagDto dto);

    /**
     * 分页查询标签
     *
     * @param dto
     * @return
     */
    Pageable<MemberTagConfigDetailDto> pageTagConfig(PageMemberTagConfigDto dto);

    /**
     * 查询标签信息列表
     *
     * @param dto
     * @return
     */
    List<MemberTagConfigDetailDto> listTagConfig(ListMemberTagConfigDto dto);

    /**
     * 查询标签详情
     *
     * @param id
     * @return 详情
     */
    MemberTagConfigDetailDto getTagConfig(Integer masterType, String masterCode, Long id);

}
