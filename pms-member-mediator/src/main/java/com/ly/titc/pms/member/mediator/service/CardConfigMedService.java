package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDto;

import java.util.List;

/**
 * 卡配置
 *
 * <AUTHOR>
 * @date 2025/6/25 13:42
 */
public interface CardConfigMedService {

    /**
     * 保存会员卡配置
     *
     * @return id
     */
    Long saveCardConfig(SaveCardConfigDto dto);

    /**
     * 删除会员卡
     *
     * @param dto
     * @return
     */
    void deleteCardConfig(DeleteCardConfigDto dto);

    /**
     * 查询默认卡
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    CardConfigDto getDefaultCard(Integer masterType, String masterCode);

    /**
     * 查询卡列表
     *
     * @param cardIds
     * @return
     */
    List<CardConfigDto> listCardConfig(Integer masterType, String masterCode, List<Long> cardIds);

    /**
     * 保存会员卡升级规则
     *
     * @return id
     */
    Long saveCardLevelUpgradeRule(SaveCardLevelUpgradeRuleDto dto);

    /**
     * 保存会员卡降级规则
     *
     * @return id
     */
    Long saveCardLevelRelegationRule(SaveCardLevelRelegationRuleDto dto);

    /**
     * 保存会员卡等级
     *
     * @return id
     */
    Long saveCardLevelConfig(SaveCardLevelConfigDto dto);

    /**
     * 删除会员卡等级
     *
     * @param dto
     * @return
     */
    void deleteCardLevelConfig(DeleteCardLevelConfigDto dto);

    /**
     * 查询卡等级配置
     *
     * @param dto
     * @return
     */
    List<CardLevelConfigWithPrivilegeDto> listCardLevelConfig(ListCardLevelConfigDto dto);

    /**
     * 分页查询会员等级
     *
     * @param dto
     * @return
     */
    Pageable<CardLevelConfigWithPrivilegeDto> pageCardLevelConfig(PageCardLevelConfigDto dto);

    /**
     * 查询卡等级权益
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @param cardLevel
     * @return
     */
    List<PrivilegeConfigDto> listCardLevelPrivilege(Integer masterType, String masterCode, Long cardId, Integer cardLevel);

    /**
     * 查询会员卡等级详情
     *
     * @param levelId
     * @return
     */
    CardLevelConfigWithPrivilegeDto getCardLevelConfig(Integer masterType, String masterCode, Long levelId);

    /**
     * 启停操作会员卡等级
     *
     * @param dto
     * @return
     */
    void actionCardLevel(ActionCardLevelDto dto);

    /**
     * 根据归属查询会员卡模板
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    List<CardConfigDto> listCardConfig(Integer masterType, String masterCode);

    /**
     * 查询卡详情
     *
     * @param cardId
     * @return
     */
    CardConfigDto getCardConfig(Integer masterType, String masterCode, Long cardId);

    /**
     * 根据归属查询会员卡模板
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @return
     */
    CardFullConfigDto getCardFullConfig(Integer masterType, String masterCode, Long cardId);

    /**
     * 根据归属查询会员卡模板
     *
     * @param masterType
     * @param masterCode
     * @param cardIds
     * @return
     */
    List<CardFullConfigDto> listCardFullConfig(Integer masterType, String masterCode, List<Long> cardIds);

    /**
     * 根据卡和等级查询升级规则
     *
     * @param dto
     * @return 升级规则
     */
    CardLevelUpgradeRuleDto getCardLevelUpgradeRule(GetCardLevelUpgradeRuleDto dto);

    /**
     * 批量查询卡升级规则
     *
     * @param dto
     * @return
     */
    List<CardLevelUpgradeRuleDto> listCardLevelUpgradeRule(ListCardLevelUpgradeRuleDto dto);

    /**
     * 分页查询会员卡升级规则
     *
     * @param dto
     * @return 分页结果
     */
    Pageable<CardLevelUpgradeRuleDto> pageUpgradeRule(PageCardLevelUpgradeRuleDto dto);

    /**
     * 升级规则详情
     *
     * @param id
     * @return
     */
    CardLevelUpgradeRuleDto getUpgradeRule(Integer masterType, String masterCode, Long id);

    /**
     * 根据卡和等级查询降级规则
     *
     * @param dto
     * @return 降级规则
     */
    CardLevelRelegationRuleDto getRelegationRule(GetCardLevelRelegationRuleDto dto);

    /**
     * 批量查询卡保级规则
     *
     * @param dto
     * @return
     */
    List<CardLevelRelegationRuleDto> listCardLevelRelegationRule(ListCardLevelRelegationRuleDto dto);

    /**
     * 分页查询会员卡降级规则
     *
     * @param dto
     * @return 分页结果
     */
    Pageable<CardLevelRelegationRuleDto> pageRelegationRule(PageCardLevelRelegationRuleDto dto);

    /**
     * 保级规则详情
     *
     * @param id
     * @return
     */
    CardLevelRelegationRuleDto getRelegationRule(Integer masterType, String masterCode, Long id);


    /**
     * 删除升级规则
     *
     * @param dto
     * @return
     */
    void deleteUpgradeRule(DeleteUpgradeRuleDto dto);

    /**
     * 启用停用升级规则
     *
     * @param dto
     * @return
     */
    void actionUpgradeRule(ActionUpgradeRuleDto dto);

    /**
     * 删除保级规则
     *
     * @param dto
     * @return
     */
    void deleteRelegationRule(DeleteRelegationRuleDto dto);

    /**
     * 启停保级规则
     *
     * @param dto
     * @return
     */
    void actionRelegationRule(ActionRelegationRuleDto dto);

    /**
     * 根据卡id，等级查询权益
     *
     * @param req req
     * @return 等级相关权力列表
     */
    List<CardLevelPrivilegeConfigDto> listCardLevelPrivilege(ListCardLevelPrivilegeDto req);

}
