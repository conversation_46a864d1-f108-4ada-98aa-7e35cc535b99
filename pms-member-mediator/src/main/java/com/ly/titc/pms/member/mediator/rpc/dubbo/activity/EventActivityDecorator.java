package com.ly.titc.pms.member.mediator.rpc.dubbo.activity;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityGrantInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityInfoResp;
import com.ly.titc.pms.spm.dubbo.interfaces.EventActivityDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;


@Slf4j
@Component
public class EventActivityDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private EventActivityDubboService activityDubboService;


    /**
     * 创建/更新事件触发活动
     */
    public String save(SaveEventActivityReq req) {
        Response<String> response = activityDubboService.save(req);
        return Response.getValidateData(response);
    }

    /**
     * 事件活动分页列表
     */
    public Pageable<EventActivityInfoResp> pageInfo(@Valid PageEventActivityInfoReq req) {
        Response<Pageable<EventActivityInfoResp>> response = activityDubboService.pageInfos(req);
        return Response.getValidateData(response);
    }

    /**
     * 查看事件活动详情
     */
    public EventActivityDetailResp getDetail(GetEventActivityReq req) {
        Response<EventActivityDetailResp> response = activityDubboService.getDetail(req);
        return Response.getValidateData(response);
    }

    /**
     * 启用
     */
    public void enable(UpdateActivityStateReq req) {
        req.setState(StateEnum.VALID.getState());
        Response<Void> response = activityDubboService.updateState(req);
        Response.getValidateData(response);
    }

    /**
     * 停用
     */
    public void disable(UpdateActivityStateReq req) {
        req.setState(StateEnum.NO_VALID.getState());
        Response<Void> response = activityDubboService.updateState(req);
        Response.getValidateData(response);
    }

    /**
     * 删除
     */
    public void delete(DeleteActivityReq req) {
        Response<Void> response = activityDubboService.delete(req);
        Response.getValidateData(response);
    }

    /**
     * 事件活动发放列表
     */
    public Pageable<EventActivityGrantInfoResp> pageGrantInfos(PageActivityGrantInfoReq req) {
        Response<Pageable<EventActivityGrantInfoResp>> pageableResponse = activityDubboService.pageGrantInfos(req);
        return Response.getValidateData(pageableResponse);
    }
}
