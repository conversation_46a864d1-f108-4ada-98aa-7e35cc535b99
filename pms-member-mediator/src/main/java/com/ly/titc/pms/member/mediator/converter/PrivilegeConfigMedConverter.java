package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeApplicableDataMappingDto;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 18:22
 */
@Mapper(componentModel = "spring")
public interface PrivilegeConfigMedConverter {

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    PrivilegeConfigInfo convertDtoToPo(SavePrivilegeConfigDto dto);

    @Mappings({
            @Mapping(target = "privilegeId",source = "privilegeId"),
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    PrivilegeApplicableDataMapping convertDtoToPo(SavePrivilegeApplicableDataMappingDto mapping, Long privilegeId, String operator);

    default List<PrivilegeApplicableDataMapping> convertDto(SavePrivilegeConfigDto dto, Long privilegeId) {
        List<PrivilegeApplicableDataMapping> mappingDtoList = new ArrayList<>();
        for (SavePrivilegeApplicableDataMappingDto detailReq : dto.getApplicableDataMapping()) {
            if (detailReq.getScopeType().equals(1)) {
                mappingDtoList.add(new PrivilegeApplicableDataMapping()
                        .setScopeType(detailReq.getScopeType())
                        .setClassification(dto.getClassification())
                        .setApplicationType(detailReq.getApplicationType())
                        .setValue(detailReq.getValue())
                        .setPrivilegeId(privilegeId)
                        .setScopeValue("")
                        .setCreateUser(dto.getOperator())
                        .setModifyUser(dto.getOperator()));
            } else {
                for (String scopeValue : detailReq.getScopeValues()) {
                    PrivilegeApplicableDataMapping mappingDto = new PrivilegeApplicableDataMapping()
                            .setScopeType(detailReq.getScopeType())
                            .setClassification(dto.getClassification())
                            .setApplicationType(detailReq.getApplicationType())
                            .setValue(detailReq.getValue())
                            .setPrivilegeId(privilegeId)
                            .setScopeValue(scopeValue)
                            .setCreateUser(dto.getOperator())
                            .setModifyUser(dto.getOperator());
                    mappingDtoList.add(mappingDto);
                }
            }
        }
        return mappingDtoList;
    }

}
