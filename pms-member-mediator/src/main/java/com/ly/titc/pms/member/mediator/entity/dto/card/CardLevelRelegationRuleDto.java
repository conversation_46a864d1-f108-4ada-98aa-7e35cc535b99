package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelRelegationRuleResp
 * @Date：2024-10-30 11:14
 * @Filename：MemberCardLevelRelegationRuleResp
 */
@Data
public class CardLevelRelegationRuleDto {

    /**
     * id
     */
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员保级等级
     */
    private Integer sourceLevel;

    /**
     * 保级成功执行类型：ALL-全部条件;ANY-满足任一个条件
     */
    private String relegationSuccessfulPerformType;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 保级失败降至指定等级
     */
    private Integer targetLevel;

    /**
     * 原等级名称
     */
    private String sourceLevelName;

    /**
     * 目标等级名称
     */
    private String targetLevelName;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 规则详情
     */
    private List<CardLevelRelegationRuleDetailDto> details;

}
