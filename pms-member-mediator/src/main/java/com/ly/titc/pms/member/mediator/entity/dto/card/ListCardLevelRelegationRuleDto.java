package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/27 15:40
 */
@Data
@Accessors(chain = true)
public class ListCardLevelRelegationRuleDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员卡ID
     */
    private List<Long> cardIds;

    /**
     * 状态
     */
    private Integer state;

}
