package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderRefundInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.ActivityOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.spm.dubbo.mq.message.MemberActivityDiscountGrantMsg;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_ACTIVITY_GRANT_TAG;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 17:41
 */
@Slf4j
@Component
public class OrderSceneRegisterHandler extends AbstractOrderSceneHandler<RegisterMemberDto> {
    @Resource
    private MemberMedService memberMedService;
    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;
    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;
    @Resource
    private MemberActivityDecorator memberActivityDecorator;

    @Override
    public CreateOrderDto<RegisterMemberDto> doPreCheck(CreateOrderDto<RegisterMemberDto> dto) {
        RegisterMemberDto memberDto = dto.getMemberSceneNoteDto();
        if(!Objects.isNull(dto.getActivityOrderDto())){
            ActivityOrderDto activityOrderDto = dto.getActivityOrderDto();
            IssueMemberCardDto memberCardInfo = memberDto.getMemberCardInfo();
            boolean flag = memberActivityDecorator.judgeAvailableMemberActivity(dto.getBlocCode(),activityOrderDto.getActivityCode(),
                    activityOrderDto.getGearCode(),null,memberCardInfo.getCardId(),memberCardInfo.getCardLevel(), dto.getAmount());
            if (!flag) {
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00017);
            }
        }
        memberMedService.registerCheck(memberDto);
        dto.setMobile(memberDto.getMobile());
        return dto;
    }

    @Override
    public String doGetLockKey(RegisterMemberDto dto) {
        return  CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + getScene() + dto.getMobile();
    }

    @Override
    public void saveSceneOrder(CreateOrderDto<RegisterMemberDto> dto) {

    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        //查询订单明细信息
        MemberOrderDetailInfo detailInfo = detailInfoBiz.getByOrderNo(orderInfo.getMemberOrderNo());
        RegisterMemberDto memberDto = JSON.parseObject(detailInfo.getMemberSceneNote(),RegisterMemberDto.class);
        memberDto.setSalesman(orderInfo.getCreateUser());
        RegisterMemberResultDto result = memberMedService.register(memberDto);
        // 更新会员信息
        memberOrderInfoBiz.updateMemberNoAndCardNo(orderInfo.getMemberOrderNo(), result.getMemberNo(), result.getMemberCardNo());
        // 发放礼包
        grantGiftPack(orderInfo, detailInfo, result.getMemberNo());
        // 返回对象
        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(result.getMemberNo());
        resultDto.setMemberCardNo(result.getMemberCardNo());
        resultDto.setMemberOrderNo(orderInfo.getMemberOrderNo());
        return resultDto;
    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {

    }

    public String getScene(){
        return MemberSceneEnum.REGISTER.getScene();
    }

}
