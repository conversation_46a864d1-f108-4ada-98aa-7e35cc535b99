package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PurchaseCardDto;
import com.ly.titc.pms.member.mediator.manager.OrderSceneManager;
import com.ly.titc.pms.spm.dubbo.mq.message.MemberActivityDiscountGrantMsg;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_ACTIVITY_GRANT_TAG;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 19:33
 */
@Slf4j
public abstract class AbstractOrderSceneHandler<T> {

    @Resource(type = TurboMQProducer.class)
    protected TurboMQProducer producer;

    public AbstractOrderSceneHandler(){
        OrderSceneManager.putInstance(this);
    }

    /**
     * 前置校验
     * @param dto
     */
    public abstract CreateOrderDto<T> doPreCheck(CreateOrderDto<T> dto);

    /**
     * 获取加锁key
     */
    public abstract String doGetLockKey(T dto);

    /**
     * 保存业务场景订单
     * @param dto
     */
    public abstract  void saveSceneOrder(CreateOrderDto<T> dto);

    /**
     * 后置处理
     */
    public abstract OrderPostResultDto postHandle(MemberOrderInfo orderInfo);

    /**
     * 退款业务回退
     */
    public abstract void refundHandle(MemberOrderRefundInfo orderInfo);

    /**
     * 获取场景
     * @return
     */
    public abstract String getScene();

    /**
     * 发放礼包
     *
     * @param orderInfo
     * @param detailInfo
     * @param memberNo
     */
    protected void grantGiftPack(MemberOrderInfo orderInfo, MemberOrderDetailInfo detailInfo, String memberNo) {
        try {
            MemberActivityDiscountGrantMsg msg = buildSendGIftMsg(orderInfo, detailInfo);
            msg.setMemberNo(memberNo);
            String msgJson = JSONObject.toJSONString(msg);
            producer.sendMsgWithTag(TurboMqTopic.PMS_SPM_ACTIVITY_TOPIC, MEMBER_ACTIVITY_GRANT_TAG, msgJson);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    protected static MemberActivityDiscountGrantMsg buildSendGIftMsg(MemberOrderInfo orderInfo, MemberOrderDetailInfo detailInfo) {
        MemberActivityDiscountGrantMsg msg = new MemberActivityDiscountGrantMsg();
        msg.setBlocCode(orderInfo.getBlocCode());
        msg.setHotelCode(orderInfo.getHotelCode());
        msg.setActivityCode(detailInfo.getActivityCode());
        msg.setSourceBizCode(orderInfo.getMemberOrderNo());
        msg.setActivityItemCode(detailInfo.getGearCode());
        msg.setMemberNo(orderInfo.getMemberNo());
        msg.setSourceClient(orderInfo.getPlatformChannel());
        msg.setTraceId(TraceNoUtil.getTraceNo());
        return msg;
    }
}
