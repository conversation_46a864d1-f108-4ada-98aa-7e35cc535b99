package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.*;

import java.util.List;

/**
 * 权益配置
 *
 * <AUTHOR>
 * @date 2025/6/25 17:54
 */
public interface PrivilegeConfigMedService {

    /**
     * 保存权益配置
     *
     * @param dto
     * @return
     */
    Long savePrivilegeConfig(SavePrivilegeConfigDto dto);

    /**
     * 删除权益配置
     *
     * @param dto
     */
    void deletePrivilegeConfig(DeletePrivilegeConfigDto dto);

    /**
     * 启用/禁用权益配置
     *
     * @param dto
     * @return
     */
    Long actionPrivilegeConfig(ActionPrivilegeConfigDto dto);

    /**
     * 查询权益配置
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    List<PrivilegeConfigDto> listPrivilegeConfig(Integer masterType, String masterCode);

    /**
     * 分页查询权益配置
     *
     * @param dto
     * @return
     */
    Pageable<PrivilegeConfigDto> pagePrivilegeConfig(PagePrivilegeConfigDto dto);

    /**
     * 获取权益配置
     *
     * @param masterType
     * @param masterCode
     * @param id
     * @return
     */
    PrivilegeConfigDto getPrivilegeConfig(Integer masterType, String masterCode, Long id);

    /**
     * 校验权益是否被使用
     *
     * @param masterType
     * @param masterCode
     * @param privilegeId
     * @return
     */
    boolean checkPrivilegeUsed(Integer masterType, String masterCode, Long privilegeId);
}
