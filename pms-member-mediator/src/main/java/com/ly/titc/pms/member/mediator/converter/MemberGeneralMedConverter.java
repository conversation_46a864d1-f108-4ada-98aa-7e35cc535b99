package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.mdm.entity.dto.dict.DictItemTreeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictItemDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardLevelConfigDto;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberGeneralConverter
 * @Date：2024-12-4 13:55
 * @Filename：MemberGeneralConverter
 */
@Mapper(componentModel = "spring")
public interface MemberGeneralMedConverter {

    default List<MemberGeneralCardConfigDto> convert(List<CardConfigDto> list, List<CardLevelConfigWithPrivilegeDto> memberCardLevelConfigInfoRespList) {
        Map<Long, List<CardLevelConfigWithPrivilegeDto>> map = memberCardLevelConfigInfoRespList.stream().collect(Collectors.groupingBy(CardLevelConfigWithPrivilegeDto::getCardId));
        return list.stream().map(item -> {
            MemberGeneralCardConfigDto dto = new MemberGeneralCardConfigDto();
            dto.setCardId(item.getId().toString());
            dto.setCardName(item.getCardName());
            List<CardLevelConfigWithPrivilegeDto> itemList = map.get(item.getId());
            if (CollectionUtils.isNotEmpty(itemList)) {
                dto.setMemberLevels(itemList.stream().map(item1 -> {
                    MemberGeneralCardLevelConfigDto dto1 = new MemberGeneralCardLevelConfigDto();
                    dto1.setCardLevel(item1.getCardLevel());
                    dto1.setCardLevelName(item1.getCardLevelName());
                    dto1.setCardId(item1.getCardId().toString());
                    return dto1;
                }).collect(Collectors.toList()));
            }
            return dto;
        }).collect(Collectors.toList());
    }

    default List<DictDto> convert(List<DictItemTreeDto> dictItemTreeDto) {
        List<DictDto> list = new ArrayList<>();
        dictItemTreeDto.forEach(item -> {
            DictDto dictDto = new DictDto();
            dictDto.setName(item.getName());
            dictDto.setValue(item.getValue());
            dictDto.setItems(item.getChildren().stream().map(item1 -> new DictItemDto().setName(item1.getName())
                    .setValue(item1.getValue())).collect(Collectors.toList()));
            list.add(dictDto);
        });
        return list;
    }

}
