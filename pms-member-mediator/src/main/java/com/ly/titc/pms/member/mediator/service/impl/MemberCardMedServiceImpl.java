package com.ly.titc.pms.member.mediator.service.impl;

import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.biz.MemberCardNoGenerateRecordBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.RedisLock;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberCardNoGenerateRecord;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.entity.wrapper.MemberIssueCardWrapper;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.converter.MemberMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardNoRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.entity.message.GenerateCardNoMsgExt;
import com.ly.titc.pms.member.mediator.service.*;
import com.ly.titc.pms.member.service.MemberService;
import com.ly.titc.springboot.mq.starter.core.producer.MessageHelper;
import com.ly.titc.springboot.mq.starter.core.producer.TurboMqProducerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员卡Med服务实现
 *
 * <AUTHOR>
 * @date 2024/10/29 10:00
 */
@Slf4j
@Service
public class MemberCardMedServiceImpl implements MemberCardMedService {

    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;
    @Resource
    private MemberCardMedConverter memberCardMedConverter;
    @Resource
    private MemberCardNoGenerateRecordBiz memberCardNoGenerateRecordBiz;
    @Resource
    private RedisFactory redisFactory;
    @Resource
    private CardConfigMedService cardConfigMedService;
    @Resource
    private TurboMqProducerTemplate turboMqProducerTemplate;
    @Resource
    private MessageMedService messageMedService;
    @Resource
    private MemberMedConverter memberMedConverter;
    @Resource
    private MemberMedService memberMedService;
    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;
    @Resource
    private MemberService memberService;
    /**
     * 卡号数量
     */
    public final static Integer CARD_NO_NUM = 100;

    /**
     * 阈值
     */
    public final static Integer THRESHOLD = 15;

    @Override
    public List<MemberCardInfoDto> listByMemberNo(Integer masterType, String masterCode, String memberNo) {
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        Map<String, String> levelNameMap = assembleLevelNameMap(masterType, masterCode, memberCardInfos);
        // 查询默认卡
        return memberCardInfos.stream().map(card -> assembleMemberCardInfoDto(card, levelNameMap)).collect(Collectors.toList());
    }

    @Override
    public List<MemberCardInfoDto> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos) {
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNos(masterType, masterCode, memberNos);
        Map<String, String> levelNameMap = assembleLevelNameMap(masterType, masterCode, memberCardInfos);
        return memberCardInfos.stream().map(card -> assembleMemberCardInfoDto(card, levelNameMap)).collect(Collectors.toList());
    }

    @Override
    public List<MemberCardInfoDto> listByCardNos(Integer masterType, String masterCode, List<String> cardNos) {
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByCardNos(masterType, masterCode, cardNos);
        Map<String, String> levelNameMap = assembleLevelNameMap(masterType, masterCode, memberCardInfos);
        return memberCardInfos.stream().map(card -> assembleMemberCardInfoDto(card, levelNameMap)).collect(Collectors.toList());
    }

    @Override
    public MemberCardInfoDto getByMemberCardNo(Integer masterType, String masterCode, String memberCardNo) {
        MemberCardInfo memberCardInfo = memberCardInfoBiz.getByMemberCardNo(masterType, masterCode, memberCardNo);
        if (memberCardInfo == null) {
            return null;
        }
        MemberCardInfoDto memberCardInfoDto = memberCardMedConverter.convertPoToDto(memberCardInfo);
        CardConfigDto config = cardConfigMedService.getCardConfig(masterType, masterCode, memberCardInfo.getCardId());
        Optional<String> optional = config.getCardLevelConfigs().stream().filter(e -> memberCardInfo.getCardLevel().equals(e.getCardLevel()))
                .map(CardLevelConfigWithPrivilegeDto::getCardLevelName).findFirst();
        optional.ifPresent(memberCardInfoDto::setCardLevelName);
        return memberCardInfoDto;
    }

    @Override
    public void checkUpdateCardLevel(UpdateCardLevelDto updateCardLevelDto) {
        String memberNo = updateCardLevelDto.getMemberNo();
        String memberCardNo = updateCardLevelDto.getMemberCardNo();

        MemberInfoDto memberInfo = memberMedService.getByMemberNo(memberNo);
        if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.error("会员不存在或会员已注销，memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10038);
        }
        MemberCardInfo memberCardInfo = memberCardInfoBiz.getByMemberCardNo(memberInfo.getMasterType(), memberInfo.getMasterCode(), memberCardNo);
        if (!memberCardInfo.getCardLevel().equals(updateCardLevelDto.getPreLevel())) {
            log.error("当前会员等级发生变动，请确认后重新升级，memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10042);
        }
        Long cardId = memberCardInfo.getCardId();
        // 会员卡是否存在
        CardConfigDto memberCardConfig = cardConfigMedService.getCardConfig(memberInfo.getMasterType(), memberInfo.getMasterCode(), cardId);
        if (memberCardConfig == null || memberCardConfig.getState().equals(StateEnum.NO_VALID.getState())) {
            log.error("会员卡不存在或已停用，cardId:{}", cardId);
            throw new ServiceException(RespCodeEnum.MEMBER_10051);
        }
        Optional<CardLevelConfigWithPrivilegeDto> optional = memberCardConfig.getCardLevelConfigs().stream().filter(e -> e.getCardLevel().equals(updateCardLevelDto.getAfterLevel())).findFirst();
        if (!optional.isPresent()) {
            throw new ServiceException(RespCodeEnum.MEMBER_10019);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCardLevel(UpdateCardLevelDto updateCardLevelDto) {
        // 校验
        checkUpdateCardLevel(updateCardLevelDto);
        // 会员卡等级配置
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(updateCardLevelDto.getMemberNo());
        MemberCardInfo memberCardInfo = memberCardInfoBiz.getByMemberCardNo(memberInfo.getMasterType(), memberInfo.getMasterCode(), updateCardLevelDto.getMemberCardNo());
        CardConfigDto memberCardConfigInfo = cardConfigMedService.getCardConfig(memberInfo.getMasterType(), memberInfo.getMasterCode(), memberCardInfo.getCardId());
        Map<Integer, String> cardLevelMap = memberCardConfigInfo.getCardLevelConfigs().stream().collect(Collectors.toMap(CardLevelConfigWithPrivilegeDto::getCardLevel, CardLevelConfigWithPrivilegeDto::getCardLevelName));
        updateCardLevelDto.setPreLevelName(cardLevelMap.get(updateCardLevelDto.getPreLevel())).setAfterLevelName(cardLevelMap.get(updateCardLevelDto.getAfterLevel()));
        // 组装数据
        memberCardMedConverter.convertMemberCardInfo(memberCardInfo, updateCardLevelDto);
        memberCardInfo.setLastLevelChangeDate(LocalDateTime.now());
        // 会员卡等级的有效期时间填充
        if (StringUtils.isBlank(updateCardLevelDto.getEffectBeginDate()) || StringUtils.isBlank(updateCardLevelDto.getEffectEndDate())) {
            CardLevelConfigWithPrivilegeDto cardLevelConfig = memberCardConfigInfo.getCardLevelConfigs().stream().filter(e -> e.getCardLevel().equals(memberCardInfo.getCardLevel())).findFirst().get();
            memberCardInfo.setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()));
            memberCardInfo.setEffectEndDate(LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(cardLevelConfig.getValidPeriod())));
        }
        // 会员卡信息保存
        memberCardInfoBiz.update(memberCardInfo);
        // 记录日志
        MemberCardLevelChangeRecord record = memberCardMedConverter.convertDtoToPo(updateCardLevelDto,memberInfo.getMasterType(), memberInfo.getMasterCode());
        memberCardLevelChangeRecordBiz.add(record);
        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo);
        memberEventMsg.setEventType(MemberEventEnum.MODIFY_CARD);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UpdateCardLevelResultDto> batchUpdateCardLevel(BatchUpdateCardLevelDto batchUpdateCardLevelDto) {
        Integer masterType = batchUpdateCardLevelDto.getMasterType();
        String masterCode = batchUpdateCardLevelDto.getMasterCode();
        Long cardId = batchUpdateCardLevelDto.getCardId();
        List<MemberCardLevelInfo> memberCardLevelInfos = batchUpdateCardLevelDto.getMemberCardLevelInfos();
        Integer afterLevel = batchUpdateCardLevelDto.getAfterLevel();
        String afterLevelName = batchUpdateCardLevelDto.getAfterLevelName();

        CardConfigDto memberCardConfigInfo = cardConfigMedService.getCardConfig(batchUpdateCardLevelDto.getMasterType(), batchUpdateCardLevelDto.getMasterCode(), cardId);
        if (memberCardConfigInfo == null) {
            log.error("batch update card level error, card is not exist, masterType:{}, masterCode:{}, cardId:{}", masterType, masterCode, cardId);
            throw new ServiceException(RespCodeEnum.MEMBER_10013);
        }
        // 组装数据
        List<String> memberCardNos = memberCardLevelInfos.stream().map(MemberCardLevelInfo::getMemberCardNo).collect(Collectors.toList());
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByCardNos(masterType, masterCode, memberCardNos);
        Map<String, MemberCardInfo> memberCardInfoMap = memberCardInfos.stream().collect(Collectors.toMap(MemberCardInfo::getMemberCardNo, Function.identity()));
        // 开始遍历
        List<MemberCardInfo> memberCardInfoList = new ArrayList<>();
        List<MemberCardLevelChangeRecord> changeRecordList = new ArrayList<>();
        List<UpdateCardLevelResultDto> resultList = new ArrayList<>();
        for (MemberCardLevelInfo memberCardLevelInfo : memberCardLevelInfos) {
            MemberCardInfo memberCardInfo = memberCardInfoMap.get(memberCardLevelInfo.getMemberCardNo());
            // 数据校验
            UpdateCardLevelResultDto resultDto = checkParam(memberCardLevelInfo, memberCardInfo, afterLevel);
            resultList.add(resultDto);
            if (!resultDto.isSuccess()) continue;
            // 填充信息
            memberCardMedConverter.fillMemberCard(memberCardInfo, batchUpdateCardLevelDto);
            memberCardInfoList.add(memberCardInfo);
            // 记录日志
            MemberCardLevelChangeRecord record = memberCardMedConverter.convertDtoToPo(batchUpdateCardLevelDto, memberCardLevelInfo);
            record.setAfterLevel(afterLevel).setAfterLevelName(afterLevelName);
            changeRecordList.add(record);
        }
        if (CollectionUtils.isNotEmpty(memberCardInfoList)) {
            memberCardInfoBiz.batchUpdate(masterType, masterCode, memberCardInfoList);
        }
        if (CollectionUtils.isNotEmpty(changeRecordList)) {
            memberCardLevelChangeRecordBiz.batchAdd(changeRecordList);
        }
        return resultList;
    }

    private static UpdateCardLevelResultDto checkParam(MemberCardLevelInfo memberCardLevelInfo, MemberCardInfo memberCardInfo, Integer afterLevel) {
        UpdateCardLevelResultDto result = new UpdateCardLevelResultDto();
        result.setMemberNo(memberCardLevelInfo.getMemberNo()).setMemberCardNo(memberCardLevelInfo.getMemberCardNo());
        if (memberCardInfo == null) {
            result.setSuccess(false).setReason("会员卡号不存在");
            return result;
        }
        if (memberCardInfo.getMemberNo().equals(memberCardLevelInfo.getMemberNo())) {
            result.setSuccess(false).setReason("会员未绑定该会员卡");
            return result;
        }
        if (memberCardInfo.getCardLevel().equals(afterLevel)) {
            result.setSuccess(false).setReason("当前会员已是该等级");
            return result;
        }
        result.setSuccess(true).setReason("Success");
        return result;
    }

    @Override
    public String updateCardInfo(MemberCardInfoDto memberCardInfo) {
        Integer masterType = memberCardInfo.getMasterType();
        String masterCode = memberCardInfo.getMasterCode();
        String memberNo = memberCardInfo.getMemberNo();

        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }

        MemberCardInfo entity = memberCardMedConverter.convertDtoToPo(memberCardInfo);
        memberCardInfoBiz.update(entity);
        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo);
        memberEventMsg.setEventType(MemberEventEnum.MODIFY_CARD);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
        return memberCardInfo.getMemberCardNo();
    }

    @Override
    public String generateCardNo(Integer masterType, String masterCode, Long cardId) {
        CardConfigDto memberCardConfigInfo = cardConfigMedService.getCardConfig(masterType, masterCode, cardId);
        if (memberCardConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10013);
        }
        CardNoRuleDto cardNoRule = memberCardConfigInfo.getCardNoRule();
        // step 1: 走缓存直接拿
        String generateKey = CommonUtil.concat(CommonConstant.CARD_NO_GENERATE_PREFIX, masterType, masterCode, cardId);
        String memberCardNo = redisFactory.lPop(generateKey);
        log.info("memberCardNo:{}", memberCardNo);
        if (ObjectUtils.isEmpty(memberCardNo)) {
            // 先走redis获取，未获取到，则补充，加等待锁
            String idempotentKey = CommonUtil.concat(CommonConstant.CARD_NO_GENERATE_IDEMPOTENT_PREFIX, masterType, masterCode, cardId);
            boolean lock = RedisLock.retryLock(idempotentKey);
            if (!lock) {
                throw new ServiceException(RespCodeEnum.CODE_2000);
            }
            try {
                memberCardNo = redisFactory.lPop(generateKey);
                if (ObjectUtils.isEmpty(memberCardNo)) {
                    // step 2: 拿不到走，走获取
                    memberCardNo = generateSingleCardNo(masterType, masterCode, cardId, cardNoRule);

                }
                log.info("newMemberCardNo:{}", memberCardNo);
                // 小于阈值 发送MQ补充
                if (redisFactory.llen(generateKey) < (THRESHOLD)) {
                    Message message = MessageHelper.builder().tag(TurboMqTopicTag.CARD_NO_GENERATE)
                            .msg(new GenerateCardNoMsgExt().setMasterType(masterType).setMasterCode(masterCode).setCardId(cardId))
                            .topic(TurboMqTopic.PMS_MEMBER_BPS_TOPIC).build();
                    SendResult sendResult = turboMqProducerTemplate.send(message);
                    log.info("发送预生成会员卡号的消息成功，sendResult:{}", sendResult);
                }
            } finally {
                RedisLock.unLock(idempotentKey);
            }
        }
        return memberCardNo;
    }

    @Override
    public void checkIssueCard(IssueMemberCardDto dto) {
        String memberCardNo = dto.getMemberCardNo();
        String memberNo = dto.getMemberNo();
        Long cardId = dto.getCardId();

        MemberInfoDto memberInfo = memberMedService.getByMemberNo(memberNo);
        if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.error("会员不存在或会员已注销，memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10032);
        }
        // 会员卡是否存在
        CardConfigDto memberCardConfigInfo = cardConfigMedService.getCardConfig(memberInfo.getMasterType(), memberInfo.getMasterCode(), cardId);
        if (memberCardConfigInfo == null || memberCardConfigInfo.getState().equals(StateEnum.NO_VALID.getState())) {
            log.error("会员卡不存在或已停用，cardId:{}", cardId);
            throw new ServiceException(RespCodeEnum.MEMBER_10051);
        }
        Integer cardLevel = dto.getCardLevel();
        // 会员卡等级是否存在
        Optional<CardLevelConfigWithPrivilegeDto> optional = memberCardConfigInfo.getCardLevelConfigs().stream().filter(e -> e.getCardLevel().equals(cardLevel)).findFirst();
        if (!optional.isPresent()) {
            log.error("会员卡等级不存在，cardId:{}, cardLevel:{}", cardId, cardLevel);
            throw new ServiceException(RespCodeEnum.MEMBER_10014);
        }
        // 会员是否已存在该卡
        boolean cardExistFlag = checkMemberHasCard(memberInfo.getMasterType(), memberInfo.getMasterCode(), memberNo, cardId);
        if (cardExistFlag) {
            log.error("会员卡已存在，memberNo:{}, cardId:{}", memberNo, cardId);
            throw new ServiceException(RespCodeEnum.MEMBER_10002);
        }
        // 会员卡号是否被占用
        boolean cardNoExistFlag = checkCardNoExist(memberInfo.getMasterType(), memberInfo.getMasterCode(), memberCardNo);
        if (cardNoExistFlag) {
            log.error("会员卡号已存在，memberNo:{}, cardId:{}, cardNo:{}", memberNo, cardId, memberCardNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10040);
        }
        // TODO 黑名单校验
    }

    @Override
    public String issueCard(IssueMemberCardDto memberCardInfo) {
        // 会员信息
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(memberCardInfo.getMemberNo());
        // 参数校验
        checkIssueCard(memberCardInfo);
        // 填充信息
        fillMemberCardInfo(memberInfo.getMasterType(), memberInfo.getMasterCode(), memberCardInfo);

        MemberIssueCardWrapper wrapper = new MemberIssueCardWrapper();
        wrapper.setMemberCardInfo(memberCardMedConverter.convertDtoToPo(memberCardInfo, LocalDateTime.now(), memberInfo.getMasterType(), memberInfo.getMasterCode()));
        wrapper.setMemberCardLevelChangeRecord(assembleChangeRecord(memberCardInfo, memberInfo.getMasterType(), memberInfo.getMasterCode()));
        memberService.issueCard(wrapper);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberCardInfo);
        memberEventMsg.setMasterType(memberInfo.getMasterType());
        memberEventMsg.setMasterCode(memberInfo.getMasterCode());
        memberEventMsg.setMemberNo(memberCardInfo.getMemberNo());
        memberEventMsg.setEventType(MemberEventEnum.ISSUE_CARD);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
        return memberCardInfo.getMemberCardNo();
    }

    /**
     * 刷新会员卡号缓存
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     */
    @Override
    public void refreshCardNoCache(Integer masterType, String masterCode, Long cardId) {
        CardConfigDto memberCardConfigInfo = cardConfigMedService.getCardConfig(masterType, masterCode, cardId);
        if (memberCardConfigInfo == null) {
            log.error("会员卡号不存在，masterType:{}. masterCode:{}, cardId:{}", masterType, masterCode, cardId);
            return;
        }
        CardNoRuleDto cardNoRule = memberCardConfigInfo.getCardNoRule();
        // 生成数量
        String generateKey = CommonUtil.concat(CommonConstant.CARD_NO_GENERATE_PREFIX, masterType, masterCode, cardId);
        // 是否小于阈值
        Long length = redisFactory.llen(generateKey);
        if (length < (THRESHOLD)) {
            List<String> availableNums = new ArrayList<>();
            for (int i = 0; i < (CARD_NO_NUM - length); i++) {
                String memberCardNo = generateSingleCardNo(masterType, masterCode, cardId, cardNoRule);
                if (StringUtils.isNotBlank(memberCardNo)) {
                    availableNums.add(memberCardNo);
                }
            }
            if (CollectionUtils.isNotEmpty(availableNums)) {
                redisFactory.rPush(generateKey, availableNums);
            }
        }
    }

    @Override
    public boolean checkHasCard(Integer masterType, String masterCode, Long cardId, Integer cardLevel) {
        return memberCardInfoBiz.checkHasCard(masterType, masterCode, cardId, cardLevel);
    }

    @Override
    public boolean checkCardNoExist(Integer masterType, String masterCode, String memberCardNo) {
        MemberCardInfo memberCardInfo = memberCardInfoBiz.getByMemberCardNo(masterType, masterCode, memberCardNo);
        return memberCardInfo != null;
    }

    @Override
    public boolean checkMemberHasCard(Integer masterType, String masterCode, String memberNo, Long cardId) {
        MemberCardInfo memberCardInfo = memberCardInfoBiz.getByMemberNoAndCardId(masterType, masterCode, memberNo, cardId);
        return memberCardInfo != null;
    }

    /**
     * 生成单个会员卡号
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @param cardNoRule
     * @return
     */
    private String generateSingleCardNo(Integer masterType, String masterCode, Long cardId, CardNoRuleDto cardNoRule) {
        String cardPrefix = cardNoRule.getCardPrefix();
        Integer cardLength = cardNoRule.getCardLength();
        List<String> excludeNumbers = StringUtils.isBlank(cardNoRule.getExcludeNumber()) ? Lists.newArrayList() : Arrays.asList(cardNoRule.getExcludeNumber().split(","));
        String memberCardNo = null;

        // 查询会员生成记录
        while (true) {
            MemberCardNoGenerateRecord record = memberCardNoGenerateRecordBiz.getMemberCardNoGenerateRecord(masterType, masterCode, cardId);
            long nowEndNum = record == null ? 0L : record.getEndNum();
            String nextEndNum = null;
            // 最大遍历30
            outerLoop:
            for (int index = 1; index <= 30; index++) {
                // 当前使用长度
                nextEndNum = Long.toString(nowEndNum + index);
                // 长度
                if (nextEndNum.length() > cardLength) {
                    log.error("masterType:{}, masterCode:{}, cardId:{} 当前长度已超过最大长度, 最大长度:{}", masterType, masterCode, cardId, cardLength);
                    // todo 发送企业微信通知
                    throw new ServiceException(RespCodeEnum.MEMBER_10017);
                }
                // 过滤字符
                if (CollectionUtils.isNotEmpty(excludeNumbers)) {
                    for (char number : nextEndNum.toCharArray()) {
                        if (excludeNumbers.contains(String.valueOf(number))) {
                            log.error("masterType:{}, masterCode:{}, cardId:{} 包含过滤字符, 过滤字符:{}", masterType, masterCode, cardId, number);
                            continue outerLoop;
                        }
                    }
                }
                // 查询卡号是否被占用
                memberCardNo = cardPrefix + String.format("%0" + cardLength + "d", Integer.parseInt(nextEndNum));
                MemberCardInfo existMemberCardInfo = memberCardInfoBiz.getByMemberCardNo(masterType, masterCode, memberCardNo);
                if (existMemberCardInfo != null) {
                    log.error("masterType:{}, masterCode:{}, cardId:{} 会员卡号已存在, memberCardNo:{}", masterType, masterCode, cardId, memberCardNo);
                    continue;
                }
                log.info("masterType:{}, masterCode:{}, 生成卡号:{}", masterType, masterCode, memberCardNo);
                break;
            }
            if (record == null) {
                record = new MemberCardNoGenerateRecord();
                record.setMasterType(masterType);
                record.setMasterCode(masterCode);
                record.setCardId(cardId);
                record.setEndNum(nowEndNum);
                memberCardNoGenerateRecordBiz.add(record);
                break;
            } else {
                // 使用乐观锁，如果保存成功则跳出循环
                int i = memberCardNoGenerateRecordBiz.update(masterType, masterCode, cardId, nowEndNum, Long.parseLong(nextEndNum));
                if (i > 0) {
                    break;
                }
            }
        }
        return memberCardNo;
    }

    private Map<String, String> assembleLevelNameMap(Integer masterType, String masterCode, List<MemberCardInfo> memberCardInfos) {
        Map<String, String> levelNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(memberCardInfos)) {
            List<CardConfigDto> memberCardConfigs = cardConfigMedService.listCardConfig(masterType, masterCode);
            for (CardConfigDto memberCardConfig : memberCardConfigs) {
                for (CardLevelConfigWithPrivilegeDto cardLevelConfig : memberCardConfig.getCardLevelConfigs()) {
                    String key = String.format("%s_%s", memberCardConfig.getId(), cardLevelConfig.getCardLevel());
                    levelNameMap.put(key, cardLevelConfig.getCardLevelName());
                }
            }
        }
        return levelNameMap;
    }

    private MemberCardInfoDto assembleMemberCardInfoDto(MemberCardInfo card, Map<String, String> levelNameMap) {
        MemberCardInfoDto memberCardInfoDto = memberCardMedConverter.convertPoToDto(card);
        String key = String.format("%s_%s", card.getCardId(), card.getCardLevel());
        memberCardInfoDto.setCardLevelName(levelNameMap.get(key));
        return memberCardInfoDto;
    }

    private MemberCardLevelChangeRecord assembleChangeRecord(IssueMemberCardDto memberCardInfo, Integer masterType, String masterCode) {
        MemberCardLevelChangeRecord record = memberCardMedConverter.convertDtoToPo("发放会员卡", memberCardInfo);
        record.setMasterType(masterType).setMasterCode(masterCode).setPreLevel(0).setPreLevelName("")
                .setAfterLevel(memberCardInfo.getCardLevel()).setAfterLevelName(memberCardInfo.getCardLevelName())
                .setBizType(memberCardInfo.getBizType()).setBizNo(memberCardInfo.getBizNo());
        return record;
    }

    private void fillMemberCardInfo(Integer masterType, String masterCode, IssueMemberCardDto memberCardInfo) {
        CardConfigDto memberCardConfig = cardConfigMedService.getCardConfig(masterType, masterCode, memberCardInfo.getCardId());
        memberCardInfo.setCardType(memberCardConfig.getCardType());
        CardLevelConfigWithPrivilegeDto cardLevelConfig = memberCardConfig.getCardLevelConfigs().stream().filter(e -> e.getCardLevel().equals(memberCardInfo.getCardLevel())).findFirst().get();
        memberCardInfo.setCardLevelName(cardLevelConfig.getCardLevelName());
        // 会员卡等级的有效期时间填充
        if (StringUtils.isBlank(memberCardInfo.getEffectBeginDate()) || StringUtils.isBlank(memberCardInfo.getEffectEndDate())) {
            memberCardInfo.setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()));
            memberCardInfo.setEffectEndDate(LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(cardLevelConfig.getValidPeriod())));
            memberCardInfo.setIsLongTerm(cardLevelConfig.getIsLongTerm());
        }
    }
}
