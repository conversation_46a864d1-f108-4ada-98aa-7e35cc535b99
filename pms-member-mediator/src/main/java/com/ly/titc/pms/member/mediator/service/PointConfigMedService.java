package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.point.PointConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.SavePointConfigDto;

/**
 * 积分配置
 *
 * <AUTHOR>
 * @date 2025/6/25 18:56
 */
public interface PointConfigMedService {

    /**
     * 保存积分设置
     *
     * @param dto
     */
    void savePointConfig(SavePointConfigDto dto);


    /**
     * 获取积分过期时间
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    public String getPointExpireDate(Integer masterType, String masterCode);

    /**
     * 获取积分设置
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    PointConfigDto getPointConfig(Integer masterType, String masterCode);

}
