package com.ly.titc.pms.member.mediator.entity.dto.store;

import com.ly.titc.pms.member.mediator.entity.dto.usage.CodeDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:49
 */
@Data
@Accessors(chain = true)
public class StoreUsageRuleDetailDto {

    private Long id;

    /**
     * 主体类型（创建） 1:集团 2:门店 3：艺龙
     */
    private Integer masterType;

    /**
     * 主体类型编码（创建） ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组编码（冗余存储）
     */
    private String clubCode;

    /**
     * 集团编码（冗余存储）
     */
    private String blocCode;

    /**
     * 酒店编码（冗余存储）
     */
    private Integer hotelCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 规则模式：SINGLE ：唯一  MULTI：混合
     */
    private String ruleMode;

    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    private List<CodeDto> scopePlatformChannels;

    /**
     * 配置的适用来源 PUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    private List<String> scopeSources;

    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     */
    private Integer scopeHotelRange;

    /**
     * 是否可用 1可使用 0不可使用
     */
    private Integer isCanUse;

    /**
     * 可用场景
     */
    private List<String> scenes;

    /**
     * 储值使用模式 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    private Integer usageMode;

    /**
     * 储值扣款模式 1 优先本金 2 优先礼金 3 比例扣减
     */
    private Integer deductionType;

    /**
     * 礼金扣减比例
     */
    private String deductionRatio;

//    /**
//     * 使用是否需要密码 1：需要 0 不需要
//     */
//    private Integer isUsePassword;
//
//    /**
//     * 使用其他会员储值 1 支持 0 不支持
//     */
//    private Integer isUseOtherMember;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;
    /**
     * 适用门店范围
     */
    private List<CodeDto> scopeHotelCodes;

    /**
     * 储值使用模式指定门店范围
     */
    private List<CodeDto> usageHotelCodes;
}
