package com.ly.titc.pms.member.mediator.handler.schedule;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.cc.dubbo.entity.request.log.RecordReq;
import com.ly.titc.cc.dubbo.interfaces.OperationLogDubboService;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStorePeriodResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.ActionEnum;
import com.ly.titc.pms.member.com.enums.CalculateTypeEnum;
import com.ly.titc.pms.member.com.enums.ConditionTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.com.utils.PageUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.springboot.dcdb.dal.core.conditions.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.titc.pms.member.entity.bo.PageMemberCheckInParamBo;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberScheduleMqDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ScheduleDataDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_SCHEDULE;

/**
 * @Author：rui
 * @name：AbstractScheduleHandler
 * @Date：2024-11-21 14:29
 * @Filename：AbstractScheduleHandler
 */
@Slf4j
public abstract class AbstractScheduleHandler<Data extends ScheduleDataDto> {

    @Resource
    protected MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;
    @Resource
    protected MemberCheckInRecordBiz checkInRecordBiz;
    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;
    @Resource
    private AssetDecorator assetDecorator;
    @Resource
    private MemberInfoBiz memberInfoBiz;
    @DubboReference
    private OperationLogDubboService operationLogDubboService;
    @Resource
    private CardLevelUpgradeRuleBiz cardLevelUpgradeRuleBiz;
    @Resource
    private CardLevelRelegationRuleBiz cardLevelRelegationRuleBiz;
    @Resource
    private MemberTagConfigBiz  memberTagConfigBiz;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;

    // MQ发送限流配置
    private static final int MQ_BATCH_SIZE = 100; // 每批发送100条消息
    private static final long MQ_BATCH_DELAY_MS = 200; // 批次间延迟200ms


    public void doSchedule() {
        List<String> blocCodes = ConfigCenterUtil.listBloc();
        for (String blocCode : blocCodes) {
            TwoTuple<Integer, String> masterByBloc = ConfigCenterUtil.getMasterByBloc(blocCode);
            Integer masterType = masterByBloc.getFirst();
            String masterCode = masterByBloc.getSecond();
            doScheduleMainByMqWithRateLimit(masterType, masterCode);
        }
    }

    protected List<String> getNeedHandleMemberList(Integer masterType, String masterCode) {
        return memberInfoBiz.listMembers(masterType, masterCode)
                .stream()
                .map(MemberInfo::getMemberNo)
                .distinct()
                .collect(Collectors.toList());
    }

    public void doScheduleMainByMqWithRateLimit(Integer masterType, String masterCode) {
        if (Objects.equals(ScheduleHandlerEnum.MEMBER_UPGRADE_RULE.getAction(), getAction())) {
            // 升级
            List<CardLevelUpgradeRuleInfo> upgradeRuleList = cardLevelUpgradeRuleBiz.list(masterType, masterCode);
            if (CollectionUtils.isEmpty(upgradeRuleList)) {
                log.info("无升级方案：masterCode: {}", masterCode);
                return;
            }
            // 分页查询会员数据，并推送到mq
            queryAndSendMembersByPage(masterType, masterCode, (pageIndex, pageSize) -> {
                Page<MemberInfo> page = new Page<>(pageIndex, pageSize);
                LambdaQueryWrapperX<MemberInfo> wrapper = new LambdaQueryWrapperX<>();
                wrapper.eq(MemberInfo::getMasterType, masterType)
                        .eq(MemberInfo::getMasterCode, masterCode)
                        .eq(MemberInfo::getState, MemberStateEnum.VALID.getState());
                IPage<MemberInfo> result = memberInfoBiz.page(page, wrapper);
                return result.getRecords().stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
            });

        } else if (Objects.equals(ScheduleHandlerEnum.MEMBER_RELEGATION_RULE.getAction(), getAction())){
            // 降级
            List<CardLevelRelegationRuleInfo> relationRuleList = cardLevelRelegationRuleBiz.list(masterType, masterCode);
            if (CollectionUtils.isEmpty(relationRuleList)) {
                log.info("无降级方案：masterCode: {}", masterCode);
                return;
            }
            // 查询memberCardInfo里 isLongTerm=1 或者 isLongTerm=0 && effect_end_date = 今天的会员数据 - 分批处理避免OOM
            String today = LocalDate.now().toString();
            queryAndSendMembersByPage(masterType, masterCode, (pageIndex, pageSize) -> {
                IPage<MemberCardInfo> result = memberCardInfoBiz.pageRelegationCheckCards(masterType, masterCode, pageIndex, pageSize, today);
                return result.getRecords().stream().map(MemberCardInfo::getMemberNo).distinct().collect(Collectors.toList());
            });

        } else if (Objects.equals(ScheduleHandlerEnum.MEMBER_TAG.getAction(), getAction())) {
            // 打标
            List<MemberTagConfigInfo> memberTagConfigInfos = memberTagConfigBiz.listAutoTagRule(masterType, masterCode);
            if (CollectionUtils.isEmpty(memberTagConfigInfos)) {
                log.info("无打标方案：masterCode: {}", masterCode);
                return;
            }
            // 分页查询会员数据，并推送到mq - 分批处理避免OOM
            queryAndSendMembersByPage(masterType, masterCode, (pageIndex, pageSize) -> {
                Page<MemberInfo> page = new Page<>(pageIndex, pageSize);
                LambdaQueryWrapperX<MemberInfo> wrapper = new LambdaQueryWrapperX<>();
                wrapper.eq(MemberInfo::getMasterType, masterType)
                        .eq(MemberInfo::getMasterCode, masterCode)
                        .eq(MemberInfo::getState, MemberStateEnum.VALID.getState());
                IPage<MemberInfo> result = memberInfoBiz.page(page, wrapper);
                return result.getRecords().stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
            });
        }
    }

    private void sendMemberNos(Integer masterType, String masterCode, List<String> memberNos) {
        if (CollectionUtils.isEmpty(memberNos)) {
            log.info("没有需要处理的会员, masterType: {}, masterCode: {}", masterType, masterCode);
            return;
        }
        log.info("开始处理{}个会员, masterType: {}, masterCode: {}", memberNos.size(), masterType, masterCode);
        // 分批发送，每批最多100个会员
        int totalBatches = (int) Math.ceil((double) memberNos.size() / MQ_BATCH_SIZE);
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < totalBatches; i++) {
            int start = i * MQ_BATCH_SIZE;
            int end = Math.min(start + MQ_BATCH_SIZE, memberNos.size());
            List<String> batchMemberNos = memberNos.subList(start, end);

            // 发送当前批次 - 批量发送
            MemberScheduleMqDto msg = new MemberScheduleMqDto();
            msg.setMemberNo(batchMemberNos); // 批量发送会员号列表
            msg.setMasterCode(masterCode);
            msg.setMasterType(masterType);
            msg.setAction(getAction());
            String str = JSONObject.toJSONString(msg);
            try {
                producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
                successCount += batchMemberNos.size();
                log.info("批量发送会员定时任务成功, 批次:{}/{}, 会员数:{}", i + 1, totalBatches, batchMemberNos.size());
            } catch (Exception e) {
                failCount += batchMemberNos.size();
                log.error("批量发送会员定时任务失败, topic:{}, tag:{}, 批次:{}/{}, 会员数:{}, msg:{}",
                        TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, i + 1, totalBatches, batchMemberNos.size(), str, e);
            }

            // 批次间延迟，防止MQ积压
            if (i < totalBatches - 1) { // 最后一批不需要延迟
                try {
                    Thread.sleep(MQ_BATCH_DELAY_MS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("批次延迟被中断", e);
                    break;
                }
            }
        }
        log.info("masterType:{}, masterCode:{} 批量处理完成，总会员数:{}, 成功:{}，失败:{}",
                masterType, masterCode, memberNos.size(), successCount, failCount);
    }

    /**
     * 分页查询并发送会员数据，避免OOM
     *
     * @param masterType
     * @param masterCode
     * @param queryFunction 分页查询函数，返回当前页的会员号列表
     */
    private void queryAndSendMembersByPage(Integer masterType, String masterCode, PageQueryFunction queryFunction) {
        int pageIndex = 1;
        int pageSize = 1000; // 每页查询1000条记录
        int totalProcessed = 0;
        log.info("开始分页查询并发送会员数据, masterType: {}, masterCode: {}", masterType, masterCode);
        while (true) {
            try {
                List<String> memberNos = queryFunction.queryPage(pageIndex, pageSize);
                if (CollectionUtils.isEmpty(memberNos)) {
                    log.info("第{}页查询结果为空，分页查询结束", pageIndex);
                    break;
                }
                log.info("第{}页查询到{}个会员，开始发送MQ", pageIndex, memberNos.size());
                sendMemberNos(masterType, masterCode, memberNos);
                totalProcessed += memberNos.size();
                // 如果当前页数据少于pageSize，说明已经是最后一页
                if (memberNos.size() < pageSize) {
                    log.info("第{}页数据量({})小于页大小({})，分页查询结束", pageIndex, memberNos.size(), pageSize);
                    break;
                }
                pageIndex++;
                // 页间延迟，避免对数据库造成过大压力
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("分页查询延迟被中断", e);
                    break;
                }
            } catch (Exception e) {
                log.error("分页查询第{}页失败, masterType: {}, masterCode: {}", pageIndex, masterType, masterCode, e);
                break;
            }
        }
        log.info("分页查询并发送完成, masterType: {}, masterCode: {}, 总处理会员数: {}", masterType, masterCode, totalProcessed);
    }

    /**
     * 分页查询函数式接口
     */
    @FunctionalInterface
    private interface PageQueryFunction {
        /**
         * 分页查询
         *
         * @param pageIndex 页码，从1开始
         * @param pageSize  页大小
         * @return 当前页的会员号列表
         */
        List<String> queryPage(int pageIndex, int pageSize);
    }

    public abstract void process(Integer masterType, String masterCode, String memberNo);

    /**
     * 获取类型
     *
     * @return
     */
    public abstract Integer getAction();

    public AbstractScheduleHandler() {
        ScheduleHandlerFactory.putHandler(this.getAction(), this);
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员积分记录
     */
    protected List<BaseCheckDto> getMemberPointRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        // 将LocalDateTime转换为String格式，因为AssetDecorator的方法需要String参数
        String startDate = start.toLocalDate().toString();
        String endDate = end.toLocalDate().toString();
        // 调用资产服务获取会员积分统计数据
        MemberPointPeriodResp pointPeriodResp = assetDecorator.getTotalAccountPointsPeriodList(memberNoList, startDate, endDate);
        // 如果返回结果为空或没有数据，返回空列表
        if (pointPeriodResp == null || pointPeriodResp.getMemberPointPeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheckDto列表
        return pointPeriodResp.getMemberPointPeriodList().stream().map(item -> {
            BaseCheckDto dto = new BaseCheckDto();
            dto.setMemberNo(item.getMemberNo());
            // 设置总积分余额，如果为null则默认为0
            dto.setTotalScoreBalance(item.getTotalScore() != null ? item.getTotalScore() : 0);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员消费金额记录
     */
    protected List<BaseCheckDto> getMemberConsumptionRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        List<BaseCheckDto> resultList = new ArrayList<>();
        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }
        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString();
        String endDate = end.toLocalDate().toString();
        // 为每个会员查询入住记录并计算消费金额
        for (String memberNo : memberNoList) {
            try {
                // 使用分页查询方法获取该会员在指定时间范围内的入住记录
                PageMemberCheckInParamBo paramBo = new PageMemberCheckInParamBo();
                paramBo.setMemberNo(memberNo)
                        .setCheckInBeginTime(startDate)
                        .setCheckInEndTime(endDate);
                // 查询所有符合条件的入住记录
                List<MemberCheckInRecord> checkInRecords = PageUtil.queryAll((int pageIndex, int pageSize) ->
                        checkInRecordBiz.pageCheckInRecord(paramBo.setPageIndex(pageIndex).setPageSize(pageSize)), null);
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                // 计算入住消费金额：房费 + 其他费用
                BigDecimal checkInExpense = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(checkInRecords)) {
                    checkInExpense = checkInRecords.stream()
                            .map(record -> {
                                BigDecimal roomRate = record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO;
                                BigDecimal otherRate = record.getOtherRate() != null ? record.getOtherRate() : BigDecimal.ZERO;
                                return roomRate.add(otherRate);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                dto.setExpenseAmount(checkInExpense);
                resultList.add(dto);
            } catch (Exception e) {
                // 记录异常但继续处理其他会员
                log.warn("查询会员消费记录失败, memberNo: {}, error: {}", memberNo, e.getMessage());
            }
        }
        return resultList;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员充值金额
     */
    protected List<BaseCheckDto> getMemberRechargeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";
        MemberStorePeriodResp resp = assetDecorator.getMemeberStorePeriodList(memberNoList, startDate, endDate);
        if (resp == null || resp.getMemberStorePeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheckDto列表
        return resp.getMemberStorePeriodList().stream().map(item -> {
            BaseCheckDto dto = new BaseCheckDto();
            dto.setMemberNo(item.getMemberNo());
            dto.setTotalCapitalAmount(item.getTotalAmountResp() != null ? item.getTotalAmountResp().getTotalCapitalAmount() : BigDecimal.ZERO);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 批量处理会员记录的工具方法
     *
     * @param memberNoList 会员编号列表
     * @param start        开始时间
     * @param end          结束时间
     * @param processor    处理函数，接收会员编号列表和时间范围，返回BaseCheckDto列表
     * @param batchSize    批处理大小，默认50
     * @return 处理结果列表
     */
    private List<BaseCheckDto> processMemberRecordsBatch(List<String> memberNoList, LocalDateTime start, LocalDateTime end,
                                                         BatchProcessor processor, int batchSize) {
        List<BaseCheckDto> resultList = new ArrayList<>();

        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }

        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";

        // 分批处理会员列表
        for (int i = 0; i < memberNoList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, memberNoList.size());
            List<String> batchMemberNos = memberNoList.subList(i, endIndex);
            try {
                List<BaseCheckDto> batchResults = processor.process(batchMemberNos, startDate, endDate);
                resultList.addAll(batchResults);
            } catch (Exception e) {
                log.warn("批量处理会员记录失败, memberNos: {}, error: {}", batchMemberNos, e.getMessage());
                // 为失败的批次创建默认记录
                for (String memberNo : batchMemberNos) {
                    BaseCheckDto dto = new BaseCheckDto();
                    dto.setMemberNo(memberNo);
                    resultList.add(dto);
                }
            }
        }
        return resultList;
    }

    /**
     * 批量处理会员记录的工具方法（使用默认批处理大小50）
     */
    private List<BaseCheckDto> processMemberRecordsBatch(List<String> memberNoList, LocalDateTime start, LocalDateTime end,
                                                         BatchProcessor processor) {
        return processMemberRecordsBatch(memberNoList, start, end, processor, 50);
    }

    /**
     * 批处理函数式接口
     */
    @FunctionalInterface
    private interface BatchProcessor {
        List<BaseCheckDto> process(List<String> memberNos, String startDate, String endDate) throws Exception;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住次数
     */
    protected List<BaseCheckDto> getMemberCheckoutRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);
            // 按会员编号分组统计入住次数
            Map<String, Long> memberCheckInCountMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo, Collectors.counting()));
            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                dto.setCheckInCount(memberCheckInCountMap.getOrDefault(memberNo, 0L).intValue());
                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住房晚
     */
    protected List<BaseCheckDto> getMemberStayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);

            // 按会员编号分组统计房晚数
            Map<String, Integer> memberRoomNightsMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo,
                            Collectors.summingInt(MemberCheckInRecord::getRoomNights)));

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                dto.setRoomNights(memberRoomNightsMap.getOrDefault(memberNo, 0));
                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员未入住天数
     */
    protected List<BaseCheckDto> getMemberUnstayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询会员最近一次入住记录
            List<MemberCheckInRecord> lastRecords = checkInRecordBiz.listLastByMemberNos(memberNos);
            // 按会员编号建立映射
            Map<String, MemberCheckInRecord> memberLastRecordMap = lastRecords.stream()
                    .collect(Collectors.toMap(MemberCheckInRecord::getMemberNo, record -> record));

            LocalDate currentDate = LocalDate.now();
            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                MemberCheckInRecord lastRecord = memberLastRecordMap.get(memberNo);
                if (lastRecord != null && lastRecord.getCheckOutDate() != null) {
                    try {
                        // 解析最后一次离店日期
                        LocalDate lastCheckOutDate = LocalDate.parse(lastRecord.getCheckOutDate());
                        // 计算未入住天数（当前日期 - 最后离店日期）
                        long unstayDays = ChronoUnit.DAYS.between(lastCheckOutDate, currentDate);
                        // 确保未入住天数不为负数
                        dto.setUnstayDays((int) Math.max(0, unstayDays));
                    } catch (Exception e) {
                        log.warn("解析离店日期失败, memberNo: {}, checkOutDate: {}", memberNo, lastRecord.getCheckOutDate());
                        dto.setUnstayDays(0);
                    }
                } else {
                    // 如果没有入住记录，设置为一个较大的值
                    dto.setUnstayDays(Integer.MAX_VALUE);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取平均房费
     */
    protected List<BaseCheckDto> getMemberAvgRoomFeeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);

            // 按会员编号分组计算平均房费
            Map<String, List<MemberCheckInRecord>> memberRecordsMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo));

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);

                List<MemberCheckInRecord> memberRecords = memberRecordsMap.get(memberNo);
                if (memberRecords != null && !memberRecords.isEmpty()) {
                    // 计算总房费和总房晚数
                    BigDecimal totalRoomFee = memberRecords.stream()
                            .map(record -> record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    int totalRoomNights = memberRecords.stream()
                            .mapToInt(MemberCheckInRecord::getRoomNights)
                            .sum();

                    // 计算平均房费：总房费 / 总房晚数
                    if (totalRoomNights > 0) {
                        BigDecimal avgRoomFee = totalRoomFee.divide(BigDecimal.valueOf(totalRoomNights), 2, BigDecimal.ROUND_HALF_UP);
                        dto.setAvgRoomFee(avgRoomFee);
                    } else {
                        dto.setAvgRoomFee(BigDecimal.ZERO);
                    }
                } else {
                    // 如果没有入住记录，平均房费为0
                    dto.setAvgRoomFee(BigDecimal.ZERO);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取注册天数
     */
    protected List<BaseCheckDto> getMemberRegisterDaysRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询会员信息
            List<MemberInfo> memberInfos = memberInfoBiz.listByMemberNos(masterType, masterCode, memberNos);

            // 按会员编号建立映射
            Map<String, MemberInfo> memberInfoMap = memberInfos.stream()
                    .collect(Collectors.toMap(MemberInfo::getMemberNo, info -> info));

            LocalDate currentDate = LocalDate.now();

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);

                MemberInfo memberInfo = memberInfoMap.get(memberNo);
                if (memberInfo != null && memberInfo.getGmtCreate() != null) {
                    // 计算注册天数（当前日期 - 注册日期）
                    LocalDate registerDate = memberInfo.getGmtCreate().toLocalDate();
                    long registerDays = ChronoUnit.DAYS.between(registerDate, currentDate);
                    dto.setRegisterDays((int) Math.max(0, registerDays));
                } else {
                    dto.setRegisterDays(0);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    protected Boolean calculateItem(Integer calculateType, String conditionValue, String factValue) {
        try {
            // 将字符串转换为 BigDecimal 进行比较
            BigDecimal conditionVal = new BigDecimal(conditionValue);
            BigDecimal factVal = new BigDecimal(factValue);
            switch (CalculateTypeEnum.getByType(calculateType)) {
                case GT_EQ:
                    return factVal.compareTo(conditionVal) >= 0;
                case GT:
                    return factVal.compareTo(conditionVal) > 0;
                case LT_EQ:
                    return factVal.compareTo(conditionVal) <= 0;
                case LT:
                    return factVal.compareTo(conditionVal) < 0;
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("计算失败", e);
            return false;
        }
    }

    /**
     * 通用条件检查接口
     */
    public interface ConditionDetail {
        Integer getConditionType();

        Integer getCalculateType();

        String getConditionValue();
    }

    /**
     * 检查积分条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkPointConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> pointRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(pointRecords)) {
            int totalScoreBalanceTotal = pointRecords.stream()
                    .mapToInt(e -> e.getTotalScoreBalance())
                    .sum();
            actualValue = String.valueOf(totalScoreBalanceTotal);
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("积分条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查消费金额条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkConsumptionConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> consumptionRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(consumptionRecords)) {
            BigDecimal expenseAmount = consumptionRecords
                    .stream()
                    .map(e -> e.getExpenseAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualValue = expenseAmount.toString();
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("消费条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查充值金额条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkRechargeConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> rechargeRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(rechargeRecords)) {
            BigDecimal rechargeAmount = rechargeRecords
                    .stream()
                    .map(e -> e.getTotalCapitalAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualValue = rechargeAmount.toString();
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("充值条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查入住次数条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkCheckoutConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> checkoutRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(checkoutRecords)) {
            int checkInCountTotal = checkoutRecords.stream()
                    .mapToInt(e -> e.getCheckInCount())
                    .sum();
            actualValue = String.valueOf(checkInCountTotal);
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("入住次数条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查入住房晚条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkStayConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> stayRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            int roomNightsTotal = stayRecords.stream()
                    .mapToInt(e -> e.getRoomNights())
                    .sum();
            actualValue = String.valueOf(roomNightsTotal);
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("入住房晚条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查未入住天数条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkUnstayConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> unstayRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(unstayRecords)) {
            int unstayDaysTotal = unstayRecords.stream()
                    .mapToInt(e -> e.getUnstayDays())
                    .sum();
            actualValue = String.valueOf(unstayDaysTotal);
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("未入住天数条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查注册天数条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkRegisterConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> registerRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(registerRecords)) {
            int registerDaysTotal = registerRecords.stream()
                    .mapToInt(e -> e.getRegisterDays())
                    .sum();
            actualValue = String.valueOf(registerDaysTotal);
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("注册天数条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 检查平均房费条件（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkAverageRoomFeeConditionWithDetail(ConditionDetail detail, String member, List<BaseCheckDto> avgRoomFeeRecords) {
        String actualValue = "0";
        if (!CollectionUtils.isEmpty(avgRoomFeeRecords)) {
            BigDecimal avgRoomFeeTotal = avgRoomFeeRecords
                    .stream()
                    .map(e -> e.getAvgRoomFee())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualValue = avgRoomFeeTotal.toString();
        }

        boolean satisfied = calculateItem(detail.getCalculateType(), detail.getConditionValue(), actualValue);
        log.info("平均房费条件检查结果: {}, member: {}, actualValue: {}", satisfied, member, actualValue);

        return new ConditionCheckResult.ConditionDetail()
                .setConditionType(detail.getConditionType())
                .setConditionTypeName(ConditionTypeEnum.getNameByType(detail.getConditionType()))
                .setCalculateType(detail.getCalculateType())
                .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                .setConditionValue(detail.getConditionValue())
                .setActualValue(actualValue)
                .setSatisfied(satisfied);
    }

    /**
     * 通用条件检查方法（带详细结果）
     */
    protected ConditionCheckResult.ConditionDetail checkConditionWithDetail(ConditionDetail detail, String memberNo,
            List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords,
            List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords,
            List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {

        switch (ConditionTypeEnum.getByType(detail.getConditionType())) {
            case POINT:
                return checkPointConditionWithDetail(detail, memberNo, pointRecords);
            case CONSUME_AMOUNT:
                return checkConsumptionConditionWithDetail(detail, memberNo, consumptionRecords);
            case RECHARGE_AMOUNT:
                return checkRechargeConditionWithDetail(detail, memberNo, rechargeRecords);
            case IN_COUNT:
                return checkCheckoutConditionWithDetail(detail, memberNo, checkoutRecords);
            case IN_NIGHT:
                return checkStayConditionWithDetail(detail, memberNo, stayRecords);
            case UNCHECKED_DAYS:
                return checkUnstayConditionWithDetail(detail, memberNo, unstayRecords);
            case AVERAGE_ROOM_FEE:
                return checkAverageRoomFeeConditionWithDetail(detail, memberNo, averageRecords);
            case REGISTER_DAYS:
                return checkRegisterConditionWithDetail(detail, memberNo, registerRecords);
            default:
                return new ConditionCheckResult.ConditionDetail()
                        .setConditionType(detail.getConditionType())
                        .setConditionTypeName("未知条件")
                        .setCalculateType(detail.getCalculateType())
                        .setCalculateTypeName(CalculateTypeEnum.getNameByType(detail.getCalculateType()))
                        .setConditionValue(detail.getConditionValue())
                        .setActualValue("0")
                        .setSatisfied(false);
        }
    }

    /**
     * 检查所有条件（带详细结果）
     */
    protected ConditionCheckResult checkAllConditionsWithDetail(List<? extends ConditionDetail> details, String memberNo,
            List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords,
            List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords,
            List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {

        ConditionCheckResult result = new ConditionCheckResult();
        boolean allPassed = true;

        for (ConditionDetail detail : details) {
            ConditionCheckResult.ConditionDetail conditionResult = checkConditionWithDetail(
                detail, memberNo, pointRecords, consumptionRecords, rechargeRecords,
                checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords);

            if (conditionResult.isSatisfied()) {
                result.getSatisfiedConditions().add(conditionResult);
            } else {
                result.getUnsatisfiedConditions().add(conditionResult);
                allPassed = false;
            }
        }

        result.setPassed(allPassed);
        return result;
    }

    /**
     * 检查任意条件（带详细结果）
     */
    protected ConditionCheckResult checkAnyConditionWithDetail(List<? extends ConditionDetail> details, String memberNo,
            List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords,
            List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords,
            List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {

        ConditionCheckResult result = new ConditionCheckResult();
        boolean anyPassed = false;

        for (ConditionDetail detail : details) {
            ConditionCheckResult.ConditionDetail conditionResult = checkConditionWithDetail(
                detail, memberNo, pointRecords, consumptionRecords, rechargeRecords,
                checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords);

            if (conditionResult.isSatisfied()) {
                result.getSatisfiedConditions().add(conditionResult);
                anyPassed = true;
            } else {
                result.getUnsatisfiedConditions().add(conditionResult);
            }
        }

        result.setPassed(anyPassed);
        return result;
    }

    /**
     * 会员记录数据缓存类
     */
    public static class MemberRecordsCache {
        private final Map<String, List<BaseCheckDto>> cache = new HashMap<>();

        public List<BaseCheckDto> get(String key) {
            return cache.get(key);
        }

        public void put(String key, List<BaseCheckDto> records) {
            cache.put(key, records);
        }

        public boolean containsKey(String key) {
            return cache.containsKey(key);
        }

        private String generateKey(String type, LocalDateTime start, LocalDateTime end) {
            return String.format("%s_%s_%s", type,
                start != null ? start.toString() : "null",
                end != null ? end.toString() : "null");
        }

        public String getPointRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("POINT", start, end);
        }

        public String getConsumptionRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("CONSUMPTION", start, end);
        }

        public String getRechargeRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("RECHARGE", start, end);
        }

        public String getCheckoutRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("CHECKOUT", start, end);
        }

        public String getStayRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("STAY", start, end);
        }

        public String getUnstayRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("UNSTAY", start, end);
        }

        public String getAvgRoomFeeRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("AVG_ROOM_FEE", start, end);
        }

        public String getRegisterDaysRecordsKey(LocalDateTime start, LocalDateTime end) {
            return generateKey("REGISTER_DAYS", start, end);
        }
    }

    /**
     * 批量获取会员的所有记录数据（带缓存优化）
     *
     * @param masterType 主体类型
     * @param masterCode 主体编码
     * @param memberNo 会员编号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param cache 缓存对象
     * @return 包含所有记录类型的Map
     */
    protected Map<String, List<BaseCheckDto>> getAllMemberRecords(Integer masterType, String masterCode,
            String memberNo, LocalDateTime startDate, LocalDateTime endDate, MemberRecordsCache cache) {

        Map<String, List<BaseCheckDto>> result = new HashMap<>();
        List<String> memberNoList = Arrays.asList(memberNo);

        // 积分记录
        String pointKey = cache.getPointRecordsKey(startDate, endDate);
        if (cache.containsKey(pointKey)) {
            result.put("pointRecords", cache.get(pointKey));
        } else {
            List<BaseCheckDto> pointRecords = getMemberPointRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(pointKey, pointRecords);
            result.put("pointRecords", pointRecords);
        }
        // 消费记录
        String consumptionKey = cache.getConsumptionRecordsKey(startDate, endDate);
        if (cache.containsKey(consumptionKey)) {
            result.put("consumptionRecords", cache.get(consumptionKey));
        } else {
            List<BaseCheckDto> consumptionRecords = getMemberConsumptionRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(consumptionKey, consumptionRecords);
            result.put("consumptionRecords", consumptionRecords);
        }
        // 充值记录
        String rechargeKey = cache.getRechargeRecordsKey(startDate, endDate);
        if (cache.containsKey(rechargeKey)) {
            result.put("rechargeRecords", cache.get(rechargeKey));
        } else {
            List<BaseCheckDto> rechargeRecords = getMemberRechargeRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(rechargeKey, rechargeRecords);
            result.put("rechargeRecords", rechargeRecords);
        }
        // 入住次数记录
        String checkoutKey = cache.getCheckoutRecordsKey(startDate, endDate);
        if (cache.containsKey(checkoutKey)) {
            result.put("checkoutRecords", cache.get(checkoutKey));
        } else {
            List<BaseCheckDto> checkoutRecords = getMemberCheckoutRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(checkoutKey, checkoutRecords);
            result.put("checkoutRecords", checkoutRecords);
        }
        // 房晚数记录
        String stayKey = cache.getStayRecordsKey(startDate, endDate);
        if (cache.containsKey(stayKey)) {
            result.put("stayRecords", cache.get(stayKey));
        } else {
            List<BaseCheckDto> stayRecords = getMemberStayRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(stayKey, stayRecords);
            result.put("stayRecords", stayRecords);
        }
        // 未入住天数记录
        String unstayKey = cache.getUnstayRecordsKey(startDate, endDate);
        if (cache.containsKey(unstayKey)) {
            result.put("unstayRecords", cache.get(unstayKey));
        } else {
            List<BaseCheckDto> unstayRecords = getMemberUnstayRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(unstayKey, unstayRecords);
            result.put("unstayRecords", unstayRecords);
        }
        // 平均房费记录
        String avgRoomFeeKey = cache.getAvgRoomFeeRecordsKey(startDate, endDate);
        if (cache.containsKey(avgRoomFeeKey)) {
            result.put("avgRoomFeeRecords", cache.get(avgRoomFeeKey));
        } else {
            List<BaseCheckDto> avgRoomFeeRecords = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(avgRoomFeeKey, avgRoomFeeRecords);
            result.put("avgRoomFeeRecords", avgRoomFeeRecords);
        }

        // 注册天数记录
        String registerDaysKey = cache.getRegisterDaysRecordsKey(startDate, endDate);
        if (cache.containsKey(registerDaysKey)) {
            result.put("registerDaysRecords", cache.get(registerDaysKey));
        } else {
            List<BaseCheckDto> registerDaysRecords = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, memberNoList, startDate, endDate);
            cache.put(registerDaysKey, registerDaysRecords);
            result.put("registerDaysRecords", registerDaysRecords);
        }

        return result;
    }

    public void addLogRecord(ActionEnum actionEnum, String blocCode, String memberNo, String opContent, String operator) {
        RecordReq recordReq = new RecordReq();
        recordReq.setTenant(CommonConstant.PROJECT_CODE);
        // 操作模块
        recordReq.setBizCode(CommonConstant.MEMBER_MANAGE_CODE);
        recordReq.setBizName(CommonConstant.MEMBER_MANAGE_NAME);
        // 行为
        recordReq.setCategory(actionEnum.getCode().toString());
        recordReq.setCategoryName(actionEnum.getDesc());
        // 操作对象
        Map<String, Object> tagNamemap = new HashMap<>();
        tagNamemap.put("name", memberNo);
        tagNamemap.put("blocCode", blocCode);
        recordReq.setTags(tagNamemap);
        // 操作时间
        recordReq.setTimestamp(System.currentTimeMillis());
        // 操作内容
        recordReq.setOpContent(opContent);
        recordReq.setTrackingId(UUID.randomUUID().toString());
        // 操作用户
        recordReq.setOperator(operator);
        recordReq.setState("SUCCESS");
        Response<String> resp = operationLogDubboService.record(recordReq);
        log.info("记录日志，resp: {}", resp);
    }
}
