package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.chm.entity.response.BlocChannelResp;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.account.dubbo.entity.request.expense.ListExpenseReq;
import com.ly.titc.pms.account.dubbo.entity.response.ExpenseInfoResp;
import com.ly.titc.pms.member.com.enums.PointsIssueNodeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.mediator.converter.MemberGeneralMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictItemDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberSourceDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.chm.ChmChannelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.account.AccountExpenseDecorator;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberGeneralMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberGeneralMedService
 * @Date：2024-12-4 11:24
 * @Filename：MemberGeneralMedService
 */
@Slf4j
@Service
public class MemberGeneralMedServiceImpl implements MemberGeneralMedService {

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private MemberGeneralMedConverter converter;

    @Resource
    private ChmChannelDecorator chmChannelDecorator;

    @Resource
    private AccountExpenseDecorator accountExpenseDecorator;

    private static final String dict_code = "ApplicableChannel";


    @Override
    public List<MemberGeneralCardConfigDto> queryMemberIdentity(Integer masterType, String masterCode, String name) {
        List<CardConfigDto> list = cardConfigMedService.listCardConfig(masterType, masterCode);
        if (CollectionUtils.isNotEmpty(list)) {
            List<CardConfigDto> firstFilter = new ArrayList<>();
            if (StringUtils.isNotEmpty(name)) {
                firstFilter  = list.stream().filter(item -> item.getCardName().contains(name)).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(firstFilter)) {
                ListCardLevelConfigDto dto = new ListCardLevelConfigDto().setMasterType(masterType).setMasterCode(masterCode)
                        .setCardIds(list.stream().map(CardConfigDto::getId).collect(Collectors.toList())).setState(StateEnum.VALID.getState());
                List<CardLevelConfigWithPrivilegeDto> cardLevelConfigs = cardConfigMedService.listCardLevelConfig(dto);
                return converter.convert(firstFilter, cardLevelConfigs);
            } else {
                ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto();
                levelConfigDto.setMasterType(masterType);
                levelConfigDto.setMasterCode(masterCode);
                levelConfigDto.setName(name);

                List<CardLevelConfigWithPrivilegeDto> memberCardLevelConfigInfoRespList = cardConfigMedService.listCardLevelConfig(levelConfigDto);
                if (CollectionUtils.isNotEmpty(memberCardLevelConfigInfoRespList)) {
                    List<Long> cardIdList = memberCardLevelConfigInfoRespList.stream().map(CardLevelConfigWithPrivilegeDto::getCardId).collect(Collectors.toList());
                    list = list.stream().filter(item -> cardIdList.contains(item.getId())).collect(Collectors.toList());
                    return converter.convert(list, memberCardLevelConfigInfoRespList);
                }
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<MemberSourceDto> queryMemberSource(Integer masterType, String masterCode, String name) {

        List<MemberSourceDto> list = new ArrayList<>();
        for (PlatformChannelEnum value : PlatformChannelEnum.values()) {
            if (StringUtils.isNotEmpty(name)) {
                if (!value.getPlatformChannelDesc().contains(name)) {
                    continue;
                }
            }
            list.add(new MemberSourceDto().setSource(value.getPlatformChannel()).setDesc(value.getPlatformChannelDesc()));
        }
        return list;
    }

    @Override
    public List<DictDto> queryApplicableChannel(String trackingId) {

       Map<String, List<PlatformChannelEnum>> map =  Arrays.stream(PlatformChannelEnum.values()).collect(Collectors.groupingBy(PlatformChannelEnum::getPlatform));
        List<DictDto> resp = new ArrayList<>();
        map.forEach((k, v) -> {
            DictDto dictDto = new DictDto().setValue(k).setName(v.get(0).getPlatformDesc());
            dictDto.setItems(v.stream().map(item -> {
                return new DictItemDto().setValue(item.getPlatformChannel()).setName(item.getPlatformChannelDesc());
            }).collect(Collectors.toList()));
            resp.add(dictDto);
        });
        return resp;
    }

    @Override
    public List<DictDto> queryApplicableOrderChannel(String blocCode) {

        List<BlocChannelResp> channelRespList = chmChannelDecorator.listBlocChannel(blocCode);
        List<DictDto> resp = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelRespList)) {
            Map<String,List<BlocChannelResp>> blocChannelMap = channelRespList.stream().collect(Collectors.groupingBy(BlocChannelResp::getChannelTypeCode));
            blocChannelMap.forEach((k, v) -> {
                DictDto dictDto = new DictDto().setValue(k).setName(v.get(0).getChannelTypeName());
                dictDto.setItems(v.stream().map(item -> new DictItemDto().setValue(item.getChannelCode()).setName(item.getChannelName())).collect(Collectors.toList()));
                resp.add(dictDto);
            });
        }

        return resp;
    }

    @Override
    public List<DictDto> queryApplicableExpense(String blocCode) {
        ListExpenseReq listExpenseReq = new ListExpenseReq();
        listExpenseReq.setMasterType(MasterTypeEnum.BLOC.getType());
        listExpenseReq.setMasterCode(blocCode);
        listExpenseReq.setTrackingId(TraceNoUtil.getTraceNo());
        List<ExpenseInfoResp> expenseInfos = accountExpenseDecorator.listExpense(listExpenseReq);
        List<DictDto> dictList = new ArrayList<>();
        expenseInfos.stream().collect(Collectors.groupingBy(ExpenseInfoResp::getCategoryCode)).forEach((categoryCode, expenses) -> {
            ExpenseInfoResp expenseInfoResp = expenses.get(0);
            DictDto dictDto = new DictDto().setValue(expenseInfoResp.getCategoryCode()).setName(expenseInfoResp.getCategoryName());
            dictDto.setItems(expenses.stream().map(e -> new DictItemDto().setValue(e.getItemCode()).setName(e.getItemName())).collect(Collectors.toList()));
            dictList.add(dictDto);
        });
        return dictList;
    }

    @Override
    public List<DictDto> queryPointsIssueNode(String blocCode) {
        return Arrays.stream(PointsIssueNodeEnum.values()).map(e -> new DictDto().setValue(e.getNode()).setName(e.getDesc())).collect(Collectors.toList());
    }
}
