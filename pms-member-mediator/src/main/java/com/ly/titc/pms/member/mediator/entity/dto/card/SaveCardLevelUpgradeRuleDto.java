package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 保存卡升级规则
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveCardLevelUpgradeRuleDto {

    /**
     * 规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 升级类型 1 手动 2 自动
     */
    private Integer upgradeType;

    /**
     * 升级成功执行类型 ALL-全部  ANY-满足任何一个条件
     */
    private String upgradeSuccessfulPerformType;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 规则明细
     */
    private List<SaveCardLevelUpgradeRuleDetailDto> details;
}
