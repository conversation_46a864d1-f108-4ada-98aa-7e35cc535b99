package com.ly.titc.pms.member.mediator.entity.dto.member;

import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelPrivilegeConfigDto;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.CouponStatisticsResp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-2-20 20:31
 */
@Data
@Accessors(chain = true)
public class MemberDefaultCardFullInfoDto extends MemberIdentityBaseInfoDto{

    /**
     * 会员权益信息
     */
    private List<CardLevelPrivilegeConfigDto> privileges;

    /**
     * 默认卡对应的权益信息(订单下单快照权益)
     */
    private List<CardLevelPrivilegeConfigDto> orderPrivileges;


    /**
     * 会员资产信息
     */
    private MemberStoreAccountResp storeAccount;

    /**
     * 会员积分信息
     */
    private MemberPointAccountResp pointAccount;

    /**
     * 会员优惠券信息
     */
    private CouponStatisticsResp couponStatisticsResp;
}
