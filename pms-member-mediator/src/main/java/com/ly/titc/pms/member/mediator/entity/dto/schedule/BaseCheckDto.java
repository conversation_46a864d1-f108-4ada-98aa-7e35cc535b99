package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author：rui
 * @name：BaseCheckDto
 * @Date：2024-11-21 21:12
 * @Filename：BaseCheckDto
 */
@Data
public class BaseCheckDto {

    private String memberNo;

    /**
     * 总积分余额
     */
    private Integer totalScoreBalance = 0;

    /**
     * 储值本金
     */
    private BigDecimal totalCapitalAmount;

    /**
     * 消费金额（房费+其他费用）
     */
    private BigDecimal expenseAmount;

    /**
     * 入住次数
     */
    private Integer checkInCount;

    /**
     * 入住房晚数
     */
    private Integer roomNights;

    /**
     * 未入住天数
     */
    private Integer unstayDays;

    /**
     * 平均房费
     */
    private BigDecimal avgRoomFee;

    /**
     * 注册天数
     */
    private Integer registerDays;
}
