package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.MemberRelatedConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberTagConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberTagMarkRuleInfo;
import com.ly.titc.pms.member.entity.bo.ListMemberTagConfigBo;
import com.ly.titc.pms.member.entity.bo.PageMemberTagConfigBo;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 17:12
 */
@Mapper(componentModel = "spring")
public interface MemberConfigMedConverter {
    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    MemberRelatedConfigInfo convertDtoToPo(SaveMemberRelatedConfigDto dto);

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    MemberTagConfigInfo convertDtoToPo(SaveMemberTagConfigDto dto);

    @Mappings({
            @Mapping(target = "id",source = "id"),
            @Mapping(target = "tagId",source = "tagId"),
    })
    MemberTagMarkRuleInfo convertDtoToPo(SaveMemberTagMarkRuleDto rule, Long tagId, Long id);

    PageMemberTagConfigBo convertDtoToBo(PageMemberTagConfigDto dto);

    MemberTagConfigDto convertPoToDto(MemberTagConfigInfo memberTagConfigInfo);

    ListMemberTagConfigBo convertDtoToBo(ListMemberTagConfigDto dto);

    MemberTagConfigDetailDto convertTagConfigResp(MemberTagConfigInfo info);

    MemberTagMarkRuleDto convertTagMarkRuleInfoRespList(MemberTagMarkRuleInfo dto);

    List<MemberTagMarkRuleDto> convertTagMarkRuleInfoRespList(List<MemberTagMarkRuleInfo> dto);

    default MemberTagConfigDetailDto convertTagConfigResp(MemberTagConfigInfo info, List<MemberTagMarkRuleInfo> markRuleInfoDtoList, Integer memberCount) {
        MemberTagConfigDetailDto resp = convertTagConfigResp(info);
        resp.setMarkRules(convertTagMarkRuleInfoRespList(markRuleInfoDtoList));
        resp.setMemberCount(memberCount);
        return resp;
    }

    MemberRelatedConfigDto convertPoToDto(MemberRelatedConfigInfo memberRelatedConfig);
}
