package com.ly.titc.pms.member.mediator.entity.dto.card;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：rui
 * @name：ListByCardIdAndLevelReq
 * @Date：2024-11-7 19:56
 * @Filename：ListByCardIdAndLevelReq
 */
@Data
public class ListCardLevelPrivilegeDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    private Long cardId;

    private List<Long> cardIdList;

    private Integer cardLevel;

    private Integer state;

    private String scopeValue;
}
