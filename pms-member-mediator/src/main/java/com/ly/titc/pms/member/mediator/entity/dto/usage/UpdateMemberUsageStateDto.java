package com.ly.titc.pms.member.mediator.entity.dto.usage;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.mediator.entity.dto.BasePageDto;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class UpdateMemberUsageStateDto extends BasePageDto {

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    /**
     * 状态 0 无效 1 有效
     */
    @NotNull(message = "状态不能为空")
    @LegalEnum(target = StateEnum.class,methodName = "getState")
    private Integer state;



}
