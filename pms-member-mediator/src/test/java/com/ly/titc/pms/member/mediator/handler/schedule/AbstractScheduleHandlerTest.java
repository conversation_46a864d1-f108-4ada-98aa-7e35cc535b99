package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractScheduleHandler测试类
 * 主要测试会员记录查询缓存功能
 */
public class AbstractScheduleHandlerTest {

    @Mock
    private AbstractScheduleHandler handler;

    private AbstractScheduleHandler.MemberRecordsCache cache;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        cache = new AbstractScheduleHandler.MemberRecordsCache();
        
        // 创建一个具体的handler实例用于测试
        handler = new AbstractScheduleHandler() {
            @Override
            protected List<BaseCheckDto> getMemberPointRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNoList.get(0));
                dto.setTotalScoreBalance(100);
                return Arrays.asList(dto);
            }

            @Override
            protected List<BaseCheckDto> getMemberConsumptionRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }

            @Override
            protected List<BaseCheckDto> getMemberRechargeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }

            @Override
            protected List<BaseCheckDto> getMemberCheckoutRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }

            @Override
            protected List<BaseCheckDto> getMemberStayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }

            @Override
            protected List<BaseCheckDto> getMemberUnstayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }

            @Override
            protected List<BaseCheckDto> getMemberAvgRoomFeeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }

            @Override
            protected List<BaseCheckDto> getMemberRegisterDaysRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
                return Arrays.asList(new BaseCheckDto());
            }
        };
    }

    @Test
    void testMemberRecordsCacheKeyGeneration() {
        LocalDateTime start = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime end = LocalDateTime.of(2024, 12, 31, 23, 59);

        // 测试各种记录类型的key生成
        String pointKey = cache.getPointRecordsKey(start, end);
        String consumptionKey = cache.getConsumptionRecordsKey(start, end);
        String rechargeKey = cache.getRechargeRecordsKey(start, end);

        assertNotNull(pointKey);
        assertNotNull(consumptionKey);
        assertNotNull(rechargeKey);
        
        // 确保不同类型的key不相同
        assertNotEquals(pointKey, consumptionKey);
        assertNotEquals(pointKey, rechargeKey);
        assertNotEquals(consumptionKey, rechargeKey);
    }

    @Test
    void testMemberRecordsCacheFunctionality() {
        String testKey = "test_key";
        List<BaseCheckDto> testData = Arrays.asList(new BaseCheckDto());

        // 测试缓存为空时
        assertFalse(cache.containsKey(testKey));
        assertNull(cache.get(testKey));

        // 测试添加到缓存
        cache.put(testKey, testData);
        assertTrue(cache.containsKey(testKey));
        assertEquals(testData, cache.get(testKey));
    }

    @Test
    void testGetAllMemberRecords() {
        Integer masterType = 1;
        String masterCode = "TEST";
        String memberNo = "M001";
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

        // 测试获取所有会员记录
        Map<String, List<BaseCheckDto>> result = handler.getAllMemberRecords(
                masterType, masterCode, memberNo, startDate, endDate, cache);

        // 验证返回的记录包含所有类型
        assertNotNull(result);
        assertTrue(result.containsKey("pointRecords"));
        assertTrue(result.containsKey("consumptionRecords"));
        assertTrue(result.containsKey("rechargeRecords"));
        assertTrue(result.containsKey("checkoutRecords"));
        assertTrue(result.containsKey("stayRecords"));
        assertTrue(result.containsKey("unstayRecords"));
        assertTrue(result.containsKey("avgRoomFeeRecords"));
        assertTrue(result.containsKey("registerDaysRecords"));

        // 验证每种记录都不为空
        assertNotNull(result.get("pointRecords"));
        assertNotNull(result.get("consumptionRecords"));
        assertNotNull(result.get("rechargeRecords"));
        assertNotNull(result.get("checkoutRecords"));
        assertNotNull(result.get("stayRecords"));
        assertNotNull(result.get("unstayRecords"));
        assertNotNull(result.get("avgRoomFeeRecords"));
        assertNotNull(result.get("registerDaysRecords"));
    }

    @Test
    void testGetAllMemberRecordsWithCache() {
        Integer masterType = 1;
        String masterCode = "TEST";
        String memberNo = "M001";
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

        // 第一次调用
        Map<String, List<BaseCheckDto>> result1 = handler.getAllMemberRecords(
                masterType, masterCode, memberNo, startDate, endDate, cache);

        // 第二次调用，应该使用缓存
        Map<String, List<BaseCheckDto>> result2 = handler.getAllMemberRecords(
                masterType, masterCode, memberNo, startDate, endDate, cache);

        // 验证两次调用返回相同的结果
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1.size(), result2.size());
    }
}
