package com.ly.titc.pms.member.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.dao.MemberCardLevelChangeRecordDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @title: MemberCardLevelChangeLogBiz
 * @projectName pms-member
 * @description: 会员等级变更
 * @date 2023/10/11 11:48
 */
@Slf4j
@Component
public class MemberCardLevelChangeRecordBiz extends AbstractBiz<MemberCardLevelChangeRecord> {

    @Resource(type = MemberCardLevelChangeRecordDao.class)
    protected MemberCardLevelChangeRecordDao memberCardLevelChangeRecordDao;

    /**
     * 批量记录
     *
     * @param memberLevelChangeLogs
     */
    public void batchAdd(List<MemberCardLevelChangeRecord> memberLevelChangeLogs) {
        if (!CollectionUtils.isEmpty(memberLevelChangeLogs)) {
            memberCardLevelChangeRecordDao.insertBatch(memberLevelChangeLogs);
        }
    }

    /**
     * 记录会员卡等级变更日志
     *
     * @param memberLevelChangeLog
     */
    public void add(MemberCardLevelChangeRecord memberLevelChangeLog) {
        memberLevelChangeLog.setRecordNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        memberCardLevelChangeRecordDao.insert(memberLevelChangeLog);
    }

    public void deleteByMemberNo(String memberNo, String operator) {
        LambdaUpdateWrapper<MemberCardLevelChangeRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardLevelChangeRecord::getMemberNo, memberNo)
                .set(MemberCardLevelChangeRecord::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberCardLevelChangeRecord::getModifyUser, operator);
        memberCardLevelChangeRecordDao.update(null, wrapper);
    }

    public List<MemberCardLevelChangeRecord> getLevelChangeRecordByMaster(Integer masterType, String masterCode) {
        return memberCardLevelChangeRecordDao.getLatestLevelChangeRecordsByMemberAndType(masterType, masterCode,
                Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(),
                ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.DOWN_AUTO.getType()));
    }

    public Page<MemberCardLevelChangeRecord> pageCardLevelChangeRecord(Integer pageIndex, Integer pageSize, String memberNo, String beginTime, String endTime) {
        Page<MemberCardLevelChangeRecord> page = new Page<>(pageIndex, pageSize);
        LambdaQueryWrapper<MemberCardLevelChangeRecord> wrapper = Wrappers.lambdaQuery(MemberCardLevelChangeRecord.class)
                .eq(StringUtils.isNotEmpty(memberNo), MemberCardLevelChangeRecord::getMemberNo, memberNo)
                .ne(MemberCardLevelChangeRecord::getChangeType, ChangeTypeEnum.REGISTER.getType());
        if (StringUtils.isNotEmpty(beginTime) && StringUtils.isNotEmpty(endTime)) {
            wrapper.between(MemberCardLevelChangeRecord::getGmtCreate, beginTime + " 00:00:00", endTime + " 23:59:59");
        }
        wrapper.orderByDesc(MemberCardLevelChangeRecord::getGmtCreate);
        return memberCardLevelChangeRecordDao.selectPage(page, wrapper);
    }

    public MemberCardLevelChangeRecord getMemberLastedRecordByType(String memberNo, List<Integer> changeType) {
        return memberCardLevelChangeRecordDao.selectOne(Wrappers.lambdaQuery(MemberCardLevelChangeRecord.class)
                .eq(MemberCardLevelChangeRecord::getMemberNo, memberNo)
                .in(MemberCardLevelChangeRecord::getChangeType, changeType)
                .orderByDesc(MemberCardLevelChangeRecord::getGmtCreate)
                .last("limit 1"));
    }

    /**
     * 根据业务ID查询会员卡等级变更记录
     *
     * @param bizNo 业务ID
     * @return 会员卡等级变更记录
     */
    public MemberCardLevelChangeRecord getByBizNo(String bizNo) {
        return memberCardLevelChangeRecordDao.selectOne(Wrappers.lambdaQuery(MemberCardLevelChangeRecord.class)
                .eq(MemberCardLevelChangeRecord::getBizNo, bizNo)
                .orderByDesc(MemberCardLevelChangeRecord::getGmtCreate)
                .last("limit 1"));
    }

    /**
     * 根据业务ID和业务类型查询会员卡等级变更记录
     *
     * @param bizNo   业务ID
     * @param bizType 业务类型
     * @return 会员卡等级变更记录
     */
    public MemberCardLevelChangeRecord getByBizNoAndType(Integer masterType, String masterCode, String bizNo, String bizType) {
        return memberCardLevelChangeRecordDao.selectOne(Wrappers.lambdaQuery(MemberCardLevelChangeRecord.class)
                .eq(MemberCardLevelChangeRecord::getMasterType, masterType)
                .eq(MemberCardLevelChangeRecord::getMasterCode, masterCode)
                .eq(MemberCardLevelChangeRecord::getBizNo, bizNo)
                .eq(StringUtils.isNotEmpty(bizType), MemberCardLevelChangeRecord::getBizType, bizType)
                .orderByDesc(MemberCardLevelChangeRecord::getGmtCreate)
                .last("limit 1"));
    }


}
