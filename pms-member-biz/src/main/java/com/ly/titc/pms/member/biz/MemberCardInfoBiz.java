package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.dal.dao.MemberCardInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @title: MemberCardInfoBiz
 * @projectName pms-member
 * @description: 会员卡biz
 * @date 2023/11/17 15:13
 */
@Component
public class MemberCardInfoBiz extends AbstractBiz<MemberCardInfo> {

    @Resource(type = MemberCardInfoDao.class)
    private MemberCardInfoDao memberCardInfoDao;

    /**
     * 根据会员编号查询会员卡
     *
     * @param memberNo
     * @return
     */
    public List<MemberCardInfo> listByMemberNo(Integer masterType, String masterCode, String memberNo) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .eq(MemberCardInfo::getMemberNo, memberNo);
        return memberCardInfoDao.selectList(wrapper);
    }

    /**
     * 根据会员编号查询会员卡
     *
     * @param memberNos
     * @return
     */
    public List<MemberCardInfo> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .in(MemberCardInfo::getMemberNo, memberNos);
        return memberCardInfoDao.selectList(wrapper);
    }

    /**
     * 根据会员卡号查询会员卡信息
     *
     * @param masterType
     * @param masterCode
     * @param memberCardNo
     * @return
     */
    public MemberCardInfo getByMemberCardNo(Integer masterType, String masterCode, String memberCardNo) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .eq(MemberCardInfo::getMemberCardNo, memberCardNo);
        return memberCardInfoDao.selectOne(wrapper);
    }

    /**
     * 根据会员卡号查询
     *
     * @param masterType
     * @param masterCode
     * @param cardNos
     * @return
     */
    public List<MemberCardInfo> listByCardNos(Integer masterType, String masterCode, List<String> cardNos) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .in(MemberCardInfo::getMemberCardNo, cardNos);
        return memberCardInfoDao.selectList(wrapper);
    }

    /**
     * 新增会员卡
     *
     * @param memberCardInfo
     */
    public void add(MemberCardInfo memberCardInfo) {
        memberCardInfoDao.insert(memberCardInfo);
    }

    /**
     * 会员卡信息更新
     *
     * @param memberCardInfo
     */
    public void update(MemberCardInfo memberCardInfo) {
        LambdaUpdateWrapper<MemberCardInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardInfo::getMemberNo, memberCardInfo.getMemberNo())
                .eq(MemberCardInfo::getMemberCardNo, memberCardInfo.getMemberCardNo());
        wrapper.set(MemberCardInfo::getCardLevel, memberCardInfo.getCardLevel())
                .set(MemberCardInfo::getPhysicalCardNo, memberCardInfo.getPhysicalCardNo())
                .set(MemberCardInfo::getEffectBeginDate, memberCardInfo.getEffectBeginDate())
                .set(MemberCardInfo::getEffectEndDate, memberCardInfo.getEffectEndDate())
                .set(MemberCardInfo::getIsLongTerm, memberCardInfo.getIsLongTerm())
                .set(MemberCardInfo::getState, memberCardInfo.getState())
                .set(MemberCardInfo::getModifyUser, memberCardInfo.getModifyUser());
        memberCardInfoDao.update(null, wrapper);
    }

    /**
     * 删除会员下所有会员卡
     *
     * @param memberNo
     * @param operator
     */
    public void deleteByMemberNo(String memberNo, String operator) {
        LambdaUpdateWrapper<MemberCardInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardInfo::getMemberNo, memberNo);
        wrapper.set(MemberCardInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberCardInfo::getModifyUser, operator);
        memberCardInfoDao.update(null, wrapper);
    }

    /**
     * 删除会员某个卡
     *
     * @param memberNo
     * @param operator
     */
    public void deleteByMemberCard(String memberNo, String memberCardNo, String operator) {
        LambdaUpdateWrapper<MemberCardInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardInfo::getMemberNo, memberNo)
                .eq(MemberCardInfo::getMemberCardNo, memberCardNo);
        wrapper.set(MemberCardInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberCardInfo::getModifyUser, operator);
        memberCardInfoDao.update(null, wrapper);
    }

    /**
     * 校验会员是否拥有会员卡
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @return
     */
    public boolean checkHasCard(Integer masterType, String masterCode, Long cardId, Integer cardLevel) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .eq(MemberCardInfo::getCardId, cardId)
                .eq(cardLevel != null, MemberCardInfo::getCardLevel, cardLevel)
                .last(" limit 1 ");
        return memberCardInfoDao.selectCount(wrapper) > 0;
    }

    /**
     * 批量保存
     *
     * @param memberCardInfoList
     */
    public void batchUpdate(Integer masterType, String masterCode, List<MemberCardInfo> memberCardInfoList) {
        memberCardInfoDao.updateBatch(masterType,masterCode, memberCardInfoList);
    }

    public MemberCardInfo getByMemberNoAndCardId(Integer masterType, String masterCode, String memberNo, Long cardId) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .eq(MemberCardInfo::getMemberNo, memberNo)
                .eq(MemberCardInfo::getCardId, cardId);
        return memberCardInfoDao.selectOne(wrapper);
    }

    public MemberCardInfo getByPhysicalCardNo(Integer masterType, String masterCode, String physicalCardNo) {
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .eq(MemberCardInfo::getPhysicalCardNo, physicalCardNo);
        return memberCardInfoDao.selectOne(wrapper);
    }

    /**
     * 分页查询需要降级检查的会员卡信息
     * 查询条件：isLongTerm=1 或者 isLongTerm=0 && effect_end_date = 今天
     *
     * @param masterType
     * @param masterCode
     * @param pageIndex
     * @param pageSize
     * @param today 今天的日期字符串，格式：yyyy-MM-dd
     * @return
     */
    public IPage<MemberCardInfo> pageRelegationCheckCards(Integer masterType, String masterCode, Integer pageIndex, Integer pageSize, String today) {
        Page<MemberCardInfo> page = new Page<>(pageIndex, pageSize);
        LambdaQueryWrapperX<MemberCardInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberCardInfo::getMasterType, masterType)
                .eq(MemberCardInfo::getMasterCode, masterCode)
                .eq(MemberCardInfo::getState, 1) // 有效状态
                .eq(MemberCardInfo::getIsLongTerm, 0)
                .eq(MemberCardInfo::getEffectEndDate, today);
//                .and(w -> w.eq(MemberCardInfo::getIsLongTerm, 1)
//                        .or(subW -> subW.eq(MemberCardInfo::getIsLongTerm, 0)
//                                .eq(MemberCardInfo::getEffectEndDate, today)));
        return memberCardInfoDao.selectPage(page, wrapper);
    }
}
