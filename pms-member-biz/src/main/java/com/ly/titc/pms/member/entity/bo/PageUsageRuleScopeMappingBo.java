package com.ly.titc.pms.member.entity.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询使用规则
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class PageUsageRuleScopeMappingBo implements Serializable {
    /**
     * 归属主体
     */
    private Integer masterType;

    /**
     * 归属主体code
     */
    private String masterCode;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 适用来源
     */
    private List<String> scopeSources;
    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

    /**
     * 适用酒店
     */
    private  List<String> scopeHotelCodes;

    private List<String> scopeSourceCodes;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则IDs
     */
    private List<Long> ruleIds;

    /**
     * 集团code
     */
    private String blocCode;

    private Integer pageIndex = 1;

    private Integer pageSize = 20;

}
