package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.pms.member.dal.dao.AssetUsageRuleScopeMappingDao;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资产使用规则范围
 *
 * <AUTHOR>
 * @date 2025/6/25 11:44
 */
@Component
public class AssetUsageRuleScopeMappingBiz {

    @Resource
    private AssetUsageRuleScopeMappingDao assetUsageRuleScopeMappingDao;

    public List<AssetUsageRuleScopeMapping> listByScope(String scopeSource, String scopeSourceCode, String assetType, String platformChannel) {
        LambdaQueryWrapper<AssetUsageRuleScopeMapping> wrapper = Wrappers.lambdaQuery(AssetUsageRuleScopeMapping.class)
                .eq(AssetUsageRuleScopeMapping::getScopeSource, scopeSource)
                .eq(AssetUsageRuleScopeMapping::getScopeSourceCode, scopeSourceCode)
                .eq(AssetUsageRuleScopeMapping::getAssetType, assetType)
                .eq(AssetUsageRuleScopeMapping::getScopePlatformChannel, platformChannel);
        return assetUsageRuleScopeMappingDao.selectList(wrapper);
    }

    public List<AssetUsageRuleScopeMapping> listByScopeSource(String assetType, PageUsageRuleScopeMappingBo pageBo) {
        LambdaQueryWrapper<AssetUsageRuleScopeMapping> wrapper = Wrappers.lambdaQuery(AssetUsageRuleScopeMapping.class)
                .eq(AssetUsageRuleScopeMapping::getMasterType, pageBo.getMasterType())
                .eq(AssetUsageRuleScopeMapping::getMasterCode, pageBo.getMasterCode())
                .eq(AssetUsageRuleScopeMapping::getAssetType, assetType)
                .in(CollectionUtils.isNotEmpty(pageBo.getScopeSources()), AssetUsageRuleScopeMapping::getScopeSource, pageBo.getScopeSources())
                .in(CollectionUtils.isNotEmpty(pageBo.getScopePlatformChannels()), AssetUsageRuleScopeMapping::getScopePlatformChannel, pageBo.getScopePlatformChannels())
                .in(CollectionUtils.isNotEmpty(pageBo.getScopeHotelCodes()), AssetUsageRuleScopeMapping::getHotelCode, pageBo.getScopeHotelCodes())
                .eq(pageBo.getMasterType() != null, AssetUsageRuleScopeMapping::getMasterType, pageBo.getMasterType())
                .eq(StringUtils.isNotEmpty(pageBo.getMasterCode()), AssetUsageRuleScopeMapping::getMasterCode, pageBo.getMasterCode());
        return assetUsageRuleScopeMappingDao.selectList(wrapper);
    }

    public List<AssetUsageRuleScopeMapping> listByScopeSourceCode(String assetType, PageUsageRuleScopeMappingBo pageBo) {
        LambdaQueryWrapper<AssetUsageRuleScopeMapping> wrapper = Wrappers.lambdaQuery(AssetUsageRuleScopeMapping.class)
                .eq(AssetUsageRuleScopeMapping::getAssetType,assetType)
                .in(AssetUsageRuleScopeMapping::getScopeSource,pageBo.getScopeSources())
                .in(CollectionUtils.isNotEmpty(pageBo.getScopePlatformChannels()),AssetUsageRuleScopeMapping::getScopePlatformChannel,pageBo.getScopePlatformChannels())
                .in(CollectionUtils.isNotEmpty(pageBo.getScopeHotelCodes()),AssetUsageRuleScopeMapping::getScopeSourceCode,pageBo.getScopeHotelCodes())
                .eq(pageBo.getMasterType() !=null,AssetUsageRuleScopeMapping::getMasterType,pageBo.getMasterType())
                .eq(StringUtils.isNotEmpty(pageBo.getMasterCode()),AssetUsageRuleScopeMapping::getMasterCode,pageBo.getMasterCode());
        return assetUsageRuleScopeMappingDao.selectList(wrapper);
    }

    public void deleteByRuleId(Long ruleId) {
        LambdaUpdateWrapper<AssetUsageRuleScopeMapping> wrapper = Wrappers.lambdaUpdate(AssetUsageRuleScopeMapping.class)
                .set(AssetUsageRuleScopeMapping::getIsDelete, StatusEnum.VALID.getStatus())
                .eq(AssetUsageRuleScopeMapping::getRuleId, ruleId);
        assetUsageRuleScopeMappingDao.update(null, wrapper);
    }

    public void insertBatch(List<AssetUsageRuleScopeMapping> scopeMappings) {
        assetUsageRuleScopeMappingDao.insertBatch(scopeMappings);
    }
}
