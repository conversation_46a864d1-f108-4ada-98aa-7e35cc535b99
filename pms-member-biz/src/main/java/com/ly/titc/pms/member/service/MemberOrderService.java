package com.ly.titc.pms.member.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderPayInfoBiz;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.entity.wrapper.MemberOrderPayWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberOrderWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:16
 */
@Slf4j
@Service
public class MemberOrderService {
    @Resource
    private MemberOrderInfoBiz orderInfoBiz;
    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;
    @Resource
    private MemberOrderPayInfoBiz payInfoBiz;


    @Transactional(rollbackFor = Exception.class)
    public void save(MemberOrderWrapper wrapper){

        orderInfoBiz.add(wrapper.getOrderInfo());
        detailInfoBiz.add(wrapper.getDetailInfo());

    }

    public MemberOrderInfo getByOrderNo(String memberOrderNo){
      return orderInfoBiz.getByOrderNo(memberOrderNo);
    }

    public MemberOrderDetailInfo getDetailByOrderNo(String memberOrderNo){
        return detailInfoBiz.getByOrderNo(memberOrderNo);
    }


    public List<MemberOrderPayInfo> listPayByOrderNo(String memberOrderNo){
        return payInfoBiz.listByOrderNo(memberOrderNo);
    }



    public void savePay(MemberOrderPayInfo info){
        payInfoBiz.add(info);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updatePayState(MemberOrderPayWrapper wrapper){
        MemberOrderPayInfo payInfo = wrapper.getPayInfo();
        MemberOrderPayInfo existPayInfo =  payInfoBiz.getByPayNo(payInfo.getMemberOrderNo(),payInfo.getMemberOrderPayNo());
        //不是支付中的不处理
        if(!existPayInfo.getOrderPayState().equals(1)){
            log.info("本单已是终态，不处理 payNo:{}",payInfo.getMemberOrderPayNo());
            return;
        }
        payInfoBiz.updateTradeState(payInfo);
        orderInfoBiz.updateState(wrapper.getOrderInfo());

    }

}
