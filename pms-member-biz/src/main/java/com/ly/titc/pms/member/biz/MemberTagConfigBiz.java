package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.MemberTagConfigInfoDao;
import com.ly.titc.pms.member.dal.dao.MemberTagMarkRuleInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberTagConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberTagMarkRuleInfo;
import com.ly.titc.pms.member.entity.bo.ListMemberTagConfigBo;
import com.ly.titc.pms.member.entity.bo.PageMemberTagConfigBo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员标签配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:51
 */
@Component
public class MemberTagConfigBiz {

    @Resource
    private MemberTagConfigInfoDao memberTagConfigInfoDao;

    @Resource
    private MemberTagMarkRuleInfoDao memberTagMarkRuleInfoDao;

    public MemberTagConfigInfo getByName(Integer masterType, String masterCode, String name) {
        LambdaQueryWrapper<MemberTagConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberTagConfigInfo::getMasterType, masterType)
                .eq(MemberTagConfigInfo::getMasterCode, masterCode)
                .eq(MemberTagConfigInfo::getName, name);
        return memberTagConfigInfoDao.selectOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(MemberTagConfigInfo memberRelatedConfigInfo,  List<MemberTagMarkRuleInfo> markRules) {
        memberTagConfigInfoDao.insert(memberRelatedConfigInfo);
        memberTagMarkRuleInfoDao.insertBatch(markRules);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(MemberTagConfigInfo memberRelatedConfigInfo, List<MemberTagMarkRuleInfo> markRules) {
        LambdaUpdateWrapper<MemberTagConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberTagConfigInfo::getId, memberRelatedConfigInfo.getId())
                .set(MemberTagConfigInfo::getName, memberRelatedConfigInfo.getName())
                .set(MemberTagConfigInfo::getType, memberRelatedConfigInfo.getType())
                .set(MemberTagConfigInfo::getAutoDelete, memberRelatedConfigInfo.getAutoDelete())
                .set(MemberTagConfigInfo::getMarkType, memberRelatedConfigInfo.getMarkType())
                .set(MemberTagConfigInfo::getSatisfyPerformType, memberRelatedConfigInfo.getSatisfyPerformType())
                .set(MemberTagConfigInfo::getRemark, memberRelatedConfigInfo.getRemark())
                .set(MemberTagConfigInfo::getCycleType, memberRelatedConfigInfo.getCycleType())
                .set(MemberTagConfigInfo::getState, memberRelatedConfigInfo.getState())
                .set(MemberTagConfigInfo::getSort, memberRelatedConfigInfo.getSort())
                .set(MemberTagConfigInfo::getModifyUser, memberRelatedConfigInfo.getModifyUser());
        memberTagConfigInfoDao.update(null, wrapper);
        memberTagMarkRuleInfoDao.delete(new LambdaQueryWrapper<MemberTagMarkRuleInfo>().eq(MemberTagMarkRuleInfo::getTagId, memberRelatedConfigInfo.getId()));
        memberTagMarkRuleInfoDao.insertBatch(markRules);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByTagId(Long tagId, String operator) {
        LambdaUpdateWrapper<MemberTagConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberTagConfigInfo::getId, tagId)
                .set(MemberTagConfigInfo::getIsDelete, Constant.ONE)
                .set(MemberTagConfigInfo::getModifyUser, operator);
        memberTagConfigInfoDao.update(null, wrapper);
        memberTagMarkRuleInfoDao.delete(new LambdaQueryWrapper<MemberTagMarkRuleInfo>().eq(MemberTagMarkRuleInfo::getTagId, tagId));
    }

    public IPage<MemberTagConfigInfo> pageMemberTagConfig(PageMemberTagConfigBo pageBo) {
        QueryWrapper<MemberTagConfigInfo> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(pageBo.getMasterType() != null, MemberTagConfigInfo::getMasterType, pageBo.getMasterType())
                .eq(StringUtils.isNotEmpty(pageBo.getMasterCode()), MemberTagConfigInfo::getMasterCode, pageBo.getMasterCode())
                .eq(pageBo.getType() != null, MemberTagConfigInfo::getType, pageBo.getType())
                .like(StringUtils.isNotEmpty(pageBo.getName()), MemberTagConfigInfo::getName, pageBo.getName())
                .eq(pageBo.getAutoDelete() != null, MemberTagConfigInfo::getAutoDelete, pageBo.getAutoDelete())
                .in(CollectionUtils.isNotEmpty(pageBo.getTagIdList()), MemberTagConfigInfo::getId, pageBo.getTagIdList())
                .in(CollectionUtils.isNotEmpty(pageBo.getTypeList()), MemberTagConfigInfo::getType, pageBo.getTypeList());
        return memberTagConfigInfoDao.selectPage(new Page<>(pageBo.getPageIndex(), pageBo.getPageSize()), wrapper);
    }

    public List<MemberTagConfigInfo> listTagConfig(ListMemberTagConfigBo bo) {
        return memberTagConfigInfoDao.selectList(new QueryWrapper<MemberTagConfigInfo>().lambda()
                .eq(bo.getMasterType() != null, MemberTagConfigInfo::getMasterType, bo.getMasterType())
                .eq(StringUtils.isNotEmpty(bo.getMasterCode()), MemberTagConfigInfo::getMasterCode, bo.getMasterCode())
                .eq(bo.getType() != null, MemberTagConfigInfo::getType, bo.getType())
                .like(StringUtils.isNotEmpty(bo.getName()), MemberTagConfigInfo::getName, bo.getName())
                .eq(bo.getAutoDelete() != null, MemberTagConfigInfo::getAutoDelete, bo.getAutoDelete())
                .in(CollectionUtils.isNotEmpty(bo.getTagIdList()), MemberTagConfigInfo::getId, bo.getTagIdList())
                .in(CollectionUtils.isNotEmpty(bo.getTypeList()), MemberTagConfigInfo::getType, bo.getTypeList()));
    }

    public MemberTagConfigInfo getById(Integer masterType, String masterCode, Long id) {
        return memberTagConfigInfoDao.selectOne(new QueryWrapper<MemberTagConfigInfo>().lambda()
                .eq(MemberTagConfigInfo::getMasterType, masterType)
                .eq(MemberTagConfigInfo::getMasterCode, masterCode)
                .eq(MemberTagConfigInfo::getId, id));
    }

    public List<MemberTagMarkRuleInfo> listMarkRuleByTagId(Long tagId) {
        return memberTagMarkRuleInfoDao.selectList(new QueryWrapper<MemberTagMarkRuleInfo>().lambda().eq(MemberTagMarkRuleInfo::getTagId, tagId));
    }

    public List<MemberTagMarkRuleInfo> listMarkRuleByTagIds(List<Long> tagIds) {
        return memberTagMarkRuleInfoDao.selectList(new QueryWrapper<MemberTagMarkRuleInfo>().lambda().in(MemberTagMarkRuleInfo::getTagId, tagIds));
    }

    public void batchAdd(List<MemberTagConfigInfo> memberTagConfigInfos) {
        memberTagConfigInfoDao.insertBatch(memberTagConfigInfos);
    }
}
