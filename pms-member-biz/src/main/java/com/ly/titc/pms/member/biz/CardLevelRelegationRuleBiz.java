package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.CardLevelRelegationRuleDetailDao;
import com.ly.titc.pms.member.dal.dao.CardLevelRelegationRuleInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelRelegationRuleBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 卡保级规则配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:47
 */
@Component
public class CardLevelRelegationRuleBiz {

    @Resource
    private CardLevelRelegationRuleInfoDao cardLevelRelegationRuleInfoDao;

    @Resource
    private CardLevelRelegationRuleDetailDao cardLevelRelegationRuleDetailDao;

    public CardLevelRelegationRuleInfo getById(Integer masterType, String masterCode, Long id) {
        LambdaQueryWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getMasterType, masterType)
                .eq(CardLevelRelegationRuleInfo::getMasterCode, masterCode)
                .eq(CardLevelRelegationRuleInfo::getId, id);
        return cardLevelRelegationRuleInfoDao.selectOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(CardLevelRelegationRuleInfo info, List<CardLevelRelegationRuleDetailInfo> relegationRuleDetails) {
        cardLevelRelegationRuleInfoDao.insert(info);
        // 全删全插
        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, info.getId());
        cardLevelRelegationRuleDetailDao.delete(wrapper);
        cardLevelRelegationRuleDetailDao.insertBatch(relegationRuleDetails);
    }

    public List<CardLevelRelegationRuleInfo> listByCardId(Long cardId) {
        LambdaQueryWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getCardId, cardId);
        return cardLevelRelegationRuleInfoDao.selectList(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(CardLevelRelegationRuleInfo info,
                       List<CardLevelRelegationRuleDetailInfo> relegationRuleDetails) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<CardLevelRelegationRuleInfo>()
                .eq(CardLevelRelegationRuleInfo::getId, info.getId())
                .set(CardLevelRelegationRuleInfo::getName, info.getName())
                .set(CardLevelRelegationRuleInfo::getSourceLevel, info.getSourceLevel())
                .set(CardLevelRelegationRuleInfo::getTargetLevel, info.getTargetLevel())
                .set(CardLevelRelegationRuleInfo::getRelegationSuccessfulPerformType, info.getRelegationSuccessfulPerformType())
                .set(CardLevelRelegationRuleInfo::getRelegationFailureRule, info.getRelegationFailureRule())
                .set(CardLevelRelegationRuleInfo::getCycleType, info.getCycleType())
                .set(CardLevelRelegationRuleInfo::getDescription, info.getDescription())
                .set(CardLevelRelegationRuleInfo::getState, info.getState())
                .set(CardLevelRelegationRuleInfo::getSort, info.getSort())
                .set(CardLevelRelegationRuleInfo::getModifyUser, info.getModifyUser());
        cardLevelRelegationRuleInfoDao.update(null, wrapper);
        // 全删全插
        cardLevelRelegationRuleDetailDao.delete(new LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo>()
                .eq(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, info.getId()));
        cardLevelRelegationRuleDetailDao.insertBatch(relegationRuleDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getCardId, cardId);
        wrapper.set(CardLevelRelegationRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleInfo::getModifyUser, operator);
        cardLevelRelegationRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.eq(CardLevelRelegationRuleDetailInfo::getCardId, cardId);
        detailWrapper.set(CardLevelRelegationRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleDetailInfo::getModifyUser, operator);
        cardLevelRelegationRuleDetailDao.update(null, detailWrapper);
    }

    public void deleteByCardId(Long cardId, Integer cardLevel, String operator) {
        LambdaQueryWrapper<CardLevelRelegationRuleInfo> ruleWrapper = new LambdaQueryWrapper<>();
        ruleWrapper.eq(CardLevelRelegationRuleInfo::getCardId, cardId)
                .and(w -> w.eq(CardLevelRelegationRuleInfo::getSourceLevel, cardLevel).or().eq(CardLevelRelegationRuleInfo::getTargetLevel, cardLevel));
        List<CardLevelRelegationRuleInfo> ruleInfos = cardLevelRelegationRuleInfoDao.selectList(ruleWrapper);
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            List<Long> ruleIds = ruleInfos.stream().map(CardLevelRelegationRuleInfo::getId).collect(Collectors.toList());
            deleteByRuleId(ruleIds, operator);
        }

    }

    public void deleteByRuleId(List<Long> ruleIds, String operator) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardLevelRelegationRuleInfo::getId, ruleIds);
        wrapper.set(CardLevelRelegationRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleInfo::getModifyUser, operator);
        cardLevelRelegationRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.in(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, ruleIds);
        detailWrapper.set(CardLevelRelegationRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleDetailInfo::getModifyUser, operator);
        cardLevelRelegationRuleDetailDao.update(null, detailWrapper);
    }

    public void deleteByRuleId(Long ruleId, String operator) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getId, ruleId);
        wrapper.set(CardLevelRelegationRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleInfo::getModifyUser, operator);
        cardLevelRelegationRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.eq(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, ruleId);
        detailWrapper.set(CardLevelRelegationRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleDetailInfo::getModifyUser, operator);
        cardLevelRelegationRuleDetailDao.update(null, detailWrapper);
    }

    public void updateState(Long ruleId, Integer state, String operator) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getId, ruleId);
        wrapper.set(CardLevelRelegationRuleInfo::getState, state)
                .set(CardLevelRelegationRuleInfo::getModifyUser, operator);
        cardLevelRelegationRuleInfoDao.update(null, wrapper);
    }

    public CardLevelRelegationRuleInfo getByCardLevel(Long cardId, Integer memberCardLevel) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getCardId, cardId);
        wrapper.set(CardLevelRelegationRuleInfo::getSourceLevel, memberCardLevel);
        return cardLevelRelegationRuleInfoDao.selectOne(wrapper);
    }

    public List<CardLevelRelegationRuleDetailInfo> listDetailByRuleId(Long ruleId) {
        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, ruleId);
        return cardLevelRelegationRuleDetailDao.selectList(wrapper);
    }

    public List<CardLevelRelegationRuleInfo> listByCardIds(List<Long> cardIds, Integer state) {
        LambdaQueryWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelRelegationRuleInfo::getCardId, cardIds)
                .eq(CardLevelRelegationRuleInfo::getState, state);
        return cardLevelRelegationRuleInfoDao.selectList(wrapper);
    }

    public List<CardLevelRelegationRuleDetailInfo> listDetailByRuleIds(List<Long> ruleIds) {
        LambdaQueryWrapper<CardLevelRelegationRuleDetailInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, ruleIds);
        return cardLevelRelegationRuleDetailDao.selectList(wrapper);
    }

    public IPage<CardLevelRelegationRuleInfo> pageRelegationRule(PageCardLevelRelegationRuleBo pageBo) {
        QueryWrapper<CardLevelRelegationRuleInfo> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Objects.nonNull(pageBo.getCardId()), CardLevelRelegationRuleInfo::getCardId, pageBo.getCardId());
        wrapper.lambda().eq(Objects.nonNull(pageBo.getSourceLevel()), CardLevelRelegationRuleInfo::getSourceLevel, pageBo.getSourceLevel())
                .eq(StringUtils.isNotEmpty(pageBo.getMasterCode()), CardLevelRelegationRuleInfo::getMasterCode, pageBo.getMasterCode())
                .eq(pageBo.getMasterType() != null, CardLevelRelegationRuleInfo::getMasterType, pageBo.getMasterType())
                .eq(pageBo.getState() != null, CardLevelRelegationRuleInfo::getState, pageBo.getState())
                .like(StringUtils.isNotEmpty(pageBo.getName()), CardLevelRelegationRuleInfo::getName, pageBo.getName())
                .orderByDesc(CardLevelRelegationRuleInfo::getSort)
                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(pageBo.getCardIdList()), CardLevelRelegationRuleInfo::getCardId, pageBo.getCardIdList())
                .orderByDesc(CardLevelRelegationRuleInfo::getId);
        return cardLevelRelegationRuleInfoDao.selectPage(new Page<>(pageBo.getPageIndex(), pageBo.getPageSize()), wrapper);
    }
}
