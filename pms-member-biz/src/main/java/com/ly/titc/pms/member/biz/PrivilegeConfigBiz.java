package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.PrivilegeApplicableDataMappingDao;
import com.ly.titc.pms.member.dal.dao.PrivilegeConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import com.ly.titc.pms.member.entity.bo.PagePrivilegeConfigBo;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权益设置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:53
 */
@Component
public class PrivilegeConfigBiz {

    @Resource
    private PrivilegeConfigInfoDao privilegeConfigInfoDao;

    @Resource
    private PrivilegeApplicableDataMappingDao privilegeApplicableDataMappingDao;

    public List<PrivilegeConfigInfo> listByIds(List<Long> privilegeIds) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeConfigInfo::getId, privilegeIds);
        return privilegeConfigInfoDao.selectList(wrapper);
    }

    public List<PrivilegeConfigInfo> listByIds(List<Long> privilegeIds, Integer state) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeConfigInfo::getId, privilegeIds)
                .eq(PrivilegeConfigInfo::getState, state);
        return privilegeConfigInfoDao.selectList(wrapper);
    }

    public List<PrivilegeApplicableDataMapping> listMappingByIds(List<Long> privilegeIds) {
        LambdaQueryWrapper<PrivilegeApplicableDataMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeIds);
        return privilegeApplicableDataMappingDao.selectList(wrapper);
    }

    public void insert(PrivilegeConfigInfo privilegeConfigInfo, List<PrivilegeApplicableDataMapping> mappings) {
        privilegeConfigInfoDao.insert(privilegeConfigInfo);
        privilegeApplicableDataMappingDao.insertBatch(mappings);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(PrivilegeConfigInfo privilegeConfigInfo, List<PrivilegeApplicableDataMapping> mappings) {
        LambdaUpdateWrapper<PrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<PrivilegeConfigInfo>()
                .eq(PrivilegeConfigInfo::getId, privilegeConfigInfo.getId())
                .set(PrivilegeConfigInfo::getType, privilegeConfigInfo.getType())
                .set(PrivilegeConfigInfo::getClassification, privilegeConfigInfo.getClassification())
                .set(PrivilegeConfigInfo::getName, privilegeConfigInfo.getName())
                .set(PrivilegeConfigInfo::getUnit, privilegeConfigInfo.getUnit())
                .set(PrivilegeConfigInfo::getPic, privilegeConfigInfo.getPic())
                .set(PrivilegeConfigInfo::getInstruction, privilegeConfigInfo.getInstruction())
                .set(PrivilegeConfigInfo::getDescription, privilegeConfigInfo.getDescription())
                .set(PrivilegeConfigInfo::getSort, privilegeConfigInfo.getSort())
                .set(PrivilegeConfigInfo::getState, privilegeConfigInfo.getState())
                .set(PrivilegeConfigInfo::getModifyUser, privilegeConfigInfo.getModifyUser());
        privilegeConfigInfoDao.update(null, wrapper);
        privilegeApplicableDataMappingDao.delete(new LambdaQueryWrapper<PrivilegeApplicableDataMapping>()
                .eq(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeConfigInfo.getId()));
        privilegeApplicableDataMappingDao.insertBatch(mappings);
    }

    public PrivilegeConfigInfo getByName(Integer masterType, String masterCode, String name) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrivilegeConfigInfo::getMasterType, masterType)
                .eq(PrivilegeConfigInfo::getMasterCode, masterCode)
                .eq(PrivilegeConfigInfo::getName, name);
        return privilegeConfigInfoDao.selectOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrivilegeId(Long privilegeId, String operator) {
        LambdaUpdateWrapper<PrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<PrivilegeConfigInfo>()
                .eq(PrivilegeConfigInfo::getId, privilegeId)
                .set(PrivilegeConfigInfo::getIsDelete, Constant.ONE)
                .set(PrivilegeConfigInfo::getModifyUser, operator);
        privilegeConfigInfoDao.update(null, wrapper);
        privilegeApplicableDataMappingDao.delete(new LambdaQueryWrapper<PrivilegeApplicableDataMapping>()
                .eq(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeId));
    }

    public PrivilegeConfigInfo getById(Integer masterType, String masterCode, Long privilegeId) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrivilegeConfigInfo::getId, privilegeId)
                .eq(PrivilegeConfigInfo::getMasterType, masterType)
                .eq(PrivilegeConfigInfo::getMasterCode, masterCode);
        return privilegeConfigInfoDao.selectOne(wrapper);
    }

    public void updateState(Long privilegeId, Integer state, String operator) {
        LambdaUpdateWrapper<PrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<PrivilegeConfigInfo>()
                .eq(PrivilegeConfigInfo::getId, privilegeId)
                .set(PrivilegeConfigInfo::getState, state)
                .set(PrivilegeConfigInfo::getModifyUser, operator);
        privilegeConfigInfoDao.update(null, wrapper);
    }

    public List<PrivilegeApplicableDataMapping> listApplicableDataMapping(List<Long> privilegeIdList, String scopeValue) {
        LambdaQueryWrapper<PrivilegeApplicableDataMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeIdList)
                .eq(PrivilegeApplicableDataMapping::getScopeValue, scopeValue);
        return privilegeApplicableDataMappingDao.selectList(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<PrivilegeConfigInfo> infos, List<PrivilegeApplicableDataMapping> mappings) {
        privilegeConfigInfoDao.insertBatch(infos);
        privilegeApplicableDataMappingDao.insertBatch(mappings);
    }

    public List<PrivilegeConfigInfo> listByMaster(Integer masterType, String masterCode) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrivilegeConfigInfo::getMasterType, masterType)
                .eq(PrivilegeConfigInfo::getMasterCode, masterCode);
        return privilegeConfigInfoDao.selectList(wrapper);
    }

    public IPage<PrivilegeConfigInfo> pagePrivilegeConfig(PagePrivilegeConfigBo bo) {
        return privilegeConfigInfoDao.selectPage(new Page<>(bo.getPageIndex(), bo.getPageSize()),
                new LambdaQueryWrapper<PrivilegeConfigInfo>()
                        .eq(PrivilegeConfigInfo::getMasterType, bo.getMasterType())
                        .eq(PrivilegeConfigInfo::getMasterCode, bo.getMasterCode())
                        .eq(StringUtils.isNotBlank(bo.getPrivilegeName()), PrivilegeConfigInfo::getName, bo.getPrivilegeName()));
    }

    public List<PrivilegeApplicableDataMapping> listMappingById(Long privilegeId) {
        LambdaQueryWrapper<PrivilegeApplicableDataMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeId);
        return privilegeApplicableDataMappingDao.selectList(wrapper);
    }
}
