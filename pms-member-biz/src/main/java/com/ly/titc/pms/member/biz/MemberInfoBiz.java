package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.annotation.RedisCache;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.dal.dao.MemberInfoDao;
import com.ly.titc.pms.member.dal.entity.bo.PageMemberParamBo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.springboot.dcdb.dal.core.encrypt.EncryptService;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname MemberInfoBiz
 * @descrition 会员基础业务处理类
 * @since 2023/7/31 17:18
 */
@Slf4j
@Component
public class MemberInfoBiz extends AbstractBiz<MemberInfo> {

    @Resource(type = MemberInfoDao.class)
    protected MemberInfoDao memberInfoDao;

    @Resource
    private EncryptService encryptService;

    /**
     * 根据会员编号获取会员信息
     *
     * @param memberNo
     * @return
     */
    public MemberInfo getByMemberNo(Integer masterType, String masterCode, String memberNo) {
        return memberInfoDao.selectOne(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMemberNo, memberNo));
    }

    /**
     * 根据会员编号获取会员信息
     *
     * @param memberNo
     * @return
     */
    public MemberInfo getByMemberNo(String memberNo) {
        return memberInfoDao.selectOne(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMemberNo, memberNo));
    }

    /**
     * 根据手机号查询会员是否存在
     */
    public boolean existByMobile(Integer masterType, String masterCode, String mobile, String memberNo) {
        LambdaQueryWrapperX<MemberInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMobile, encryptService.encrypt(mobile))
                .ne(MemberInfo::getMemberNo, memberNo);
        return memberInfoDao.exists(wrapper);
    }


    /**
     * 新增
     *
     * @param entity
     * @return
     */
    public void add(MemberInfo entity) {
        memberInfoDao.insert(entity);
    }

    /**
     * 更新会员信息
     *
     * @param entity
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(MemberInfo entity) {

        LambdaUpdateWrapper<MemberInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberInfo::getMasterType, entity.getMasterType())
                .eq(MemberInfo::getMasterCode, entity.getMasterCode())
                .eq(MemberInfo::getMemberNo, entity.getMemberNo())
                .set(MemberInfo::getMobile, encryptService.encrypt(entity.getMobile()))
                .set(MemberInfo::getNickName, entity.getNickName())
                .set(MemberInfo::getRealName, entity.getRealName())
                .set(MemberInfo::getEnName, entity.getEnName())
                .set(MemberInfo::getBirthday, entity.getBirthday())
                .set(MemberInfo::getGender, entity.getGender())
                .set(MemberInfo::getCustomizeMemberNo, entity.getCustomizeMemberNo())
                .set(MemberInfo::getIdType, entity.getIdType())
                .set(MemberInfo::getIdNo, StringUtils.isNotBlank(entity.getIdNo()) ? encryptService.encrypt(entity.getIdNo()) : "")
//                .set(MemberInfo::getState, entity.getState())
                .set(MemberInfo::getModifyUser, entity.getModifyUser())
                .set(MemberInfo::getGmtModified, LocalDateTime.now());
        memberInfoDao.update(null, wrapper);
    }

    /**
     * 会员注销
     *
     * @param memberNo
     */
    public void cancelMember(Integer masterType, String masterCode, String memberNo,String operator) {
        memberInfoDao.update(null, new LambdaUpdateWrapper<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMemberNo, memberNo)
                .set(MemberInfo::getState, MemberStateEnum.CANCEL.getState())
                .set(MemberInfo::getModifyUser, operator));
    }

    /**
     * 会员恢复
     *
     * @param memberNo
     */
    public void recoverMember(Integer masterType, String masterCode, String memberNo, String operator) {
        memberInfoDao.update(null, new LambdaUpdateWrapper<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMemberNo, memberNo)
                .set(MemberInfo::getState, MemberStateEnum.VALID.getState())
                .set(MemberInfo::getModifyUser, operator));
    }


    public List<MemberInfo> listByMobiles(Integer masterType, String masterCode, String mobile, Integer state) {
        LambdaQueryWrapperX<MemberInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMobile, encryptService.encrypt(mobile))
                .eq(MemberInfo::getState, state);
        return memberInfoDao.selectList(wrapper);
    }

    public List<MemberInfo> listByMobiles(Integer masterType, String masterCode, List<String> mobiles, Integer state) {
        List<String> encryptMobiles = mobiles.stream().map(mobile -> encryptService.encrypt(mobile)).collect(Collectors.toList());
        LambdaQueryWrapperX<MemberInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .in(MemberInfo::getMobile, encryptMobiles)
                .eq(MemberInfo::getState, state);
        return memberInfoDao.selectList(wrapper);
    }

    public List<MemberInfo> listByIdNos(Integer masterType, String masterCode, Integer idType, List<String> idNos, Integer state) {
        List<String> encryptIdNos = idNos.stream().map(idNo -> encryptService.encrypt(idNo)).collect(Collectors.toList());
        LambdaQueryWrapperX<MemberInfo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getIdType, idType)
                .in(MemberInfo::getIdNo, encryptIdNos)
                .eq(MemberInfo::getState, state);
        return memberInfoDao.selectList(wrapper);
    }

    /**
     * 分页查询会员
     *
     * @param pageMemberParamBo
     * @return
     */
    public IPage<String> pageMember(PageMemberParamBo pageMemberParamBo) {
        Page<String> page = new Page<>(pageMemberParamBo.getPageIndex(), pageMemberParamBo.getPageSize());
        if (StringUtils.isNotBlank(pageMemberParamBo.getMobile())) {
            pageMemberParamBo.setMobile(encryptService.encrypt(pageMemberParamBo.getMobile()));
        }
        if (StringUtils.isNotBlank(pageMemberParamBo.getIdNo())) {
            pageMemberParamBo.setIdNo(encryptService.encrypt(pageMemberParamBo.getIdNo()));
        }
        return memberInfoDao.pageMember(pageMemberParamBo, page);
    }

    /**
     * 根据会员号列表查询
     *
     * @param memberNos
     * @return
     */
    public List<MemberInfo> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos) {
        return memberInfoDao.selectList(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .in(MemberInfo::getMemberNo, memberNos));
    }

    /**
     * 根据手机号查询会员
     *
     * @param masterType
     * @param masterCode
     * @param mobile
     * @return
     */
    public MemberInfo getByMobile(Integer masterType, String masterCode, String mobile) {
        return memberInfoDao.selectOne(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMobile, encryptService.encrypt(mobile)));
    }

    public MemberInfo getByMobile(Integer masterType, String masterCode, String mobile,  String memberNo) {
        return memberInfoDao.selectOne(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getMobile, encryptService.encrypt(mobile))
                .ne(StringUtils.isNotEmpty(memberNo), MemberInfo::getMemberNo, memberNo));
    }

    /**
     * 根据手机号查询会员
     *
     * @param masterType
     * @param masterCode
     * @param mobile
     * @return
     */
    public  List<MemberInfo> matchMobileName(Integer masterType, String masterCode, String realName, String mobile,Integer size) {
        if(StringUtils.isNotEmpty(mobile)){
            mobile = encryptService.encrypt(mobile);
        }
        return memberInfoDao.matchMobileName(mobile, realName, masterType, masterCode, size);
    }


    public Long selectCount(Integer masterType, String masterCode, String hotelCode, String startTime, String endTime) {
        return memberInfoDao.selectCount(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getRegisterHotelType, "HOTEL")
                .eq(MemberInfo::getRegisterHotel, hotelCode)
                .eq(MemberInfo::getState, MemberStateEnum.VALID.getState())
                .between(MemberInfo::getGmtCreate, startTime, endTime));
    }

    public List<MemberInfo> listByHotelCode(Integer masterType, String masterCode, String hotelCode) {
        return memberInfoDao.selectList(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getRegisterHotelType, "HOTEL")
                .eq(MemberInfo::getRegisterHotel, hotelCode)
                .eq(MemberInfo::getState, MemberStateEnum.VALID.getState()));
    }

    public List<MemberInfo> listMembers(Integer masterType, String masterCode) {
        return memberInfoDao.selectList(new LambdaQueryWrapperX<MemberInfo>()
                .eq(MemberInfo::getMasterType, masterType)
                .eq(MemberInfo::getMasterCode, masterCode)
                .eq(MemberInfo::getState, MemberStateEnum.VALID.getState()));
    }

}
