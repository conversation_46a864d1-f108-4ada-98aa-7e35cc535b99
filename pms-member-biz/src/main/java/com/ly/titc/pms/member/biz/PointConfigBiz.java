package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.PointConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.PointConfigInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 积分设置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:52
 */
@Component
public class PointConfigBiz {

    @Resource
    private PointConfigInfoDao pointConfigInfoDao;

    public PointConfigInfo getByMaster(Integer masterType, String masterCode) {
        LambdaQueryWrapper<PointConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointConfigInfo::getMasterType, masterType)
                .eq(PointConfigInfo::getMasterCode, masterCode);
        return pointConfigInfoDao.selectOne(wrapper);
    }

    public PointConfigInfo getById(Long id) {
        return pointConfigInfoDao.selectById(id);
    }

    public void insert(PointConfigInfo configInfo) {
        pointConfigInfoDao.insert(configInfo);
    }

    public void update(PointConfigInfo configInfo) {
        LambdaUpdateWrapper<PointConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PointConfigInfo::getId, configInfo.getId());
        wrapper.set(PointConfigInfo::getPointLimit, configInfo.getPointLimit())
                .set(PointConfigInfo::getPointLimitUnit, configInfo.getPointLimitUnit())
                .set(PointConfigInfo::getPointLimitLong, configInfo.getPointLimitLong())
                .set(PointConfigInfo::getAllowExchangeVerifyModify, configInfo.getAllowExchangeVerifyModify())
                .set(PointConfigInfo::getAllowMemberPointStrategyModify, configInfo.getAllowMemberPointStrategyModify())
                .set(PointConfigInfo::getIsExchangeVerifyRequired, configInfo.getIsExchangeVerifyRequired())
                .set(PointConfigInfo::getMemberPointStrategy, configInfo.getMemberPointStrategy())
                .set(PointConfigInfo::getModifyUser, configInfo.getModifyUser());
        pointConfigInfoDao.update(null, wrapper);
    }
}
