<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.ly.titc</groupId>
  <artifactId>pms-member-parent</artifactId>
  <packaging>pom</packaging>
  <version>1.0.0</version>
  <modules>
    <module>pms-member-dal</module>
    <module>pms-member-com</module>
    <module>pms-member-biz</module>
    <module>pms-member-facade</module>
    <module>pms-member-mediator</module>
    <module>pms-member-interfaces</module>
    <module>pms-member-dubbo-provider</module>
    <module>pms-member-job</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <!--##########################======springboot begin分割线=======###########################-->
    <!--springboot-->
    <spring-boot-dependencies.version>1.0.2-SNAPSHOT</spring-boot-dependencies.version>
    <!--##########################======springboot end分割线=========###########################-->

    <!--##########################======java begin分割线===============###########################-->
    <!--lombok -->
    <lombok.version>1.18.20</lombok.version>
    <!--mapstruct-->
    <mapstruct.version>1.4.2.Final</mapstruct.version>
    <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    <!--##########################======java end分割线=================###########################-->

    <!--##########################======titc begin分割线===============###########################-->
    <!--api-->
    <cdm-interfaces.version>1.0.0-SNAPSHOT</cdm-interfaces.version>
    <mdm-api.version>1.1.3-SNAPSHOT</mdm-api.version>
    <pms-member-interfaces.version>1.0.1-SNAPSHOT</pms-member-interfaces.version>
    <com-component-interfaces.version>1.0.0-SNAPSHOT</com-component-interfaces.version>
    <com-component-sdk.version>1.0.3-SNAPSHOT</com-component-sdk.version>
    <cashier-interfaces.version>1.0.3-SNAPSHOT</cashier-interfaces.version>
    <pms-account-interfaces.version>1.0.5-SNAPSHOT</pms-account-interfaces.version>
    <pms-member-asset-interfaces.version>1.0.0-SNAPSHOT</pms-member-asset-interfaces.version>
    <pms-spm-interfaces.version>1.0.4-SNAPSHOT</pms-spm-interfaces.version>
    <pms-customer-interfaces.version>1.0.0-SNAPSHOT</pms-customer-interfaces.version>
    <chm-api.version>1.1.6-SNAPSHOT</chm-api.version>
    <!--##########################======titc end分割线=================###########################-->

    <springboot-mq.version>2.0.3-SNAPSHOT</springboot-mq.version>
    <com.common.version>1.0.10-SNAPSHOT</com.common.version>
    <oauth-client.version>1.0.6-SNAPSHOT</oauth-client.version>
    <ehr-api.version>1.0.3-SNAPSHOT</ehr-api.version>
    <com.ly.dal.version>3.6.6</com.ly.dal.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!--##########################======springboot begin分割线=======###########################-->
      <!--springboot dependencies -->
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-dependencies-pom</artifactId>
        <version>${spring-boot-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
        <exclusions>
          <exclusion>
            <groupId>com.ly.titc</groupId>
            <artifactId>springboot-start-dcdb-dal</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <!--##########################======springboot end分割线=======#############################-->

      <!--##########################======titc begin分割线=============###########################-->
      <!--api-->
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>mdm-api</artifactId>
        <version>${mdm-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>cdm-interfaces</artifactId>
        <version>${cdm-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-member-interfaces</artifactId>
        <version>${pms-member-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-member-asset-interfaces</artifactId>
        <version>${pms-member-asset-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>com-component-interfaces</artifactId>
        <version>${com-component-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>com-component-sdk</artifactId>
        <version>${com-component-sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>common</artifactId>
        <version>${com.common.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.ly.dsf</groupId>
            <artifactId>dsf-spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>oauth-client</artifactId>
        <version>${oauth-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>ehr-api</artifactId>
        <version>${ehr-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>cashier-interfaces</artifactId>
        <version>${cashier-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-account-interfaces</artifactId>
        <version>${pms-account-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-spm-interfaces</artifactId>
        <version>${pms-spm-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-customer-interfaces</artifactId>
        <version>${pms-customer-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-dcdb-dal</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>chm-api</artifactId>
        <version>${chm-api.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <!-- 打包环境配置 -->
  <profiles>
    <profile>
      <id>test</id>
      <properties>
        <package.environment>test</package.environment>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>qa</id>
      <properties>
        <package.environment>qa</package.environment>
      </properties>
    </profile>
    <profile>
      <id>uat</id>
      <properties>
        <package.environment>uat</package.environment>
      </properties>
    </profile>
    <profile>
      <id>stage</id>
      <properties>
        <package.environment>stage</package.environment>
      </properties>
    </profile>
    <profile>
      <id>product</id>
      <properties>
        <package.environment>product</package.environment>
      </properties>
    </profile>
  </profiles>

  <repositories>
    <!-- &lt;!&ndash; ly.com maven mirror &ndash;&gt;-->
    <repository>
      <id>lyRepository</id>
      <name>lyRepository</name>
      <url>https://nexus.17usoft.com/repository/mvn-all/</url>
      <layout>default</layout>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>
</project>