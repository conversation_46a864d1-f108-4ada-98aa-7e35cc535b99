package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员标签标记规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("member_tag_mark_rule_info")
public class MemberTagMarkRuleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 打标条件类型
     */
    private Integer conditionType;

    /**
     * 计算方式
     */
    private Integer calculateType;

    /**
     * 条件值
     */
    private String conditionValue;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
