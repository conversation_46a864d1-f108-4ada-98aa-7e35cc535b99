package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员系统设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("point_config_info")
public class PointConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 积分有效期
     */
    private Integer pointLimit;

    /**
     * 积分有效期单位 年 YEAR 月:MONTH(date)
     */
    private String pointLimitUnit;

    /**
     * 积分有效期 是否长期有效
     */
    private Integer pointLimitLong;

    /**
     * 积分兑换是否需要验证 (0-否, 1-是)
     */
    private Integer isExchangeVerifyRequired;

    /**
     * 是否允许酒店修改兑换设置 (仅集团层级有效)
     */
    private Integer allowExchangeVerifyModify;

    /**
     * 客房消费积分会员策略 1、房间主客 2、离店时选择
     */
    private Integer memberPointStrategy;

    /**
     * 是否允许酒店修改客房消费积分会员策略设置 (仅集团层级有效)
     */
    private Integer allowMemberPointStrategyModify;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
