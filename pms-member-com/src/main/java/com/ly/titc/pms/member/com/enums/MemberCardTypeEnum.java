package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：MemberCardTypeEnum
 * @Date：2024-11-19 19:22
 * @Filename：MemberCardTypeEnum
 */
public enum MemberCardTypeEnum {

    BASIC(1, "基础卡"),
    ENTERPRISE(2, "企业卡");

    private Integer type;

    private String name;

    MemberCardTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static MemberCardTypeEnum getByType(Integer type) {
        for (MemberCardTypeEnum memberCardTypeEnum : values()) {
            if (memberCardTypeEnum.getType().equals(type)) {
                return memberCardTypeEnum;
            }
        }
        return null;
    }

    public static String getNameByType(Integer type) {
        return getByType(type).getName();
    }
}
