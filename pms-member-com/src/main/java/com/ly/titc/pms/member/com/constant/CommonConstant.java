package com.ly.titc.pms.member.com.constant;

/**
 * <AUTHOR>
 * @date 2024/11/6 10:54
 */
public interface CommonConstant {

    String MEMBER_CONFIG_INIT_IDEMPOTENT_PREFIX = "MEMBER_CONFIG_INIT_IDEMPOTENT";

    String REGISTER_IDEMPOTENT_PREFIX = "REGISTER_IDEMPOTENT";

    String ADDRESS_IDEMPOTENT_PREFIX = "ADDRESS_IDEMPOTENT";

    String INVOICE_HEADER_IDEMPOTENT_PREFIX = "INVOICE_HEADER_IDEMPOTENT";

    String OCCUPANTS_IDEMPOTENT_PREFIX = "OCCUPANTS_IDEMPOTENT";

    String TAG_IDEMPOTENT_PREFIX = "TAG_IDEMPOTENT";

    String CARD_NO_GENERATE_PREFIX = "CARD_NO_GENERATE";

    String CARD_NO_GENERATE_IDEMPOTENT_PREFIX = "CARD_NO_GENERATE_IDEMPOTENT";

    String TRACKING_PREFIX = "PMS_MEMBER_GATEWAY_%s";

    String CHECK_IN_RECORD_IDEMPOTENT_PREFIX = "CHECKIN_RECORD_IDEMPOTENT";

    String CHECK_IN_SUMMARY_IDEMPOTENT_PREFIX = "CHECKIN_SUMMARY_IDEMPOTENT";

    String CARD_CONFIG_SAVE_IDEMPOTENT_PREFIX = "CARD_CONFIG_IDEMPOTENT";

    String CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX = "CARD_LEVEL_CONFIG_IDEMPOTENT";

    String MEMBER_CONFIG_SAVE_IDEMPOTENT_PREFIX = "MEMBER_CONFIG_IDEMPOTENT";

    String MEMBER_TAG_SAVE_IDEMPOTENT_PREFIX = "MEMBER_TAG_IDEMPOTENT";

    String PRIVILEGE_SAVE_IDEMPOTENT_PREFIX = "PRIVILEGE_IDEMPOTENT";

    String POINT_CONFIG_SAVE_IDEMPOTENT_PREFIX = "POINT_CONFIG_IDEMPOTENT";

    /**
     * 日志操作状态
     */
    String LOG_SUCCESS_STATE = "SUCCESS";

    /**
     *  项目配置
     */
    String PROJECT_CONFIG = "PROJECT_CONFIG";


    /**
     * 项目code
     */
    String PROJECT_CODE = "PMS-MEMBER";

    String MEMBER_MANAGE_CODE = "1";

    String MEMBER_MANAGE_NAME = "会员管理";

    String REGISTER_SMS_PREFIX = "REGISTER_SMS";

    String REGISTER_VERIFY_CODE_PREFIX = "REGISTER_VERIFY_CODE";


    /**
     * lockKey
     */
     String CREATE_ORDER_LOCK_KEY_PREFIX = "CREATE_ORDER_LOCK_KEY_PREFIX";

     String CREATE_REFUND_ORDER_LOCK_KEY_PREFIX = "CREATE_REFUND_ORDER_LOCK_KEY_PREFIX";

     String PAY_LOCK_KEY_PREFIX = "PAY_LOCK_KEY_PREFIX";

    /**
     * 图片上传地址
     */
    String IMAGE_PATH = "image/";

}
