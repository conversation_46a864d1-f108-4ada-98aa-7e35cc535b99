
package com.ly.titc.pms.member.com.enums;

import java.util.Arrays;
import java.util.List;

/**
 * @Author：rui
 * @name：PrivilegeClassificationEnum
 * @Date：2024-11-19 23:34
 * @Filename：PrivilegeClassificationEnum
 */
public enum PrivilegeTypeEnum {
   // 权益类型 1 价格权益 2 积分权益 3 线下权益 4 生态权益

    PRICE(1, "价格权益"),
    INTEGRAL(2, "积分权益"),
    OFFLINE(3, "线下权益"),
    ECOSYSTEM(4, "生态权益");

    private Integer type;

    private String name;

    public String getName() {
        return name;
    }

    public static String getNameByType(Integer type) {
        for (PrivilegeTypeEnum value : PrivilegeTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }

    public static List<Integer> getOrderPrivilegeType(){
        return Arrays.asList(2,3);
    }

    public Integer getType() {
        return type;
    }

    PrivilegeTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
