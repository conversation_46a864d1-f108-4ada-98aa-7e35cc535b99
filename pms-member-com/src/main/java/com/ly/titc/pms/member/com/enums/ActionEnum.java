package com.ly.titc.pms.member.com.enums;

import java.util.Arrays;

/**
 * @Author：rui
 * @name：ActionEnum
 * @Date：2024-11-12 17:04
 * @Filename：ActionEnum
 */
public enum ActionEnum {

    ADD(0,0,"新增"),
    SAVE(1,1,"编辑"),
    DELETE(2,2,"删除"),
    STOP(3,4,"启用"),
    START(4,4,"停用"),
    MEMBER_REGISTER(5,5,"会员注册"),
    MEMBER_INFO_UPDATE(6,6,"信息修改"),
    MEMBER_COMMON_PROFILE_ADD(7,7,"常用信息新增"),
    MEMBER_COMMON_PROFILE_UPDATE(8,8,"常用信息编辑"),
    MEMBER_COMMON_PROFILE_DELETE(9,9,"常用信息删除"),
    MEMBER_TAG_ADD(10,10,"标签新增"),
    MEMBER_TAG_DELETE(11,11,"标签删除"),
    MEMBER_LEVEL_UPDATE(12,12,"会员升级"),
    MEMBER_LEVEL_DOWNGRADE(13,13,"会员降级"),
    MEMBER_LEVEL_KEEP(14,14,"会员保级"),
    MEMBER_CARD_ISSUE(15,15,"发放会员卡"),
    MEMBER_CARD_RECYCLE(16,16,"回收会员卡")
    ;

    private Integer code;
    private String desc;
    private int sort;

    ActionEnum(Integer code, int sort, String desc) {
        this.code = code;
        this.sort = sort;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public static ActionEnum getByCode(Integer code) {
        return null == code ? null : Arrays.stream(ActionEnum.values()).filter(e -> e.getCode() == code)
                .findFirst().orElse(null);
    }
}
