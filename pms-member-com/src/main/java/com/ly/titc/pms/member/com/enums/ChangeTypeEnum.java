package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：ChangeTypeEnum
 * @Date：2024-11-21 17:04
 * @Filename：ChangeTypeEnum
 */
public enum ChangeTypeEnum {

    //等级变化类型，1注册; 2升级；3保级成功；4保级失败; 5手动处理; 6迁移数据
    REGISTER(1, "注册"),
    UPGRADE_AUTO(2, "自动升级"),
    DOWN_AUTO(3, "自动降级"),
    UPGRADE_ARTIFICIAL(4, "人工升级"),
    UPGRADE_PURCHASE(5, "购买升级"),
    SUCCESS(6, "保级成功"),
    MIGRATION(7, "迁移数据"),
    CANCEL(8, "注销"),
    ISSUE(9, "发卡"),// 已有一张其他卡，重新发放一张卡
    RECYCLE(10, "回收"); // 发卡的反向操作，回收这张卡

    private Integer type;
    private String desc;

    ChangeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(Integer type) {
        for (ChangeTypeEnum value : ChangeTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static Integer getTypeByDesc(String desc) {
        for (ChangeTypeEnum value : ChangeTypeEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value.getType();
            }
        }
        return null;
    }
}
