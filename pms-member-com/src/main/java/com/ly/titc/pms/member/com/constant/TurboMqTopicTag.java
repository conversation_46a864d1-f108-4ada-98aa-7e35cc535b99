package com.ly.titc.pms.member.com.constant;

/**
 * <AUTHOR>
 * @classname TurbomqTopicTag
 * @descrition
 * @since 2022/11/27 上午11:46
 */
public interface TurboMqTopicTag {

    /**
     * 会员变动消息
     */
    String MEMBER_EVENT = "MEMBER_EVENT";

    /**
     * 会员卡号生成
     */
    String CARD_NO_GENERATE = "CARD_NO_GENERATE";

    /**
     * 会员入住记录汇总
     */
    String CHECK_IN_SUMMARY = "CHECK_IN_SUMMARY";

    /**
     * 会员定时任务
     */
    String MEMBER_SCHEDULE = "MEMBER_SCHEDULE";

    /**
     * 会员活动回滚
     */
    String MEMBER_ACTIVITY_ROLLBACK_TAG = "member_activity_rollback";

    /**
     * 会员活动发放
     */
    String MEMBER_ACTIVITY_GRANT_TAG = "member_activity_grant";

    /**
     * 客人事件
     */
    String CUSTOMER_EVENT = "CUSTOMER_EVENT";
}
