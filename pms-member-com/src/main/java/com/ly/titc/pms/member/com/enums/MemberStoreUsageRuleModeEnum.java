package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 10:20
 */
@Getter
@AllArgsConstructor
public enum MemberStoreUsageRuleModeEnum {

    SINGLE_STORE("SINGLE", "唯一"),
    MULTI_STORE("MULTI", "混合"),;

    private String code;

    private String desc;

    public static MemberStoreUsageRuleModeEnum getByCode(String code) {
        for (MemberStoreUsageRuleModeEnum memberStoreUsageRuleModeEnum : MemberStoreUsageRuleModeEnum.values()) {
            if (memberStoreUsageRuleModeEnum.getCode().equals(code)) {
                return memberStoreUsageRuleModeEnum;
            }
        }
        return null;
    }
}
