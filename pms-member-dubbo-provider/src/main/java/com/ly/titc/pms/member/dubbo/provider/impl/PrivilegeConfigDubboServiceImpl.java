package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.entity.request.GetByIdReq;
import com.ly.titc.pms.member.dubbo.entity.request.privilege.*;
import com.ly.titc.pms.member.dubbo.entity.response.PrivilegeConfigResp;
import com.ly.titc.pms.member.dubbo.interfaces.PrivilegeConfigDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.PrivilegeConfigConverter;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.*;
import com.ly.titc.pms.member.mediator.service.PrivilegeConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27 19:47
 */
@Slf4j
@Validated
@DubboService
public class PrivilegeConfigDubboServiceImpl implements PrivilegeConfigDubboService {

    @Resource
    private PrivilegeConfigMedService privilegeConfigMedService;

    @Resource
    private PrivilegeConfigConverter privilegeConfigConverter;


    @Override
    public Response<Long> savePrivilegeConfig(SavePrivilegeConfigReq request) {
        SavePrivilegeConfigDto dto = privilegeConfigConverter.convertReqToDto(request);
        Long privilegeId = privilegeConfigMedService.savePrivilegeConfig(dto);
        return Response.success(privilegeId);
    }

    @Override
    public Response<String> deletePrivilegeConfig(DeletePrivilegeConfigReq request) {
        DeletePrivilegeConfigDto dto = privilegeConfigConverter.convertReqToDto(request);
        privilegeConfigMedService.deletePrivilegeConfig(dto);
        return Response.success(null);
    }

    @Override
    public Response<Long> actionPrivilegeConfig(ActionPrivilegeConfigReq request) {
        ActionPrivilegeConfigDto dto = privilegeConfigConverter.convertReqToDto(request);
        privilegeConfigMedService.actionPrivilegeConfig(dto);
        return Response.success(null);
    }

    @Override
    public Response<PrivilegeConfigResp> getPrivilegeConfig(GetByIdReq request) {
        PrivilegeConfigDto privilegeConfig = privilegeConfigMedService.getPrivilegeConfig(request.getMasterType(), request.getMasterCode(), request.getId());
        return Response.success(privilegeConfigConverter.convertDtoToResp(privilegeConfig));
    }

    @Override
    public Response<List<PrivilegeConfigResp>> listPrivilegeConfig(BaseMasterReq request) {
        List<PrivilegeConfigDto> privilegeConfigs = privilegeConfigMedService.listPrivilegeConfig(request.getMasterType(), request.getMasterCode());
        return Response.success(privilegeConfigs.stream().map(privilegeConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<Pageable<PrivilegeConfigResp>> pagePrivilegeConfig(PagePrivilegeConfigReq request) {
        PagePrivilegeConfigDto dto = privilegeConfigConverter.convertReqToDto(request);
        Pageable<PrivilegeConfigDto> pageable = privilegeConfigMedService.pagePrivilegeConfig(dto);
        return Response.success(PageableUtil.convert(pageable, privilegeConfigConverter::convertDtoToResp));
    }

    @Override
    public Response<Boolean> checkPrivilegeUsed(CheckPrivilegeUsedReq request) {
        boolean flag = privilegeConfigMedService.checkPrivilegeUsed(request.getMasterType(), request.getMasterCode(), request.getPrivilegeId());
        return Response.success(flag);
    }
}
