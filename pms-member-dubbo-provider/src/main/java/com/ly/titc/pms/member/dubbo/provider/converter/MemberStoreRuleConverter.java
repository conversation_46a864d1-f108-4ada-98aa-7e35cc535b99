package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SaveStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreAvailableUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.response.SaveStoreUsageRuleResultResp;
import com.ly.titc.pms.member.mediator.entity.dto.store.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/12 19:01
 */
@Mapper(componentModel = "spring")
public interface MemberStoreRuleConverter {
    SaveStoreConfigDto convertReqToDto(SaveStoreConfigReq request);

    MemberStoreConfigResp convertDtoToResp(StoreConfigDto dto);

    PageStoreUsageDto convertReqToDto(PageStoreUsageRuleReq request);

    MemberStoreUsageRuleResp convertDtoToResp(StoreUsageRuleDetailDto storeUsageRuleDetailDto);

    SaveStoreUsageRuleDto convertReqToDto(SaveStoreUsageConfigReq request);

    SaveStoreUsageRuleResultResp convertDtoToResp(SaveUsageRuleResultDto dto);

    UpdateStoreUsageStateDto convertRequestToDto(UpdateStoreUsageStateReq request);

    DeleteStoreUsageDto convertReqToDto(DeleteStoreUsageReq request);

    SaveStoreUsageRuleResultResp convertDtoToResp(MemberUsageRuleSaveResultDto memberUsageRuleSaveResultDto);

    ListStoreRuleDto convertReqToDto(ListStoreRuleReq request);

    MemberStoreAvailableUsageRuleResp convertDtoToResp(StoreUsageRuleDto storeUsageRuleDto);

}
