package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.member.config.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRelatedConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberTagConfigDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberTagConfigResp;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.*;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/27 18:38
 */
@Mapper(componentModel = "spring")
public interface MemberConfigConverter {
    SaveMemberRelatedConfigDto convertReqToDto(SaveMemberRelatedConfigReq request);

    GetMemberRelatedConfigDto convertReqToDto(GetMemberRelatedConfigReq request);

    MemberRelatedConfigResp convertDtoToResp(MemberRelatedConfigDto memberRelatedConfig);

    SaveMemberTagConfigDto convertReqToDto(SaveMemberTagConfigReq request);

    PageMemberTagConfigDto convertReqToDto(PageMemberTagConfigReq request);

    MemberTagConfigResp convertDtoToResp(MemberTagConfigDto memberRelatedConfig);

    ListMemberTagConfigDto convertReqToDto(ListMemberTagConfigReq request);

    MemberTagConfigDetailResp convertDtoToResp(MemberTagConfigDetailDto memberTagConfigDetailDto);

    DeleteMemberTagDto convertReqToDto(DeleteMemberTagConfigReq request);
}
