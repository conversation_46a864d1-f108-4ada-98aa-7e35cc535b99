package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.com.enums.BlacklistSceneEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.BlacklistMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.CancelBlacklistMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.ListBlacklistParamReq;
import com.ly.titc.pms.member.dubbo.entity.response.BlacklistInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.SelectValueResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberBlacklistDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberBlacklistConverter;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.CancelBlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.ListBlacklistParamDto;
import com.ly.titc.pms.member.mediator.service.MemberBlacklistMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员黑名单Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2025/2/7 16:19
 */
@Slf4j
@Validated
@DubboService
public class MemberBlacklistDubboServiceImpl implements MemberBlacklistDubboService {

    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;

    @Resource
    private MemberBlacklistConverter memberBlacklistConverter;

    @Override
    public Response<SelectValueResp> listBlacklistScene(BaseReq req) {
        return Response.success(Arrays.stream(BlacklistSceneEnum.values())
                .map(e -> new SelectValueResp().setText(e.getDesc()).setValue(e.getType())).collect(Collectors.toList()));
    }

    @Override
    public Response<SelectValueResp> listPlatformChannel(BaseReq req) {
        return Response.success(Arrays.stream(PlatformChannelEnum.values())
                .map(e -> new SelectValueResp().setText(e.getPlatformChannelDesc()).setValue(e.getPlatformChannel())).collect(Collectors.toList()));
    }

    @Override
    public Response<String> blacklist(BlacklistMemberReq request) {
        BlacklistMemberDto dto = memberBlacklistConverter.convertRequestToDto(request);
        String blacklistNo = memberBlacklistMedService.blacklist(dto);
        return Response.success(blacklistNo);
    }

    @Override
    public Response<String> cancelBlacklist(CancelBlacklistMemberReq request) {
        CancelBlacklistMemberDto dto = memberBlacklistConverter.convertRequestToDto(request);
        String blacklistNo = memberBlacklistMedService.cancelBlacklist(dto);
        return Response.success(blacklistNo);
    }

    @Override
    public Response<List<BlacklistInfoResp>> listBlacklist(ListBlacklistParamReq request) {
        ListBlacklistParamDto dto = memberBlacklistConverter.convertRequestToDto(request);
        List<BlacklistInfoDto> list = memberBlacklistMedService.listBlacklist(dto);
        return Response.success(memberBlacklistConverter.convertDtoToResp(list));
    }
}
