package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.card.*;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDto;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/27 11:40
 */
@Mapper(componentModel = "spring")
public interface CardConfigConverter {
    SaveCardConfigDto convertReqToDto(SaveCardConfigReq request);

    CardConfigResp convertDtoToResp(CardConfigDto defaultCard);

    DeleteCardConfigDto convertReqToDto(DeleteCardConfigReq request);

    SaveCardLevelConfigDto convertReqToDto(SaveCardLevelConfigReq request);

    DeleteCardLevelConfigDto convertReqToDto(DeleteCardLevelConfigReq request);

    ListCardLevelConfigDto convertReqToDto(ListCardLevelConfigReq request);

    CardLevelConfigResp convertDtoToResp(CardLevelConfigWithPrivilegeDto cardLevelConfig);

    PageCardLevelConfigDto convertReqToDto(PageCardLevelConfigReq request);

    ActionCardLevelDto convertReqToDto(ActionCardLevelReq request);

    CardFullConfigResp convertDtoToResp(CardFullConfigDto cardFullConfig);

    GetCardLevelUpgradeRuleDto convertReqToDto(GetUpgradeRuleReq request);

    CardLevelUpgradeRuleResp convertDtoToResp(CardLevelUpgradeRuleDto cardLevelUpgradeRule);

    ListCardLevelUpgradeRuleDto convertReqToDto(ListCardLevelUpgradeRuleReq request);

    PageCardLevelUpgradeRuleDto convertReqToDto(PageCardLevelUpgradeRuleReq request);

    CardLevelRelegationRuleResp convertDtoToResp(CardLevelRelegationRuleDto relegationRule);

    ListCardLevelRelegationRuleDto convertReqToDto(ListCardLevelRelegationRuleReq request);

    PageCardLevelRelegationRuleDto convertReqToDto(PageCardLevelRelegationRuleReq request);

    GetCardLevelRelegationRuleDto convertReqToDto(GetRelegationRuleReq request);

    DeleteUpgradeRuleDto convertReqToDto(DeleteUpgradeRuleReq request);

    ActionUpgradeRuleDto convertReqToDto(ActionUpgradeRuleReq request);

    DeleteRelegationRuleDto convertReqToDto(DeleteRelegationRuleReq request);

    ActionRelegationRuleDto convertReqToDto(ActionRelegationRuleReq request);

    ListCardLevelPrivilegeDto convertReqToDto(ListCardLevelPrivilegeReq request);

    CardLevelPrivilegeConfigResp convertDtoToResp(CardLevelPrivilegeConfigDto cardLevelPrivilegeConfigDto);

    PrivilegeConfigResp convertDtoToResp(PrivilegeConfigDto privilegeConfigDto);

    SaveCardLevelUpgradeRuleDto convertReqToDto(SaveCardLevelUpgradeRuleReq request);

    SaveCardLevelRelegationRuleDto convertReqToDto(SaveCardLevelRelegationRuleReq request);
}
