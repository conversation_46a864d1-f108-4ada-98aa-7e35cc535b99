package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.dubbo.entity.request.asset.GetStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SaveStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreAvailableUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.response.SaveStoreUsageRuleResultResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberStoreRuleDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberStoreRuleConverter;
import com.ly.titc.pms.member.mediator.entity.dto.store.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import com.ly.titc.pms.member.mediator.service.StoreConfigMedService;
import com.ly.titc.pms.member.mediator.service.StoreUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:11
 */
@Slf4j
@Validated
@DubboService
public class MemberStoreRuleDubboServiceImpl implements MemberStoreRuleDubboService {

    @Resource
    private StoreUsageMedService storeUsageMedService;
    @Resource
    private StoreConfigMedService storeConfigMedService;
    @Resource
    private MemberStoreRuleConverter memberStoreRuleConverter;

    @Override
    public Response<Boolean> saveConfig(SaveStoreConfigReq request) {
        SaveStoreConfigDto dto = memberStoreRuleConverter.convertReqToDto(request);
        storeConfigMedService.saveStoreConfig(dto);
        return Response.success(true);
    }

    @Override
    public Response<MemberStoreConfigResp> getConfig(GetStoreConfigReq request) {
        StoreConfigDto dto = storeConfigMedService.getStoreConfig(request.getBlocCode(), request.getMasterType(), request.getMasterCode());
        return Response.success(memberStoreRuleConverter.convertDtoToResp(dto));
    }

    @Override
    public Response<Pageable<MemberStoreUsageRuleResp>> pageRule(PageStoreUsageRuleReq request) {
        PageStoreUsageDto dto = memberStoreRuleConverter.convertReqToDto(request);
        Pageable<StoreUsageRuleDetailDto> page = storeUsageMedService.pageStoreUsageRule(dto);
        return Response.success(PageableUtil.convert(page, memberStoreRuleConverter::convertDtoToResp));
    }

    @Override
    public Response<List<SaveStoreUsageRuleResultResp>> saveRule(SaveStoreUsageConfigReq request) {
        SaveStoreUsageRuleDto dto = memberStoreRuleConverter.convertReqToDto(request);
        SaveStoreUsageRuleResultDto result = storeUsageMedService.saveStoreUsageRule(dto);
        List<SaveStoreUsageRuleResultResp> results = result.getRepeatedList().stream().map(memberStoreRuleConverter::convertDtoToResp).collect(Collectors.toList());
        return Response.success(results);
    }

    @Override
    public Response<Boolean> updateState(UpdateStoreUsageStateReq request) {
        UpdateStoreUsageStateDto dto = memberStoreRuleConverter.convertRequestToDto(request);
        storeUsageMedService.updateState(dto);
        return Response.success(true);
    }

    @Override
    public Response<Boolean> deleteRule(DeleteStoreUsageReq request) {
        DeleteStoreUsageDto dto = memberStoreRuleConverter.convertReqToDto(request);
        storeUsageMedService.deleteStoreUsageRule(dto);
        return Response.success(null);
    }

    @Override
    public Response<List<SaveStoreUsageRuleResultResp>> remind(UsageRuleRemindReq request) {
        List<MemberUsageRuleSaveResultDto> results = storeUsageMedService.remind(request.getBlocCode(), request.getMasterType(), request.getMasterCode());
        return Response.success(results.stream().map(memberStoreRuleConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<List<MemberStoreAvailableUsageRuleResp>> listAvailableRule(ListStoreRuleReq request) {
        ListStoreRuleDto dto = memberStoreRuleConverter.convertReqToDto(request);
        List<StoreUsageRuleDto> storeUsageRules = storeUsageMedService.listStoreRule(dto);
        return Response.success(storeUsageRules.stream().map(memberStoreRuleConverter::convertDtoToResp).collect(Collectors.toList()));
    }
}
