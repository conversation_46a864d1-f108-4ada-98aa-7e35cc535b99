package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SavePointConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPointConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPointUsageRuleInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.SavePointUsageRuleResultResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberPointRuleDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberPointRuleConverter;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.service.PointConfigMedService;
import com.ly.titc.pms.member.mediator.service.PointUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分可用规则
 *
 * <AUTHOR>
 * @date 2025/6/12 10:57
 */
@Slf4j
@Validated
@DubboService
public class MemberPointRuleDubboServiceImpl implements MemberPointRuleDubboService {

    @Resource
    private MemberPointRuleConverter memberPointRuleConverter;

    @Resource
    private PointUsageMedService pointUsageMedService;

    @Resource
    private PointConfigMedService pointConfigMedService;

    @Resource
    private HotelDecorator hotelDecorator;


    @Override
    public Response<Boolean> setConfig(SavePointConfigReq request) {
        SavePointConfigDto configReq = memberPointRuleConverter.convertReqToReq(request);
        pointConfigMedService.savePointConfig(configReq);
        return Response.success(true);
    }

    @Override
    public Response<MemberPointConfigResp> getConfig(BaseMasterReq req) {
        PointConfigDto pointConfig = pointConfigMedService.getPointConfig(req.getMasterType(), req.getMasterCode());
        MemberPointConfigResp response = memberPointRuleConverter.convertRespToResp(pointConfig);
        return Response.success(response);
    }

    @Override
    public Response<Pageable<MemberPointUsageRuleInfoResp>> pageRule(PagePointUsageRuleReq request) {
        PagePointUsageDto dto = memberPointRuleConverter.convertReqToReq(request);
        Pageable<PointUsageRuleDetailDto> resp = pointUsageMedService.pagePointUsageRule(dto);
        List<HotelBaseInfoResp> respList = hotelDecorator.listHotelBaseInfos(request.getBlocCode(), null);
        Map<Long, HotelBaseInfoResp> hotelRespMap = respList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelVid, Function.identity()));
        return Response.success(PageableUtil.convert(resp, d -> memberPointRuleConverter.convertRespToResponse(d, hotelRespMap)));
    }

    @Override
    public Response<List<SavePointUsageRuleResultResp>> saveRule(SavePointUsageConfigReq request) {
        SavePointUsageRuleDto dto = memberPointRuleConverter.convertReqToDto(request);
        List<SaveUsageRuleResultDto> results =  pointUsageMedService.savePointUsageRule(dto);
        return Response.success(results.stream().map(memberPointRuleConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<Boolean> updateState(UpdatePointUsageStateReq request) {
        UpdatePointUsageStateDto dto = memberPointRuleConverter.convertReqToDto(request);
        Boolean result= pointUsageMedService.updateState(dto);
        return Response.success(result);
    }

    @Override
    public Response<Boolean> deleteRule(DeletePointUsageReq request) {
        DeletePointUsageRuleDto dto = memberPointRuleConverter.convertReqToDto(request);
        pointUsageMedService.deletePointUsageRule(dto);
        return Response.success(true);
    }

    @Override
    public Response<List<SavePointUsageRuleResultResp>> remind(UsageRuleRemindReq request) {
        List<MemberUsageRuleSaveResultDto> results= pointUsageMedService.remind(request.getBlocCode(), request.getMasterType(), request.getMasterCode());
        return Response.success(results.stream().map(memberPointRuleConverter::convertDtoToResponse).collect(Collectors.toList()));
    }
}
