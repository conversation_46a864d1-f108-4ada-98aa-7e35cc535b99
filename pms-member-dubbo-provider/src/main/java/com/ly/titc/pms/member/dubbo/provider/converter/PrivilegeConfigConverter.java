package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.privilege.ActionPrivilegeConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.privilege.DeletePrivilegeConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.privilege.PagePrivilegeConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.privilege.SavePrivilegeConfigReq;
import com.ly.titc.pms.member.dubbo.entity.response.PrivilegeConfigResp;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.*;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/27 19:48
 */
@Mapper(componentModel = "spring")
public interface PrivilegeConfigConverter {
    SavePrivilegeConfigDto convertReqToDto(SavePrivilegeConfigReq request);

    DeletePrivilegeConfigDto convertReqToDto(DeletePrivilegeConfigReq request);

    ActionPrivilegeConfigDto convertReqToDto(ActionPrivilegeConfigReq request);

    PrivilegeConfigResp convertDtoToResp(PrivilegeConfigDto dto);

    PagePrivilegeConfigDto convertReqToDto(PagePrivilegeConfigReq request);
}
