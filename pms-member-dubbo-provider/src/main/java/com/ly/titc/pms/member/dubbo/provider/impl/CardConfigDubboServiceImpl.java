package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.GetByIdReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.*;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.dubbo.interfaces.CardConfigDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.CardConfigConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.PrivilegeConfigDto;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27 11:39
 */
@Slf4j
@Validated
@DubboService
public class CardConfigDubboServiceImpl implements CardConfigDubboService {

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private CardConfigConverter cardConfigConverter;

    @Override
    public Response<Long> saveCardConfig(SaveCardConfigReq request) {
        SaveCardConfigDto dto = cardConfigConverter.convertReqToDto(request);
        Long cardId = cardConfigMedService.saveCardConfig(dto);
        return Response.success(cardId);
    }

    @Override
    public Response<String> deleteCardConfig(DeleteCardConfigReq request) {
        DeleteCardConfigDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.deleteCardConfig(dto);
        return Response.success(null);
    }

    @Override
    public Response<CardConfigResp> getDefaultCard(BaseMasterReq request) {
        CardConfigDto defaultCard = cardConfigMedService.getDefaultCard(request.getMasterType(), request.getMasterCode());
        CardConfigResp cardConfigResp = cardConfigConverter.convertDtoToResp(defaultCard);
        return Response.success(cardConfigResp);
    }

    @Override
    public Response<CardConfigResp> getCardConfig(GetCardConfigReq request) {
        CardConfigDto cardConfig = cardConfigMedService.getCardConfig(request.getMasterType(), request.getMasterCode(), request.getCardId());
        return Response.success(cardConfigConverter.convertDtoToResp(cardConfig));
    }

    @Override
    public Response<List<CardConfigResp>> listCardConfig(ListCardConfigReq request) {
        List<CardConfigDto> cardConfigs = cardConfigMedService.listCardConfig(request.getMasterType(), request.getMasterCode(), request.getCardIds());
        return Response.success(cardConfigs.stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<List<CardConfigResp>> listCardConfigByMaster(BaseMasterReq request) {
        List<CardConfigDto> cardConfigs = cardConfigMedService.listCardConfig(request.getMasterType(), request.getMasterCode());
        return Response.success(cardConfigs.stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<Long> saveCardLevelConfig(SaveCardLevelConfigReq request) {
        SaveCardLevelConfigDto dto = cardConfigConverter.convertReqToDto(request);
        Long cardLevelId = cardConfigMedService.saveCardLevelConfig(dto);
        return Response.success(cardLevelId);
    }

    @Override
    public Response<String> deleteCardLevelConfig(DeleteCardLevelConfigReq request) {
        DeleteCardLevelConfigDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.deleteCardLevelConfig(dto);
        return Response.success(null);
    }

    @Override
    public Response<List<CardLevelConfigResp>> listCardLevelConfig(ListCardLevelConfigReq request) {
        ListCardLevelConfigDto dto = cardConfigConverter.convertReqToDto(request);
        List<CardLevelConfigWithPrivilegeDto> cardLevelConfigs = cardConfigMedService.listCardLevelConfig(dto);
        return Response.success(cardLevelConfigs.stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<Pageable<CardLevelConfigResp>> pageCardLevelConfig(PageCardLevelConfigReq request) {
        PageCardLevelConfigDto dto = cardConfigConverter.convertReqToDto(request);
        Pageable<CardLevelConfigWithPrivilegeDto> pageable = cardConfigMedService.pageCardLevelConfig(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<CardLevelConfigResp> getCardLevelConfig(GetByIdReq request) {
        CardLevelConfigWithPrivilegeDto cardLevelConfig = cardConfigMedService.getCardLevelConfig(request.getMasterType(), request.getMasterCode(), request.getId());
        return Response.success(cardConfigConverter.convertDtoToResp(cardLevelConfig));
    }

    @Override
    public Response<List<PrivilegeConfigResp>> listPrivilegeConfig(ListPrivilegeConfigReq request) {
        List<PrivilegeConfigDto> privilegeConfigs = cardConfigMedService.listCardLevelPrivilege(request.getMasterType(), request.getMasterCode(), request.getCardId(), request.getCardLevel());
        List<PrivilegeConfigResp> privileges = privilegeConfigs.stream().map(item -> cardConfigConverter.convertDtoToResp(item)).collect(Collectors.toList());
        return Response.success(privileges);
    }

    @Override
    public Response<String> actionCardLevel(ActionCardLevelReq request) {
        ActionCardLevelDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.actionCardLevel(dto);
        return Response.success(null);
    }

    @Override
    public Response<CardFullConfigResp> getCardFullConfig(GetCardConfigReq request) {
        CardFullConfigDto cardFullConfig = cardConfigMedService.getCardFullConfig(request.getMasterType(), request.getMasterCode(), request.getCardId());
        return Response.success(cardConfigConverter.convertDtoToResp(cardFullConfig));
    }

    @Override
    public Response<CardLevelUpgradeRuleResp> getUpgradeRule(GetUpgradeRuleReq request) {
        GetCardLevelUpgradeRuleDto dto = cardConfigConverter.convertReqToDto(request);
        CardLevelUpgradeRuleDto cardLevelUpgradeRule = cardConfigMedService.getCardLevelUpgradeRule(dto);
        return Response.success(cardConfigConverter.convertDtoToResp(cardLevelUpgradeRule));
    }

    @Override
    public Response<List<CardLevelUpgradeRuleResp>> listUpgradeRule(ListCardLevelUpgradeRuleReq request) {
        ListCardLevelUpgradeRuleDto dto = cardConfigConverter.convertReqToDto(request);
        List<CardLevelUpgradeRuleDto> cardLevelUpgradeRules = cardConfigMedService.listCardLevelUpgradeRule(dto);
        return Response.success(cardLevelUpgradeRules.stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<Pageable<CardLevelUpgradeRuleResp>> pageUpgradeRule(PageCardLevelUpgradeRuleReq request) {
        PageCardLevelUpgradeRuleDto dto = cardConfigConverter.convertReqToDto(request);
        Pageable<CardLevelUpgradeRuleDto> pageable = cardConfigMedService.pageUpgradeRule(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<CardLevelUpgradeRuleResp> getUpgradeRuleById(GetUpgradeRuleByIdReq request) {
        CardLevelUpgradeRuleDto upgradeRule = cardConfigMedService.getUpgradeRule(request.getMasterType(), request.getMasterCode(), request.getRuleId());
        return Response.success(cardConfigConverter.convertDtoToResp(upgradeRule));
    }

    @Override
    public Response<Long> saveCardLevelUpgradeRule(SaveCardLevelUpgradeRuleReq request) {
        SaveCardLevelUpgradeRuleDto upgradeRule = cardConfigConverter.convertReqToDto(request);
        Long upgradeRuleId = cardConfigMedService.saveCardLevelUpgradeRule(upgradeRule);
        return Response.success(upgradeRuleId);
    }

    @Override
    public Response<Long> saveCardLevelRelegationRule(SaveCardLevelRelegationRuleReq request) {
        SaveCardLevelRelegationRuleDto upgradeRule = cardConfigConverter.convertReqToDto(request);
        Long relegationRuleId = cardConfigMedService.saveCardLevelRelegationRule(upgradeRule);
        return Response.success(relegationRuleId);
    }

    @Override
    public Response<List<CardLevelRelegationRuleResp>> listRelegationRule(ListCardLevelRelegationRuleReq request) {
        ListCardLevelRelegationRuleDto dto = cardConfigConverter.convertReqToDto(request);
        List<CardLevelRelegationRuleDto> cardLevelRelegationRules = cardConfigMedService.listCardLevelRelegationRule(dto);
        return Response.success(cardLevelRelegationRules.stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<Pageable<CardLevelRelegationRuleResp>> pageRelegationRule(PageCardLevelRelegationRuleReq request) {
        PageCardLevelRelegationRuleDto dto = cardConfigConverter.convertReqToDto(request);
        Pageable<CardLevelRelegationRuleDto> pageable = cardConfigMedService.pageRelegationRule(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<CardLevelRelegationRuleResp> getRelegationRule(GetRelegationRuleReq request) {
        GetCardLevelRelegationRuleDto dto = cardConfigConverter.convertReqToDto(request);
        CardLevelRelegationRuleDto relegationRule = cardConfigMedService.getRelegationRule(dto);
        return Response.success(cardConfigConverter.convertDtoToResp(relegationRule));
    }

    @Override
    public Response<CardLevelRelegationRuleResp> getRelegationRuleById(GetRelegationRuleByIdReq request) {
        CardLevelRelegationRuleDto relegationRule = cardConfigMedService.getRelegationRule(request.getMasterType(), request.getMasterCode(), request.getRuleId());
        return Response.success(cardConfigConverter.convertDtoToResp(relegationRule));
    }

    @Override
    public Response<String> deleteUpgradeRule(DeleteUpgradeRuleReq request) {
        DeleteUpgradeRuleDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.deleteUpgradeRule(dto);
        return Response.success(null);
    }

    @Override
    public Response<String> actionUpgradeRule(ActionUpgradeRuleReq request) {
        ActionUpgradeRuleDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.actionUpgradeRule(dto);
        return Response.success(null);
    }

    @Override
    public Response<String> deleteRelegationRule(DeleteRelegationRuleReq request) {
        DeleteRelegationRuleDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.deleteRelegationRule(dto);
        return Response.success(null);
    }

    @Override
    public Response<String> actionRelegationRule(ActionRelegationRuleReq request) {
        ActionRelegationRuleDto dto = cardConfigConverter.convertReqToDto(request);
        cardConfigMedService.actionRelegationRule(dto);
        return Response.success(null);
    }

    @Override
    public Response<List<CardLevelPrivilegeConfigResp>> listCardLevelPrivilege(ListCardLevelPrivilegeReq request) {
        ListCardLevelPrivilegeDto dto = cardConfigConverter.convertReqToDto(request);
        List<CardLevelPrivilegeConfigDto> cardLevelPrivilegeConfigs = cardConfigMedService.listCardLevelPrivilege(dto);
        return Response.success(cardLevelPrivilegeConfigs.stream().map(cardConfigConverter::convertDtoToResp).collect(Collectors.toList()));
    }

}
