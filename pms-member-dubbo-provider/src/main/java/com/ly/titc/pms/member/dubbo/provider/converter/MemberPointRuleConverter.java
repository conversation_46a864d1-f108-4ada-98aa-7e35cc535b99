package com.ly.titc.pms.member.dubbo.provider.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SavePointConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.DeletePointUsageReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.PagePointUsageRuleReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.SavePointUsageConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.UpdatePointUsageStateReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/12 11:24
 */
@Mapper(componentModel = "spring")
public interface MemberPointRuleConverter {

    @Mappings({
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels", ignore = true),
            @Mapping(target = "scopeHotelCodes", source = "scopeHotelCodes", ignore = true),
            @Mapping(target = "usageHotelCodes", source = "usageHotelCodes", ignore = true),

    })
    MemberPointUsageRuleInfoResp convertBase(PointUsageRuleDetailDto resp);


    default MemberPointUsageRuleInfoResp convertRespToResponse(PointUsageRuleDetailDto resp, Map<Long, HotelBaseInfoResp> hotelRespMap) {
        MemberPointUsageRuleInfoResp ruleDto = convertBase(resp);
        List<CodeResp> scopePlatformChannels = new ArrayList<>();
        List<CodeResp> scopeHotelCodes = new ArrayList<>();
        List<CodeResp> usageHotelCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resp.getScopePlatformChannels())) {
            resp.getScopePlatformChannels().forEach(s -> {
                PlatformChannelEnum channelEnum = PlatformChannelEnum.getByPlatformChannel(s);
                if (channelEnum != null) {
                    CodeResp codeDto = new CodeResp();
                    codeDto.setCode(s);
                    codeDto.setName(channelEnum.getPlatformChannelDesc());
                    codeDto.setParentCode(channelEnum.getPlatform());
                    codeDto.setParentName(channelEnum.getPlatformDesc());
                    scopePlatformChannels.add(codeDto);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(resp.getScopeHotelCodes())) {
            resp.getScopeHotelCodes().forEach(s -> {
                CodeResp codeDto = new CodeResp();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                scopeHotelCodes.add(codeDto);
            });
        }
        if (CollectionUtils.isNotEmpty(resp.getUsageHotelCodes())) {
            resp.getUsageHotelCodes().forEach(s -> {
                CodeResp codeDto = new CodeResp();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                usageHotelCodes.add(codeDto);
            });
        }
        ruleDto.setScopePlatformChannels(scopePlatformChannels);
        ruleDto.setScopeHotelCodes(scopeHotelCodes);
        ruleDto.setUsageHotelCodes(usageHotelCodes);
        return ruleDto;
    }

    SavePointUsageRuleDto convertReqToDto(SavePointUsageConfigReq request);

    SavePointUsageRuleResultResp convertDtoToResponse(MemberUsageRuleSaveResultDto resp);

    UpdatePointUsageStateDto convertReqToDto(UpdatePointUsageStateReq request);

    DeletePointUsageRuleDto convertReqToDto(DeletePointUsageReq request);

    PagePointUsageDto convertReqToReq(PagePointUsageRuleReq request);

    SavePointConfigDto convertReqToReq(SavePointConfigReq request);

    MemberPointConfigResp convertRespToResp(PointConfigDto pointConfig);

    SavePointUsageRuleResultResp convertDtoToResp(SaveUsageRuleResultDto dto);
}
