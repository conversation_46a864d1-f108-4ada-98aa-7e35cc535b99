package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.dubbo.entity.request.GetByIdReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.config.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRelatedConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberTagConfigDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberTagConfigResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberConfigDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberConfigConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.config.*;
import com.ly.titc.pms.member.mediator.service.MemberConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27 18:09
 */
@Slf4j
@Validated
@DubboService
public class MemberConfigDubboServiceImpl implements MemberConfigDubboService {

    @Resource
    private MemberConfigMedService memberConfigMedService;

    @Resource
    private MemberConfigConverter memberConfigConverter;

    @Override
    public Response<Long> saveMemberRelatedConfig(SaveMemberRelatedConfigReq request) {
        SaveMemberRelatedConfigDto dto = memberConfigConverter.convertReqToDto(request);
        Long configId = memberConfigMedService.saveMemberRelatedConfig(dto);
        return Response.success(configId);
    }

    @Override
    public Response<MemberRelatedConfigResp> getMemberRelatedConfig(GetMemberRelatedConfigReq request) {
        GetMemberRelatedConfigDto dto = memberConfigConverter.convertReqToDto(request);
        MemberRelatedConfigDto memberRelatedConfig = memberConfigMedService.getMemberRelatedConfig(dto);
        return Response.success(memberConfigConverter.convertDtoToResp(memberRelatedConfig));
    }

    @Override
    public Response<Long> saveTagConfig(SaveMemberTagConfigReq request) {
        SaveMemberTagConfigDto dto = memberConfigConverter.convertReqToDto(request);
        Long tagId = memberConfigMedService.saveTagConfig(dto);
        return Response.success(tagId);
    }

    @Override
    public Response<String> deleteTagConfig(DeleteMemberTagConfigReq request) {
        DeleteMemberTagDto dto = memberConfigConverter.convertReqToDto(request);
        memberConfigMedService.deleteTagConfig(dto);
        return Response.success(null);
    }

    @Override
    public Response<Pageable<MemberTagConfigDetailResp>> pageTagConfig(PageMemberTagConfigReq request) {
        PageMemberTagConfigDto dto = memberConfigConverter.convertReqToDto(request);
        Pageable<MemberTagConfigDetailDto> pageable = memberConfigMedService.pageTagConfig(dto);
        return Response.success(PageableUtil.convert(pageable, pageable.getDatas().stream().map(memberConfigConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<List<MemberTagConfigDetailResp>> listTagConfig(ListMemberTagConfigReq request) {
        ListMemberTagConfigDto dto = memberConfigConverter.convertReqToDto(request);
        List<MemberTagConfigDetailDto> memberTagConfigDetails = memberConfigMedService.listTagConfig(dto);
        List<MemberTagConfigDetailResp> details= memberTagConfigDetails.stream().map(memberConfigConverter::convertDtoToResp).collect(Collectors.toList());
        return Response.success(details);
    }

    @Override
    public Response<MemberTagConfigDetailResp> getTagConfig(GetByIdReq req) {
        MemberTagConfigDetailDto config = memberConfigMedService.getTagConfig(req.getMasterType(), req.getMasterCode(), req.getId());
        return Response.success(memberConfigConverter.convertDtoToResp(config));
    }
}
